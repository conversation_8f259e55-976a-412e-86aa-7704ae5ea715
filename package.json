{"name": "tailwind-nextjs-starter-blog", "version": "2.4.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix --dir app --dir components --dir lib --dir layouts", "migrate": "tsx scripts/migrate.ts up", "migrate:up": "tsx scripts/migrate.ts up", "migrate:status": "tsx scripts/migrate.ts status", "migrate:show": "tsx scripts/migrate.ts show"}, "dependencies": {"@headlessui/react": "2.2.0", "@next/bundle-analyzer": "15.2.4", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.5", "@tailwindcss/typography": "^0.5.15", "@tiptap/extension-code-block-lowlight": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-youtube": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@types/js-beautify": "^1.14.3", "body-scroll-lock": "^4.0.0-beta.0", "date-fns": "^4.1.0", "esbuild": "0.25.2", "github-slugger": "^2.0.0", "gray-matter": "^4.0.2", "highlight.js": "^11.11.1", "html-react-parser": "^5.2.5", "image-size": "2.0.1", "lowlight": "^3.3.0", "lucide-react": "^0.522.0", "next": "15.2.4", "next-themes": "^0.4.6", "pliny": "0.4.1", "postcss": "^8.4.24", "react": "19.0.0", "react-dom": "19.0.0", "react-hot-toast": "^2.5.2", "react-medium-image-zoom": "^5.2.14", "reading-time": "1.5.0", "recharts": "^2.15.4", "remark": "^15.0.0", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "tailwindcss": "^4.0.5", "yet-another-react-lightbox": "^3.23.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@svgr/webpack": "^8.0.1", "@types/mdx": "^2.0.12", "@types/react": "^19.0.8", "@typescript-eslint/eslint-plugin": "^8.12.0", "@typescript-eslint/parser": "^8.12.0", "eslint": "^9.14.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.0", "globals": "^15.12.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.6.11", "tsx": "^4.20.3", "typescript": "^5.1.3"}, "packageManager": "yarn@3.6.1"}