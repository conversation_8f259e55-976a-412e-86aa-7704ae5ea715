# 🐳 Docker Deployment Guide

## Lỗi "The string did not match the expected pattern"

Đ<PERSON><PERSON> là lỗi phổ biến khi deploy Next.js app trong Docker. Nguyên nhân chính thường là:

### 🔍 Nguyên nhân và giải pháp:

1. **Environment Variables không đúng format**
2. **Supabase URL không hợp lệ** 
3. **Docker chạy development mode thay vì production**
4. **Base URL không được cấu hình đúng**

---

## 🚀 Triển khai từ đầu

### Bước 1: Cấu hình Environment
```bash
# Copy file mẫu
cp env.example .env.local

# Chỉnh sửa .env.local với thông tin thực của bạn
nano .env.local
```

**Cấu hình bắt buộc:**
```bash
# Supabase (REQUIRED)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-key-here

# Domain của bạn (REQUIRED)  
NEXT_PUBLIC_BASE_URL=https://yourdomain.com

# Admin account (REQUIRED)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
ADMIN_CONTACT_EMAIL=<EMAIL>
```

### Bước 2: Chạy script debug
```bash
# Script sẽ kiểm tra và tự động sửa các vấn đề
./scripts/docker-debug.sh
```

### Bước 3: Manual build (nếu script không work)
```bash
# Stop containers
docker-compose down

# Build clean
docker-compose build --no-cache

# Start
docker-compose up -d

# Check logs
docker-compose logs -f
```

---

## 🔧 Troubleshooting

### Lỗi "Invalid Supabase URL"
```bash
# URL phải đúng format:
NEXT_PUBLIC_SUPABASE_URL=https://abcdefgh.supabase.co
# ❌ KHÔNG: supabase.co/abcdefgh
# ❌ KHÔNG: http://abcdefgh.supabase.co
```

### Lỗi "Health check failed"
```bash
# Kiểm tra health endpoint
curl http://localhost:3001/api/health

# Nếu không response, check logs:
docker-compose logs tip-trick-guru-blog
```

### Container bị crash
```bash
# Xem logs chi tiết
docker-compose logs --tail=100 tip-trick-guru-blog

# Restart container
docker-compose restart tip-trick-guru-blog
```

### Lỗi build Docker
```bash
# Clear Docker cache
docker system prune -a

# Rebuild từ đầu
docker-compose build --no-cache --force-rm
```

---

## 📋 Checklist triển khai

- [ ] File `.env.local` đã được tạo và cấu hình đúng
- [ ] Supabase URL đúng format `https://xxx.supabase.co`
- [ ] Base URL là domain thực của bạn
- [ ] Admin credentials đã được set
- [ ] Docker containers đang chạy
- [ ] Health check endpoint `/api/health` hoạt động
- [ ] Website accessible tại `http://localhost:3001`

---

## 🆘 Lệnh hữu ích

```bash
# Xem tất cả containers
docker ps -a

# Logs real-time
docker-compose logs -f

# Shell vào container
docker-compose exec tip-trick-guru-blog sh

# Check health
curl http://localhost:3001/api/health

# Restart all
docker-compose restart

# Stop all
docker-compose down

# Clean rebuild
docker-compose down && docker-compose build --no-cache && docker-compose up -d
```

---

## 🎯 Deployment trên VM

### Port mapping
```yaml
# docker-compose.yml
ports:
  - "3001:3000"  # External:Internal
```

### Firewall
```bash
# Mở port 3001
sudo ufw allow 3001

# Check
sudo ufw status
```

### Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

---

## 🔐 Security Notes

- Đổi admin password mặc định
- Sử dụng SSL/HTTPS trong production
- Giới hạn access database
- Regular backup Supabase
- Monitor container logs

---

**Nếu vẫn gặp lỗi, hãy check logs chi tiết:**
```bash
docker-compose logs tip-trick-guru-blog --tail=50
``` 