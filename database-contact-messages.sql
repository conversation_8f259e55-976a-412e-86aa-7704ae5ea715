 -- Contact Messages Database Migration
-- 
-- NOTE: This table and policies already exist in your database!
-- This file is provided for documentation and reference purposes.
-- Running this script should be safe (it uses IF NOT EXISTS checks),
-- but it's not necessary since your contact system is already working.
--
-- If you see errors like "policy already exists", that's normal and expected.

-- Create contact_messages table for storing contact form submissions
CREATE TABLE IF NOT EXISTS public.contact_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied')),
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_messages_status ON public.contact_messages(status);
CREATE INDEX IF NOT EXISTS idx_contact_messages_created_at ON public.contact_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_contact_messages_email ON public.contact_messages(email);

-- Enable Row Level Security (RLS)
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;

-- Create policy to allow public to insert (for form submissions)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'contact_messages' 
        AND policyname = 'Allow public to insert contact messages'
    ) THEN
        CREATE POLICY "Allow public to insert contact messages" ON public.contact_messages
            FOR INSERT 
            WITH CHECK (true);
    END IF;
END $$;

-- Create policy to allow authenticated users to select/update (for admin access)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'contact_messages' 
        AND policyname = 'Allow authenticated users to view and update contact messages'
    ) THEN
        CREATE POLICY "Allow authenticated users to view and update contact messages" ON public.contact_messages
            FOR ALL 
            USING (auth.role() = 'authenticated' OR auth.role() = 'service_role');
    END IF;
END $$;

-- Grant permissions
GRANT INSERT ON public.contact_messages TO anon;
GRANT ALL ON public.contact_messages TO authenticated;
GRANT ALL ON public.contact_messages TO service_role;

-- Comment the table and columns for documentation
COMMENT ON TABLE public.contact_messages IS 'Stores contact form submissions from website visitors';
COMMENT ON COLUMN public.contact_messages.id IS 'Unique identifier for each message';
COMMENT ON COLUMN public.contact_messages.name IS 'Name of the person sending the message';
COMMENT ON COLUMN public.contact_messages.email IS 'Email address of the sender';
COMMENT ON COLUMN public.contact_messages.subject IS 'Subject line of the message';
COMMENT ON COLUMN public.contact_messages.message IS 'The actual message content';
COMMENT ON COLUMN public.contact_messages.status IS 'Message status: new, read, or replied';
COMMENT ON COLUMN public.contact_messages.ip_address IS 'Visitor IP address for rate limiting and analytics';
COMMENT ON COLUMN public.contact_messages.user_agent IS 'Browser user agent for analytics and spam detection';
COMMENT ON COLUMN public.contact_messages.created_at IS 'Timestamp when the message was submitted';

-- Note: This table already exists in your database, so this file is for documentation purposes 