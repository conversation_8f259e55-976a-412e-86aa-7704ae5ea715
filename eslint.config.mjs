import typescriptEslint from '@typescript-eslint/eslint-plugin'
import globals from 'globals'
import tsParser from '@typescript-eslint/parser'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import js from '@eslint/js'
import { FlatCompat } from '@eslint/eslintrc'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const compat = new FlatCompat({
  baseDirectory: __dirname,
})

const eslintConfig = [
  ...compat.config({
    extends: ['next/core-web-vitals', 'prettier'],
    plugins: ['@typescript-eslint'],
    rules: {
      // Suppress warnings for existing codebase - these are acceptable for a blog
      '@typescript-eslint/no-explicit-any': 'warn',
      'jsx-a11y/label-has-associated-control': 'warn',
      'jsx-a11y/alt-text': 'warn', 
      'jsx-a11y/click-events-have-key-events': 'warn',
      'jsx-a11y/no-static-element-interactions': 'warn',
      'react-hooks/exhaustive-deps': 'warn',
      '@next/next/no-img-element': 'warn',
      'no-useless-escape': 'warn',
      'react/no-unescaped-entities': 'warn', // Allow apostrophes in blog content
      
      // Allow unused variables in some cases - this is common in blogs
      'no-unused-vars': ['warn', { 
        'argsIgnorePattern': '^_',
        'varsIgnorePattern': '^_'
      }],
      '@typescript-eslint/no-unused-vars': ['warn', { 
        'argsIgnorePattern': '^_',
        'varsIgnorePattern': '^_'
      }],
      
      // Keep these as warnings for better code quality
      'prefer-const': 'warn',
    },
  }),
]

export default eslintConfig
