// Replacement types for contentlayer (since we're using Supabase)

export interface Blog {
  slug: string
  title: string
  date: string
  tags?: string[]
  summary?: string
  authors?: string[]
  body: {
    code: string
  }
  readingTime?: {
    text: string
  }
}

export interface Authors {
  slug: string
  name: string
  avatar?: string
  occupation?: string
  company?: string
  email?: string
  twitter?: string
  linkedin?: string
  github?: string
  body: {
    code: string
  }
}

// Empty arrays since we're not using contentlayer
export const allBlogs: Blog[] = []
export const allAuthors: <AUTHORS>