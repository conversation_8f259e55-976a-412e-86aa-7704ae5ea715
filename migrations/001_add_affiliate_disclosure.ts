export const affiliateDisclosureMigration = {
  id: '001_add_affiliate_disclosure',
  name: 'Add affiliate disclosure flag to blog posts',
  description: 'Adds show_affiliate_disclosure boolean column to blog_posts table',
  sql: `
    -- Add the show_affiliate_disclosure column to the blog_posts table
    ALTER TABLE blog_posts 
    ADD COLUMN IF NOT EXISTS show_affiliate_disclosure BOOLEAN DEFAULT false;

    -- Update existing posts to have default value (false - no disclosure by default)
    UPDATE blog_posts 
    SET show_affiliate_disclosure = false 
    WHERE show_affiliate_disclosure IS NULL;

    -- Add comment for documentation
    COMMENT ON COLUMN blog_posts.show_affiliate_disclosure 
    IS 'Flag to control whether affiliate disclosure is shown at the end of the post';
  `
}