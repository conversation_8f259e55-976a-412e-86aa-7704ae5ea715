-- =======================================================
-- CATEGORIES AND CATEGORY TAGS TABLES
-- =======================================================
-- Creates tables for category management system

-- Create categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL DEFAULT 'Folder',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create category_tags junction table
CREATE TABLE IF NOT EXISTS public.category_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID NOT NULL REFERENCES public.categories(id) ON DELETE CASCADE,
    tag TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(category_id, tag)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS categories_slug_idx ON public.categories(slug);
CREATE INDEX IF NOT EXISTS category_tags_category_id_idx ON public.category_tags(category_id);
CREATE INDEX IF NOT EXISTS category_tags_tag_idx ON public.category_tags(tag);

-- Enable RLS on categories tables
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.category_tags ENABLE ROW LEVEL SECURITY;

-- Categories policies - Allow read for all, admin for write
DROP POLICY IF EXISTS "categories_select_policy" ON public.categories;
CREATE POLICY "categories_select_policy" ON public.categories
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "categories_admin_policy" ON public.categories;
CREATE POLICY "categories_admin_policy" ON public.categories
    FOR ALL USING (auth.role() = 'service_role');

-- Category tags policies - Allow read for all, admin for write
DROP POLICY IF EXISTS "category_tags_select_policy" ON public.category_tags;
CREATE POLICY "category_tags_select_policy" ON public.category_tags
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "category_tags_admin_policy" ON public.category_tags;
CREATE POLICY "category_tags_admin_policy" ON public.category_tags
    FOR ALL USING (auth.role() = 'service_role');

-- Insert default categories
INSERT INTO public.categories (name, slug, description, icon) VALUES
  ('Frontend Development', 'frontend', 'Client-side development, UI/UX, and modern frameworks', 'Code'),
  ('Backend Development', 'backend', 'Server-side development, APIs, and databases', 'Server'),
  ('DevOps & Cloud', 'devops', 'Deployment, CI/CD, containerization, and cloud services', 'Cloud'),
  ('Tools & Frameworks', 'tools', 'Development tools, frameworks, and productivity', 'Wrench')
ON CONFLICT (slug) DO NOTHING;

-- Insert default category tags
INSERT INTO public.category_tags (category_id, tag) 
SELECT c.id, tag
FROM public.categories c,
UNNEST(CASE 
  WHEN c.slug = 'frontend' THEN ARRAY['react', 'nextjs', 'javascript', 'typescript', 'css', 'html', 'vue', 'angular', 'tailwind', 'frontend']
  WHEN c.slug = 'backend' THEN ARRAY['nodejs', 'api', 'database', 'mongodb', 'postgres', 'express', 'graphql', 'rest', 'python', 'backend']
  WHEN c.slug = 'devops' THEN ARRAY['docker', 'kubernetes', 'aws', 'ci-cd', 'deployment', 'cloud', 'devops']
  WHEN c.slug = 'tools' THEN ARRAY['vscode', 'git', 'webpack', 'vite', 'testing', 'performance', 'productivity', 'tools']
END) AS tag
ON CONFLICT (category_id, tag) DO NOTHING;

-- Grant permissions
GRANT SELECT ON public.categories TO anon;
GRANT SELECT ON public.category_tags TO anon;
GRANT ALL ON public.categories TO authenticated;
GRANT ALL ON public.category_tags TO authenticated;
GRANT ALL ON public.categories TO service_role;
GRANT ALL ON public.category_tags TO service_role;

-- Comments for documentation
COMMENT ON TABLE public.categories IS 'Categories for organizing blog posts and content';
COMMENT ON TABLE public.category_tags IS 'Tags associated with each category for filtering posts';
COMMENT ON COLUMN public.categories.slug IS 'URL-friendly identifier for the category';
COMMENT ON COLUMN public.categories.icon IS 'Icon name from lucide-react icon set';
COMMENT ON COLUMN public.category_tags.tag IS 'Tag name used to filter posts within this category';
