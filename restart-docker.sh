#!/bin/bash

echo "🔄 Restarting Docker containers..."
echo "=================================="

# Stop containers
echo "Stopping containers..."
docker-compose down

# Clean build
echo "Building containers (no cache)..."
docker-compose build --no-cache

# Start containers
echo "Starting containers..."
docker-compose up -d

# Wait a moment
echo "Waiting for containers to initialize..."
sleep 5

# Check status
echo "Checking container status..."
docker-compose ps

# Test health check
echo "Testing health endpoint..."
curl -s http://localhost:3001/api/health | jq . 2>/dev/null || curl -s http://localhost:3001/api/health

echo ""
echo "🚀 Application should be available at: http://localhost:3001"
echo "📋 To check logs: docker-compose logs -f"
echo "🔍 To debug: ./scripts/docker-debug.sh"
