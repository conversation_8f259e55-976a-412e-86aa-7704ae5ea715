#!/bin/bash

echo "🔍 Docker Debug Script for Blog App"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo -e "${RED}❌ .env.local file not found${NC}"
    echo "Creating example .env.local..."
    cat > .env.local << 'EOF'
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Base URL
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Admin Configuration  
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
ADMIN_CONTACT_EMAIL=<EMAIL>

# Email Configuration (Mailtrap)
MAILTRAP_API_KEY=your-mailtrap-api-key
MAILTRAP_FROM_EMAIL=<EMAIL>
MAILTRAP_FROM_NAME="Your Blog"
EOF
    echo -e "${YELLOW}⚠️ Please update .env.local with your actual values${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found .env.local file${NC}"

# Check required environment variables
echo ""
echo "🔧 Checking Environment Variables..."

check_env_var() {
    local var_name=$1
    local var_value=$(grep "^$var_name=" .env.local | cut -d'=' -f2)
    
    if [ -z "$var_value" ] || [ "$var_value" = "your-project.supabase.co" ] || [ "$var_value" = "your-anon-key" ]; then
        echo -e "${RED}❌ $var_name not configured properly${NC}"
        return 1
    else
        echo -e "${GREEN}✅ $var_name configured${NC}"
        return 0
    fi
}

# Check critical variables
MISSING_VARS=0

check_env_var "NEXT_PUBLIC_SUPABASE_URL" || MISSING_VARS=$((MISSING_VARS + 1))
check_env_var "NEXT_PUBLIC_SUPABASE_ANON_KEY" || MISSING_VARS=$((MISSING_VARS + 1))
check_env_var "NEXT_PUBLIC_BASE_URL" || MISSING_VARS=$((MISSING_VARS + 1))

# Validate SUPABASE_URL format
SUPABASE_URL=$(grep "^NEXT_PUBLIC_SUPABASE_URL=" .env.local | cut -d'=' -f2)
if [[ "$SUPABASE_URL" =~ ^https://.*\.supabase\.co$ ]]; then
    echo -e "${GREEN}✅ Supabase URL format is valid${NC}"
else
    echo -e "${RED}❌ Supabase URL format is invalid. Should be: https://your-project.supabase.co${NC}"
    MISSING_VARS=$((MISSING_VARS + 1))
fi

if [ $MISSING_VARS -gt 0 ]; then
    echo ""
    echo -e "${RED}❌ $MISSING_VARS critical environment variables need to be configured${NC}"
    echo -e "${YELLOW}Please update .env.local and run this script again${NC}"
    exit 1
fi

echo ""
echo "🐳 Docker Operations..."

# Stop existing containers
echo "Stopping existing containers..."
docker-compose down

# Rebuild and start
echo "Building and starting containers..."
docker-compose build --no-cache
docker-compose up -d

# Wait for container to be ready
echo "Waiting for container to be ready..."
sleep 10

# Check health
echo "Checking application health..."
HEALTH_CHECK=$(curl -s http://localhost:3001/api/health)
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Health check passed${NC}"
    echo "$HEALTH_CHECK" | jq . 2>/dev/null || echo "$HEALTH_CHECK"
else
    echo -e "${RED}❌ Health check failed${NC}"
    echo "Checking container logs..."
    docker-compose logs --tail=50
    exit 1
fi

echo ""
echo -e "${GREEN}🚀 Application should be running at http://localhost:3001${NC}"
echo ""
echo "📋 Troubleshooting commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Restart: docker-compose restart"
echo "  - Shell access: docker-compose exec tip-trick-guru-blog sh"
echo "  - Check health: curl http://localhost:3001/api/health" 