#!/bin/bash

# =======================================================
# TESTMAIL.APP EMAIL SETUP SCRIPT
# =======================================================

echo "🚀 Setting up email confirmation with testmail.app"
echo "================================================"

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "Creating .env.local file..."
    touch .env.local
fi

echo ""
echo "Please provide your testmail.app credentials:"
echo ""

# Get API key
read -p "Enter your testmail.app API key: " TESTMAIL_API_KEY
if [ -z "$TESTMAIL_API_KEY" ]; then
    echo "❌ API key is required"
    exit 1
fi

# Get namespace
read -p "Enter your testmail.app namespace (default: default): " TESTMAIL_NAMESPACE
TESTMAIL_NAMESPACE=${TESTMAIL_NAMESPACE:-default}

# Get from email
read -p "Enter your 'from' email address (default: <EMAIL>): " TESTMAIL_FROM_EMAIL
TESTMAIL_FROM_EMAIL=${TESTMAIL_FROM_EMAIL:-<EMAIL>}

# Get from name
read -p "Enter your 'from' name (default: Your Blog): " TESTMAIL_FROM_NAME
TESTMAIL_FROM_NAME=${TESTMAIL_FROM_NAME:-"Your Blog"}

# Get base URL
read -p "Enter your base URL (default: http://localhost:3000): " NEXT_PUBLIC_BASE_URL
NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL:-http://localhost:3000}

echo ""
echo "📧 Adding email configuration to .env.local..."

# Check if email config already exists
if grep -q "TESTMAIL_API_KEY" .env.local; then
    echo "⚠️  Email configuration already exists in .env.local"
    echo "Please update manually or remove existing entries."
else
    # Add email configuration
    cat >> .env.local << 'ENVEOF'

# Testmail.app Email Configuration
TESTMAIL_API_KEY=$TESTMAIL_API_KEY
TESTMAIL_NAMESPACE=$TESTMAIL_NAMESPACE
TESTMAIL_FROM_EMAIL=$TESTMAIL_FROM_EMAIL
TESTMAIL_FROM_NAME=$TESTMAIL_FROM_NAME

# Base URL for confirmation links
NEXT_PUBLIC_BASE_URL=$NEXT_PUBLIC_BASE_URL
ENVEOF

    echo "✅ Email configuration added successfully!"
fi

echo ""
echo "📋 Next steps:"
echo "1. Run the database migration (database-newsletter-subscribers.sql) in Supabase"
echo "2. Restart your development server: npm run dev"
echo "3. Test email confirmation by subscribing to newsletter"
echo "4. Check your testmail.app inbox for confirmation emails"
echo ""
echo "📖 For detailed setup instructions, see EMAIL_CONFIRMATION_SETUP.md"
