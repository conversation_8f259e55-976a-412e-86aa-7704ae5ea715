#!/usr/bin/env tsx

import migrations from '../migrations'
import { supabaseAdmin } from '../lib/supabase'

// Simple function to check if we can run migrations automatically
async function canRunAutomatically() {
  if (!supabaseAdmin) {
    console.log('❌ Supabase admin client not configured.')
    console.log('Missing SUPABASE_SERVICE_ROLE_KEY environment variable.')
    return false
  }

  try {
    // Test basic connectivity
    const { data, error } = await supabaseAdmin.from('blog_posts').select('id').limit(1)
    if (error && error.code !== '42P01') {
      console.log('❌ Database connection failed:', error.message)
      return false
    }
    return true
  } catch (error) {
    console.log('❌ Database connection failed:', error)
    return false
  }
}

// Add column directly using ALTER TABLE
async function addAffiliateDisclosureColumn() {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not configured')
  }

  console.log('🔧 Adding show_affiliate_disclosure column...')
  
  try {
    // Try to add the column - this will fail gracefully if it already exists
    const { error } = await supabaseAdmin.rpc('exec_sql', {
      sql: 'ALTER TABLE blog_posts ADD COLUMN IF NOT EXISTS show_affiliate_disclosure BOOLEAN DEFAULT false;'
    })
    
    if (error && !error.message.includes('already exists')) {
      throw error
    }
    
    console.log('✅ Column added successfully')
    return true
  } catch (error: any) {
    // If RPC doesn't work, we'll need to do this manually
    if (error.message?.includes('exec_sql') || error.code === 'PGRST202') {
      console.log('❌ Direct SQL execution not available in this Supabase setup.')
      return false
    }
    throw error
  }
}

// Initialize migrations table
async function initMigrationsTable() {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not configured')
  }

  try {
    console.log('🔧 Initializing migrations table...')
    
    // Try to query migrations table first
    const { error: queryError } = await supabaseAdmin.from('migrations').select('id').limit(1)
    
    if (queryError && queryError.code === '42P01') {
      // Table doesn't exist, create it
      const { error } = await supabaseAdmin.rpc('exec_sql', {
        sql: `CREATE TABLE IF NOT EXISTS migrations (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )`
      })
      
      if (error) {
        throw error
      }
    }
    
    console.log('✅ Migrations table ready')
  } catch (error) {
    console.error('❌ Error creating migrations table:', error)
    throw error
  }
}

// Check if migration has been executed
async function isMigrationExecuted(migrationId: string): Promise<boolean> {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not configured')
  }

  const { data, error } = await supabaseAdmin
    .from('migrations')
    .select('id')
    .eq('id', migrationId)
    .single()

  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    throw error
  }

  return !!data
}

// Record migration as executed
async function recordMigration(migration: any) {
  if (!supabaseAdmin) {
    throw new Error('Supabase admin client not configured')
  }

  const { error } = await supabaseAdmin
    .from('migrations')
    .insert({
      id: migration.id,
      name: migration.name,
      executed_at: new Date().toISOString()
    })

  if (error) {
    throw error
  }
}

// Execute a single migration
async function executeMigration(migration: any) {
  console.log(`🚀 Executing migration: ${migration.name}`)
  
  try {
    // For the affiliate disclosure migration, use the specific function
    if (migration.id === '001_add_affiliate_disclosure') {
      const success = await addAffiliateDisclosureColumn()
      if (!success) {
        throw new Error('Failed to add affiliate disclosure column')
      }
    } else {
      // For other migrations, we'd need to implement specific handlers
      console.log('⚠️  This migration requires manual execution')
      throw new Error('Automatic execution not available for this migration')
    }
    
    // Record as executed
    await recordMigration(migration)
    console.log(`✅ Migration completed: ${migration.name}`)
    
  } catch (error) {
    console.error(`❌ Migration failed: ${migration.name}`, error)
    throw error
  }
}

// Run all pending migrations
async function runMigrations() {
  const canRun = await canRunAutomatically()
  
  if (!canRun) {
    console.log('\n❌ Automatic migration not available.')
    console.log('Please run: npm run migrate:show')
    console.log('Then copy and paste the SQL into your Supabase SQL Editor.')
    return
  }
  
  try {
    await initMigrationsTable()
    
    console.log('🔍 Checking migration status...')
    
    for (const migration of migrations) {
      const executed = await isMigrationExecuted(migration.id)
      
      if (executed) {
        console.log(`⏭️  Skipping already executed migration: ${migration.name}`)
        continue
      }
      
      await executeMigration(migration)
    }
    
    console.log('🎉 All migrations completed successfully!')
    
  } catch (error) {
    console.error('💥 Migration process failed:', error)
    console.log('\n🛠️  Fallback: Run manual migration')
    console.log('npm run migrate:show')
    process.exit(1)
  }
}

// Show migration status
async function showStatus() {
  try {
    await initMigrationsTable()
    
    console.log('\n📊 Migration Status:')
    console.log('='.repeat(50))
    
    const executedIds = new Set()
    
    try {
      const { data } = await supabaseAdmin!.from('migrations').select('id, executed_at')
      data?.forEach(m => {
        executedIds.add(m.id)
        console.log(`✅ ${m.id} (executed: ${new Date(m.executed_at).toLocaleString()})`)
      })
    } catch (error) {
      console.log('No migrations executed yet.')
    }
    
    console.log('\n⏳ Pending migrations:')
    const pending = migrations.filter(m => !executedIds.has(m.id))
    if (pending.length === 0) {
      console.log('  (none)')
    } else {
      pending.forEach(m => {
        console.log(`  - ${m.id}: ${m.name}`)
      })
    }
    
  } catch (error) {
    console.error('Error checking status:', error)
  }
}

function showSQL() {
  console.log('\n' + '='.repeat(60))
  console.log('DATABASE MIGRATION SQL (Manual Option)')
  console.log('Copy and paste this into your Supabase SQL Editor')
  console.log('='.repeat(60) + '\n')
  
  console.log('-- Step 1: Create migrations table')
  console.log(`CREATE TABLE IF NOT EXISTS migrations (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`)
  console.log()
  
  migrations.forEach((migration, index) => {
    console.log(`-- Step ${index + 2}: ${migration.name}`)
    console.log(`-- ID: ${migration.id}`)
    if (migration.description) {
      console.log(`-- Description: ${migration.description}`)
    }
    console.log()
    console.log(migration.sql)
    console.log()
    console.log(`-- Record this migration as executed`)
    console.log(`INSERT INTO migrations (id, name) VALUES ('${migration.id}', '${migration.name}');`)
    console.log()
    console.log('-'.repeat(50))
    console.log()
  })
  
  console.log('After running the above SQL, your migrations will be complete!')
  console.log('='.repeat(60) + '\n')
}

async function main() {
  const command = process.argv[2]

  switch (command) {
    case 'up':
    case 'run':
      await runMigrations()
      break
      
    case 'status':
      await showStatus()
      break
      
    case 'show':
    case 'sql':
      showSQL()
      break

    default:
      console.log(`
Usage: npm run migrate <command>

Commands:
  up       - Run all pending migrations automatically
  run      - Same as up
  status   - Show migration status
  show     - Show SQL to run manually in Supabase
  sql      - Same as show

Examples:
  npm run migrate up     (Recommended - runs automatically)
  npm run migrate status
  npm run migrate show
      `)
      process.exit(1)
  }
}

main().catch(console.error)