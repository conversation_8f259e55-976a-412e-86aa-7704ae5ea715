# Mailtrap Email Service Setup Guide

This guide explains how to set up email confirmation for newsletter subscriptions using Mailtrap.

## Why Mailtrap?

- ✅ **No Account Activation**: Works immediately after signup  
- ✅ **Testing-First**: Perfect for development with email preview
- ✅ **Production Ready**: Seamless transition to production sending
- ✅ **Free Tier**: 1,000 emails/month for free
- ✅ **Excellent Deliverability**: Professional email infrastructure

## Setup Steps

### 1. Create Mailtrap Account

1. Go to [mailtrap.io](https://mailtrap.io)
2. Sign up for a free account  
3. Verify your email address
4. **No activation wait time!** - Account works immediately

### 2. Add and Verify Your Domain

1. Go to **Sending Domains** tab
2. Click **Add Domain**
3. Enter your domain (e.g., `yourdomain.com`)
4. Add the provided DNS records to your domain
5. Wait for domain verification (required for sending emails)

### 3. Get API Token (IMPORTANT!)

**The 401 error happens when using the wrong token type. Here's how to get the correct one:**

1. In **Sending Domains**, select your verified domain
2. Open the **Integration** tab  
3. Click **Integrate** under **Transactional Stream**
4. Toggle switch to **API** (not SMTP)
5. Copy the **API Token** (this is domain-specific)
6. Note the **Mailtrap Host** (should be `send.api.mailtrap.io`)

**Critical**: You need a **domain-specific API token** from the Integration tab, NOT a general API token from Settings → API Tokens!

### 4. Update Environment Variables

```bash
# Remove old email service config and add:
MAILTRAP_API_KEY=your_domain_specific_api_token_here
MAILTRAP_FROM_EMAIL=<EMAIL>  
MAILTRAP_FROM_NAME="PaaS Blog"

# Keep existing:
NEXT_PUBLIC_BASE_URL=https://eeekyc.live
```

### 5. Restart Development Server

```bash
npm run dev
```

## Troubleshooting 401 Error

### Most Common Cause: Wrong Token Type
The 401 error typically means you're using a general API token instead of a domain-specific one.

**Correct Token**: Sending Domains → [Your Domain] → Integration → API → API Token
**Wrong Token**: Settings → API Tokens

### Quick Fix Steps:
1. Go to **Sending Domains**
2. Select your domain  
3. **Integration** tab → **Integrate** → **API**
4. Copy the API Token shown there
5. Update `.env.local` with this token
6. Restart server

## Current Status

✅ Newsletter subscription system working  
�� **Next Step**: Get correct domain-specific API token
🚀 Works immediately once configured correctly!
