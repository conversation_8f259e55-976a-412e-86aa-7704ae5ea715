# Copy this file to .env.local and update with your actual values

# =============================================================================
# REQUIRED: Supabase Configuration
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_STORAGE_BUCKET=blog-assets

# =============================================================================
# REQUIRED: Base URL (Your domain)
# =============================================================================
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# =============================================================================
# REQUIRED: Admin Authentication
# =============================================================================
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
ADMIN_CONTACT_EMAIL=<EMAIL>

# =============================================================================
# OPTIONAL: Email Configuration (Mailtrap)
# =============================================================================
MAILTRAP_API_KEY=your-mailtrap-api-key
MAILTRAP_FROM_EMAIL=<EMAIL>
MAILTRAP_FROM_NAME="Your Blog"

# =============================================================================
# OPTIONAL: Analytics & Tracking
# =============================================================================
# NEXT_PUBLIC_GOOGLE_ANALYTICS=G-XXXXXXXXXX
# NEXT_PUBLIC_UMAMI_WEBSITE_ID=your-umami-id

# =============================================================================
# OPTIONAL: Build Configuration
# =============================================================================
# NODE_ENV=production
# NEXT_TELEMETRY_DISABLED=1
# BASE_PATH=
# UNOPTIMIZED=false

# =============================================================================
# IMPORTANT NOTES:
# =============================================================================
# 1. Never commit .env.local to git (it's in .gitignore)
# 2. Supabase URL must be in format: https://your-project.supabase.co
# 3. BASE_URL should be your actual domain (e.g., https://yourblog.com)
# 4. Use strong password for admin account
# 5. Mailtrap config is optional but needed for contact form emails 