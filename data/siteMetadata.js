/** @type {import("pliny/config").PlinyConfig } */
const siteMetadata = {
  title: 'Tip & Trick Guru - Expert Tips, Reviews & Buying Guides',
  author: 'Tip & Trick Guru',
  headerTitle: 'Tip & Trick Guru',
  description: 'Discover expert tips, in-depth reviews, and comprehensive buying guides. Get actionable insights, step-by-step tutorials, and honest product recommendations to make smarter decisions.',
  language: 'en-us',
  theme: 'system', // system, dark or light
  siteUrl: 'https://your-domain.com',
  siteRepo: 'https://github.com/yourusername/your-blog',
  siteLogo: '/static/images/logo.png',
  socialBanner: '/static/images/twitter-card.png',
  email: '<EMAIL>',
  github: 'https://github.com/yourusername',
  twitter: 'https://twitter.com/yourusername',
  linkedin: 'https://www.linkedin.com/in/yourusername',
  locale: 'en-US',
  
  // SEO & Monetization focused additions
  keywords: [
    'tips and tricks',
    'product reviews',
    'buying guides',
    'tutorials',
    'recommendations',
    'how-to guides',
    'best practices',
    'expert advice'
  ],
  
  // Monetization platforms
  amazonAssociateId: 'your-amazon-associate-id',
  affiliate: {
    amazon: 'your-amazon-associate-id',
    disclaimer: 'This site contains affiliate links. We may earn a commission if you purchase through these links at no additional cost to you.'
  },
  analytics: {
    umamiAnalytics: {
      umamiWebsiteId: '',
    },
  },
  newsletter: {
    // supports mailchimp, buttondown, convertkit, klaviyo, revue, emailoctopus
    provider: '',
  },
  comments: {
    provider: 'giscus', // supported providers: giscus, utterances, disqus
    giscusConfig: {
      repo: process.env.NEXT_PUBLIC_GISCUS_REPO,
      repositoryId: process.env.NEXT_PUBLIC_GISCUS_REPOSITORY_ID,
      category: process.env.NEXT_PUBLIC_GISCUS_CATEGORY,
      categoryId: process.env.NEXT_PUBLIC_GISCUS_CATEGORY_ID,
    },
  },
  search: {
    provider: 'kbar', // kbar or algolia
    kbarConfig: {
      searchDocumentsPath: 'search.json', // path to load documents to search
    },
  },
}

module.exports = siteMetadata 