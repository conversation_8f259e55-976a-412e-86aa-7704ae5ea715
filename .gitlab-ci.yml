# GitLab CI/CD Pipeline Configuration
# This pipeline builds and pushes Docker images to GitLab Container Registry when tags are created

stages:
  - build
  - deploy

variables:
  # Use the official Docker image with Docker-in-Docker support
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  # GitLab Container Registry
  REGISTRY_IMAGE: $CI_REGISTRY_IMAGE
  # Node.js version for any Node-based jobs
  NODE_VERSION: "18"

# Build and push Docker image only when a tag is created
build-and-push:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: /certs/client
  before_script:
    # Wait for docker daemon to be ready
    - until docker info; do sleep 1; done
    # Login to GitLab Container Registry
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    # Build the Docker image with the tag name
    - docker build -t $REGISTRY_IMAGE:$CI_COMMIT_TAG .
    # Push the tagged image to the registry
    - docker push $REGISTRY_IMAGE:$CI_COMMIT_TAG
  after_script:
    # Logout from registry
    - docker logout $CI_REGISTRY
  only:
    # Only run this job when a tag is pushed
    - tags

# Optional: Deploy job (example for reference - customize based on your deployment strategy)
deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - echo "Deploying version $CI_COMMIT_TAG"
    - echo "Image available at $REGISTRY_IMAGE:$CI_COMMIT_TAG"
    # Add your deployment commands here
    # Example: kubectl set image deployment/blog blog=$REGISTRY_IMAGE:$CI_COMMIT_TAG
    # Example: docker-compose pull && docker-compose up -d
  only:
    - tags
  when: manual  # Make deployment manual for safety
