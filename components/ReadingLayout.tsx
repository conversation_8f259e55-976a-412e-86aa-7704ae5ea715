'use client'

import { ReactNode } from 'react'
import { Clock, Calendar, User, BookO<PERSON>, Eye } from 'lucide-react'

interface ReadingLayoutProps {
  children: ReactNode
  title: string
  date: string
  readingTime?: number
  author?: string
  viewCount?: number
  tags?: string[]
}

export default function ReadingLayout({
  children,
  title,
  date,
  readingTime,
  author,
  viewCount,
  tags,
}: ReadingLayoutProps) {
  return (
    <article className="relative">
      {/* Enhanced Reading Progress Bar */}
      <div className="fixed top-0 left-0 z-50 h-1 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 ease-out reading-progress"></div>

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Beautiful Article Header */}
        <header className="mb-16 text-center">
          <div className="mb-8">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold leading-tight mb-8 bg-gradient-to-r from-gray-900 via-blue-600 to-gray-900 dark:from-white dark:via-blue-400 dark:to-white bg-clip-text text-transparent">
              {title}
            </h1>
          </div>

          {/* Article Meta */}
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-8">
            {date && (
              <div className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                <Calendar className="w-4 h-4" />
                <time dateTime={date}>
                  {new Date(date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </time>
              </div>
            )}

            {readingTime && (
              <div className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                <Clock className="w-4 h-4" />
                <span>{readingTime} min read</span>
              </div>
            )}

            {author && (
              <div className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                <User className="w-4 h-4" />
                <span>{author}</span>
              </div>
            )}

            {viewCount && (
              <div className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                <Eye className="w-4 h-4" />
                <span>{viewCount.toLocaleString()} views</span>
              </div>
            )}
          </div>

          {/* Tags */}
          {tags && tags.length > 0 && (
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              {tags.map((tag, index) => (
                <span
                  key={tag}
                  className="px-4 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-800/40 transition-colors cursor-pointer"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </header>

        {/* Enhanced Reading Content */}
        <div className="prose-reading-container">
          <div className="relative">
            {/* Reading Position Indicator */}
            <div className="hidden lg:block fixed left-8 top-1/2 -translate-y-1/2 z-40">
              <div className="w-1 h-32 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div className="w-full bg-gradient-to-b from-blue-500 to-purple-600 rounded-full reading-indicator transition-all duration-300 ease-out"></div>
              </div>
            </div>

            {/* Main Content with Enhanced Typography */}
            <div className="prose-reading-container prose-content prose-reading max-w-none">
              {children}
            </div>

            {/* Reading Enhancement Features */}
            <div className="fixed bottom-8 right-8 z-40 flex flex-col space-y-3">
              {/* Font Size Controls */}
              <div className="bg-white dark:bg-gray-800 rounded-full p-2 shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="flex space-x-2">
                  <button
                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    onClick={() => adjustFontSize('decrease')}
                    title="Decrease font size"
                  >
                    <span className="text-sm font-bold">A-</span>
                  </button>
                  <button
                    className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    onClick={() => adjustFontSize('increase')}
                    title="Increase font size"
                  >
                    <span className="text-lg font-bold">A+</span>
                  </button>
                </div>
              </div>

              {/* Reading Mode Toggle */}
              <button
                className="p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                onClick={() => toggleReadingMode()}
                title="Toggle reading mode"
              >
                <BookOpen className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .prose-reading {
          font-size: var(--reading-font-size, 1.125rem);
          line-height: var(--reading-line-height, 1.8);
          transition: font-size 0.3s ease, line-height 0.3s ease;
        }

        .reading-mode .prose-reading {
          font-size: 1.25rem;
          line-height: 1.9;
          max-width: 65ch;
          margin: 0 auto;
        }

        .reading-mode {
          background: #fefefe;
          color: #333;
        }

        .dark .reading-mode {
          background: #1a1a1a;
          color: #e5e7eb;
        }
      `}</style>
    </article>
  )
}

// Helper functions for reading enhancements
function adjustFontSize(action: 'increase' | 'decrease') {
  const root = document.documentElement
  const currentSize = getComputedStyle(root).getPropertyValue('--reading-font-size') || '1.125rem'
  const currentValue = parseFloat(currentSize)
  
  let newSize: number
  if (action === 'increase') {
    newSize = Math.min(currentValue + 0.125, 1.5) // Max 1.5rem
  } else {
    newSize = Math.max(currentValue - 0.125, 0.875) // Min 0.875rem
  }
  
  root.style.setProperty('--reading-font-size', `${newSize}rem`)
  
  // Adjust line height proportionally
  const lineHeight = newSize * 1.6
  root.style.setProperty('--reading-line-height', lineHeight.toString())
}

function toggleReadingMode() {
  document.body.classList.toggle('reading-mode')
}
