import Head from 'next/head'
import siteMetadata from '@/data/siteMetadata'
import { StructuredData } from './StructuredData'

interface SEOOptimizedProps {
  title: string
  description: string
  url?: string
  image?: string
  author?: string
  publishedDate?: string
  modifiedDate?: string
  tags?: string[]
  type?: 'website' | 'article' | 'product'
  noindex?: boolean
  canonical?: string
  breadcrumbs?: Array<{ name: string; url: string }>
  faqSchema?: Array<{ question: string; answer: string }>
  readingTime?: number
  wordCount?: number
}

export function SEOOptimized({
  title,
  description,
  url,
  image,
  author,
  publishedDate,
  modifiedDate,
  tags,
  type = 'website',
  noindex = false,
  canonical,
  breadcrumbs,
  faqSchema,
  readingTime,
  wordCount,
}: SEOOptimizedProps) {
  const pageUrl = url || siteMetadata.siteUrl
  const pageImage = image || `${siteMetadata.siteUrl}${siteMetadata.socialBanner}`
  const pageAuthor = author || siteMetadata.author
  const canonicalUrl = canonical || pageUrl

  // Enhanced title for better CTR
  const enhancedTitle =
    type === 'article'
      ? `${title} | ${siteMetadata.title}`
      : title === siteMetadata.title
        ? title
        : `${title} | ${siteMetadata.title}`

  // Meta description with optimal length (150-160 characters)
  const optimizedDescription = description.length > 160 
    ? description.substring(0, 157) + '...' 
    : description

  return (
    <>
      <Head>
        {/* Basic Meta Tags */}
        <title>{enhancedTitle}</title>
        <meta name="description" content={optimizedDescription} />
        <meta name="author" content={pageAuthor} />

        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />

        {/* Robots with advanced directives */}
        {noindex ? (
          <meta name="robots" content="noindex, nofollow" />
        ) : (
          <meta
            name="robots"
            content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
          />
        )}

        {/* Keywords for better targeting */}
        {tags && tags.length > 0 && <meta name="keywords" content={tags.join(', ')} />}

        {/* Open Graph / Facebook */}
        <meta property="og:type" content={type === 'article' ? 'article' : 'website'} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={optimizedDescription} />
        <meta property="og:url" content={pageUrl} />
        <meta property="og:image" content={pageImage} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:image:alt" content={title} />
        <meta property="og:site_name" content={siteMetadata.title} />
        <meta property="og:locale" content="en_US" />

        {/* Article specific OG tags */}
        {type === 'article' && (
          <>
            <meta property="article:author" content={pageAuthor} />
            {publishedDate && <meta property="article:published_time" content={publishedDate} />}
            {modifiedDate && <meta property="article:modified_time" content={modifiedDate} />}
            {tags && tags.map((tag) => <meta key={tag} property="article:tag" content={tag} />)}
            {readingTime && <meta property="article:reading_time" content={`${readingTime} minutes`} />}
          </>
        )}

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={optimizedDescription} />
        <meta name="twitter:image" content={pageImage} />
        <meta name="twitter:image:alt" content={title} />
        <meta name="twitter:creator" content={siteMetadata.twitter} />
        <meta name="twitter:site" content={siteMetadata.twitter} />

        {/* Pinterest */}
        <meta property="pinterest:description" content={optimizedDescription} />
        <meta property="pinterest:image" content={pageImage} />

        {/* Additional SEO tags */}
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />
        
        {/* Mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="format-detection" content="telephone=no" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Verification meta tags (uncomment and add your codes) */}
        {/* <meta name="google-site-verification" content="your-verification-code" /> */}
        {/* <meta name="msvalidate.01" content="your-verification-code" /> */}
        {/* <meta name="p:domain_verify" content="your-verification-code" /> */}
      </Head>

      {/* Structured Data */}
      {type === 'website' && <StructuredData type="website" />}

      {type === 'article' && (
        <StructuredData
          type="article"
          data={{
            title,
            description: optimizedDescription,
            url: pageUrl,
            image: pageImage,
            author: pageAuthor,
            datePublished: publishedDate,
            dateModified: modifiedDate,
            tags,
            wordCount,
            readingTime,
          }}
        />
      )}

      {/* Breadcrumb Schema */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <StructuredData type="breadcrumb" data={{ breadcrumbs }} />
      )}

      {/* FAQ Schema for better SERP features */}
      {faqSchema && faqSchema.length > 0 && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'FAQPage',
              mainEntity: faqSchema.map((faq) => ({
                '@type': 'Question',
                name: faq.question,
                acceptedAnswer: {
                  '@type': 'Answer',
                  text: faq.answer,
                },
              })),
            }),
          }}
        />
      )}
    </>
  )
} 