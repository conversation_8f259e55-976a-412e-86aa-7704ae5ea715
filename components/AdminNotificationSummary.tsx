'use client'

import React from 'react'
import { MessageCircle, Mail, Users, TrendingUp, AlertCircle, CheckCircle } from 'lucide-react'
import { useAdminNotifications } from './hooks/useAdminNotifications'

export default function AdminNotificationSummary() {
  const { counts, loading } = useAdminNotifications()

  if (loading) {
    return (
      <div className="card-modern backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 border border-gray-200/50 dark:border-gray-600/50">
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-gray-100 dark:bg-gray-700 rounded-xl h-20"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const stats = [
    {
      title: 'Pending Comments',
      value: counts.pendingComments,
      icon: MessageCircle,
      color: 'from-blue-500 to-indigo-600',
      bgColor: 'from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20',
      borderColor: 'border-blue-200 dark:border-blue-700',
      textColor: 'text-blue-700 dark:text-blue-300',
      description: 'Comments awaiting moderation',
    },
    {
      title: 'New Contacts',
      value: counts.newContacts,
      icon: Mail,
      color: 'from-emerald-500 to-green-600',
      bgColor: 'from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20',
      borderColor: 'border-emerald-200 dark:border-emerald-700',
      textColor: 'text-emerald-700 dark:text-emerald-300',
      description: 'Unread contact messages',
    },
    {
      title: 'New Today',
      value: counts.newComments,
      icon: Users,
      color: 'from-purple-500 to-violet-600',
      bgColor: 'from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20',
      borderColor: 'border-purple-200 dark:border-purple-700',
      textColor: 'text-purple-700 dark:text-purple-300',
      description: 'Comments from last 24h',
    },
    {
      title: 'Total Activity',
      value: counts.totalNotifications,
      icon: TrendingUp,
      color: 'from-orange-500 to-red-600',
      bgColor: 'from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20',
      borderColor: 'border-orange-200 dark:border-orange-700',
      textColor: 'text-orange-700 dark:text-orange-300',
      description: 'Items requiring attention',
    },
  ]

  const hasAlerts = counts.pendingComments > 0 || counts.newContacts > 0

  return (
    <div className="card-modern backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 border border-gray-200/50 dark:border-gray-600/50">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold mb-1">
              <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                Dashboard Overview
              </span>
            </h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Real-time activity and notifications
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {hasAlerts ? (
              <div className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-700 rounded-full">
                <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                <span className="text-xs font-medium text-red-700 dark:text-red-300">
                  Action Required
                </span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 px-3 py-1 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 border border-emerald-200 dark:border-emerald-700 rounded-full">
                <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                <span className="text-xs font-medium text-emerald-700 dark:text-emerald-300">
                  All Clear
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            const hasNotification = (stat.title === 'Pending Comments' && stat.value > 0) || 
                                  (stat.title === 'New Contacts' && stat.value > 0)
            
            return (
              <div
                key={stat.title}
                className={`relative p-4 rounded-xl bg-gradient-to-br ${stat.bgColor} border ${stat.borderColor} hover:shadow-lg transition-all duration-200 group`}
              >
                {/* Notification badge */}
                {hasNotification && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-sm border-2 border-white dark:border-gray-800"></div>
                )}
                
                <div className="flex items-center justify-between mb-3">
                  <div className={`h-10 w-10 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${stat.textColor}`}>
                      {stat.value}
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className={`font-semibold ${stat.textColor} mb-1`}>
                    {stat.title}
                  </h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {stat.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        {/* Quick Actions */}
        {hasAlerts && (
          <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/10 dark:to-orange-900/10 border border-yellow-200 dark:border-yellow-700 rounded-xl">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Attention Required
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
                  You have {counts.pendingComments > 0 && `${counts.pendingComments} pending comment${counts.pendingComments === 1 ? '' : 's'}`}
                  {counts.pendingComments > 0 && counts.newContacts > 0 && ' and '}
                  {counts.newContacts > 0 && `${counts.newContacts} new contact message${counts.newContacts === 1 ? '' : 's'}`} 
                  {' '}awaiting your review.
                </p>
                <div className="flex flex-wrap gap-2">
                  {counts.pendingComments > 0 && (
                    <button
                      onClick={() => {
                        const url = new URL(window.location.href)
                        url.searchParams.set('tab', 'comments')
                        window.location.href = url.toString()
                      }}
                      className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs font-medium rounded-lg hover:shadow-md transition-all duration-200 transform hover:scale-105"
                    >
                      <MessageCircle className="w-3 h-3 mr-1" />
                      Review Comments
                    </button>
                  )}
                  {counts.newContacts > 0 && (
                    <button
                      onClick={() => {
                        const url = new URL(window.location.href)
                        url.searchParams.set('tab', 'contact')
                        window.location.href = url.toString()
                      }}
                      className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white text-xs font-medium rounded-lg hover:shadow-md transition-all duration-200 transform hover:scale-105"
                    >
                      <Mail className="w-3 h-3 mr-1" />
                      Check Messages
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
