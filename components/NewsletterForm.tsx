'use client'

import React, { useState } from 'react'
import { Mail, Send, Check, X, <PERSON><PERSON><PERSON>, User } from 'lucide-react'

interface NewsletterFormProps {
  className?: string
  source?: string
  title?: string
  description?: string
  placeholder?: string
  compact?: boolean
}

export default function NewsletterForm({
  className = '',
  source = 'website',
  title = 'Subscribe to our Newsletter',
  description = 'Get the latest posts delivered right to your inbox',
  placeholder = 'Enter your email address',
  compact = false,
}: NewsletterFormProps) {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, source }),
      })

      const data = await response.json()

      if (data.success) {
        setIsSubmitted(true)
        setEmail('')
      } else {
        setError(data.error || 'Failed to subscribe. Please try again.')
      }
    } catch (err) {
      setError('Failed to subscribe. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setIsSubmitted(false)
    setError(null)
    setEmail('')
  }

  if (isSubmitted) {
    return (
      <div className={`newsletter-form animate-fade-in-scale ${className}`}>
        <div className="card-gradient border border-green-200/50 p-8 text-center dark:border-green-800/50">
          <div className="mb-4 flex justify-center">
            <div className="relative">
              <div className="shadow-strong flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-green-400 to-emerald-500">
                <Check className="h-8 w-8 text-white" />
              </div>
              <div className="bg-gradient-accent absolute -top-1 -right-1 h-4 w-4 animate-ping rounded-full" />
              <Sparkles className="absolute -top-2 -right-2 h-6 w-6 animate-pulse text-yellow-400" />
            </div>
          </div>
          <h3 className="mb-3 text-xl font-bold text-green-800 dark:text-green-200">
            🎉 You're All Set!
          </h3>
          <p className="mb-6 leading-relaxed text-green-700 dark:text-green-300">
            Welcome to our community! You'll receive amazing content directly in your inbox.
          </p>
          <button
            onClick={resetForm}
            className="inline-flex items-center text-sm font-medium text-green-600 transition-colors duration-200 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
          >
            <User className="mr-2 h-4 w-4" />
            Subscribe another email
          </button>
        </div>
      </div>
    )
  }

  if (compact) {
    return (
      <div className={`newsletter-form animate-fade-in-up ${className}`}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex flex-col gap-3 sm:flex-row">
            <div className="relative flex-1">
              <Mail className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 text-gray-400" />
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={placeholder}
                required
                className="shadow-soft focus:border-primary-500 focus:ring-primary-500/20 w-full rounded-xl border border-gray-200 bg-white/50 py-3 pr-4 pl-12 text-gray-900 placeholder-gray-500 backdrop-blur-sm transition-all duration-300 focus:ring-2 dark:border-gray-600 dark:bg-gray-800/50 dark:text-white dark:placeholder-gray-400"
              />
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="group bg-gradient-primary shadow-medium hover:shadow-strong focus:ring-primary-500/50 inline-flex items-center justify-center gap-2 rounded-xl px-6 py-3 font-semibold text-white transition-all duration-300 hover:scale-105 focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <Send className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-0.5" />
              <span>{isSubmitting ? 'Subscribing...' : 'Subscribe'}</span>
            </button>
          </div>
          {error && (
            <div className="animate-fade-in-up flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-600 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400">
              <X className="h-4 w-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}
        </form>
      </div>
    )
  }

  return (
    <div className={`newsletter-form animate-fade-in-up ${className}`}>
      <div className="card-gradient border-primary-200/30 dark:border-primary-700/30 border p-8 lg:p-10">
        {/* Header */}
        {(title || description) && (
          <div className="mb-8 text-center">
            <div className="mb-4 flex justify-center">
              <div className="relative">
                <div className="bg-gradient-accent shadow-medium flex h-14 w-14 items-center justify-center rounded-full">
                  <Mail className="h-7 w-7 text-white" />
                </div>
                <div className="bg-gradient-warm absolute -top-1 -right-1 h-4 w-4 animate-pulse rounded-full" />
              </div>
            </div>
            {title && (
              <h3 className="mb-3 text-2xl font-bold text-gray-900 dark:text-white">{title}</h3>
            )}
            {description && (
              <p className="leading-relaxed text-gray-600 dark:text-gray-300">{description}</p>
            )}
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="relative">
            <Mail className="absolute top-1/2 left-4 h-5 w-5 -translate-y-1/2 text-gray-400" />
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={placeholder}
              required
              className="shadow-soft focus:border-primary-500 focus:ring-primary-500/20 w-full rounded-xl border border-gray-200 bg-white/70 py-4 pr-4 pl-12 text-lg text-gray-900 placeholder-gray-500 backdrop-blur-sm transition-all duration-300 focus:bg-white focus:ring-2 dark:border-gray-600 dark:bg-gray-800/70 dark:text-white dark:placeholder-gray-400 dark:focus:bg-gray-800"
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="group bg-gradient-primary shadow-medium hover:shadow-strong focus:ring-primary-500/50 flex w-full items-center justify-center gap-3 rounded-xl px-8 py-4 text-lg font-semibold text-white transition-all duration-300 hover:scale-[1.02] focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <Send className="h-6 w-6 transition-transform duration-300 group-hover:translate-x-1" />
            <span>{isSubmitting ? 'Subscribing...' : 'Subscribe to Newsletter'}</span>
          </button>

          {error && (
            <div className="animate-fade-in-up flex items-center gap-3 rounded-xl border border-red-200 bg-red-50 p-4 text-red-600 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400">
              <X className="h-5 w-5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          <p className="text-center text-sm text-gray-500 dark:text-gray-400">
            ✨ No spam, unsubscribe at any time. We respect your privacy.
          </p>
        </form>
      </div>
    </div>
  )
}
