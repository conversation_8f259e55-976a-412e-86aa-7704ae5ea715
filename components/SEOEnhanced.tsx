import Head from 'next/head'
import siteMetadata from '@/data/siteMetadata'
import { StructuredData } from './StructuredData'

interface SEOEnhancedProps {
  title: string
  description: string
  url?: string
  image?: string
  author?: string
  publishedDate?: string
  modifiedDate?: string
  tags?: string[]
  type?: 'website' | 'article'
  noindex?: boolean
  canonical?: string
}

export function SEOEnhanced({
  title,
  description,
  url,
  image,
  author,
  publishedDate,
  modifiedDate,
  tags,
  type = 'website',
  noindex = false,
  canonical,
}: SEOEnhancedProps) {
  const pageUrl = url || siteMetadata.siteUrl
  const pageImage = image || `${siteMetadata.siteUrl}${siteMetadata.socialBanner}`
  const pageAuthor = author || siteMetadata.author
  const canonicalUrl = canonical || pageUrl

  // Enhanced title for better CTR
  const enhancedTitle =
    type === 'article'
      ? `${title} | ${siteMetadata.title}`
      : title === siteMetadata.title
        ? title
        : `${title} | ${siteMetadata.title}`

  return (
    <>
      <Head>
        {/* Basic Meta Tags */}
        <title>{enhancedTitle}</title>
        <meta name="description" content={description} />
        <meta name="author" content={pageAuthor} />

        {/* Canonical URL */}
        <link rel="canonical" href={canonicalUrl} />

        {/* Robots */}
        {noindex ? (
          <meta name="robots" content="noindex, nofollow" />
        ) : (
          <meta
            name="robots"
            content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
          />
        )}

        {/* Keywords for affiliate content */}
        {tags && tags.length > 0 && <meta name="keywords" content={tags.join(', ')} />}

        {/* Open Graph / Facebook */}
        <meta property="og:type" content={type === 'article' ? 'article' : 'website'} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:url" content={pageUrl} />
        <meta property="og:image" content={pageImage} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
        <meta property="og:site_name" content={siteMetadata.title} />
        <meta property="og:locale" content="en_US" />

        {/* Article specific OG tags */}
        {type === 'article' && (
          <>
            <meta property="article:author" content={pageAuthor} />
            {publishedDate && <meta property="article:published_time" content={publishedDate} />}
            {modifiedDate && <meta property="article:modified_time" content={modifiedDate} />}
            {tags && tags.map((tag) => <meta key={tag} property="article:tag" content={tag} />)}
          </>
        )}

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content={pageImage} />
        <meta name="twitter:creator" content={siteMetadata.twitter} />
        <meta name="twitter:site" content={siteMetadata.twitter} />

        {/* Additional SEO tags for monetization */}
        <meta name="theme-color" content="#000000" />
        <meta name="msapplication-TileColor" content="#000000" />

        {/* Google Site Verification (add your code) */}
        {/* <meta name="google-site-verification" content="your-verification-code" /> */}

        {/* Bing Webmaster Tools (add your code) */}
        {/* <meta name="msvalidate.01" content="your-verification-code" /> */}

        {/* Pinterest Domain Verification (add your code) */}
        {/* <meta name="p:domain_verify" content="your-verification-code" /> */}
      </Head>

      {/* Structured Data */}
      {type === 'website' && <StructuredData type="website" />}

      {type === 'article' && (
        <StructuredData
          type="article"
          data={{
            title,
            description,
            url: pageUrl,
            image: pageImage,
            author: pageAuthor,
            datePublished: publishedDate,
            dateModified: modifiedDate,
            tags,
          }}
        />
      )}
    </>
  )
}
