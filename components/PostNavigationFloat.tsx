'use client'

import { useEffect, useState } from 'react'
import Link from '@/components/Link'
import { ChevronLeft, ChevronRight, ArrowUp, Share, Bookmark, Check, Co<PERSON>, <PERSON>rk<PERSON>, Star, Heart } from 'lucide-react'

interface PostNavigationFloatProps {
  prev?: { path: string; title: string }
  next?: { path: string; title: string }
  postTitle?: string
}

export default function PostNavigationFloat({ prev, next, postTitle }: PostNavigationFloatProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [showShare, setShowShare] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [copied, setCopied] = useState(false)
  const [isLiked, setIsLiked] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show navigation after scrolling past the header with smooth transition
      setIsVisible(window.scrollY > 400)
    }

    window.addEventListener('scroll', handleScroll)
    
    // Check if post is bookmarked and liked
    const bookmarks = JSON.parse(localStorage.getItem('bookmarked-posts') || '[]')
    const likes = JSON.parse(localStorage.getItem('liked-posts') || '[]')
    setIsBookmarked(bookmarks.includes(window.location.pathname))
    setIsLiked(likes.includes(window.location.pathname))
    
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleShare = async () => {
    if (navigator.share && postTitle) {
      try {
        await navigator.share({
          title: postTitle,
          url: window.location.href,
        })
      } catch (err) {
        // Fallback to copying URL
        await navigator.clipboard.writeText(window.location.href)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      }
    } else {
      // Fallback for browsers without Web Share API
      await navigator.clipboard.writeText(window.location.href)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const handleBookmark = () => {
    const bookmarks = JSON.parse(localStorage.getItem('bookmarked-posts') || '[]')
    const currentPath = window.location.pathname
    
    if (isBookmarked) {
      const updatedBookmarks = bookmarks.filter((path: string) => path !== currentPath)
      localStorage.setItem('bookmarked-posts', JSON.stringify(updatedBookmarks))
      setIsBookmarked(false)
    } else {
      const updatedBookmarks = [...bookmarks, currentPath]
      localStorage.setItem('bookmarked-posts', JSON.stringify(updatedBookmarks))
      setIsBookmarked(true)
    }
  }

  const handleLike = () => {
    const likes = JSON.parse(localStorage.getItem('liked-posts') || '[]')
    const currentPath = window.location.pathname
    
    if (isLiked) {
      const updatedLikes = likes.filter((path: string) => path !== currentPath)
      localStorage.setItem('liked-posts', JSON.stringify(updatedLikes))
      setIsLiked(false)
    } else {
      const updatedLikes = [...likes, currentPath]
      localStorage.setItem('liked-posts', JSON.stringify(updatedLikes))
      setIsLiked(true)
    }
  }

  if (!isVisible) return null

  return (
    <>
      {/* Enhanced Left side - Previous post */}
      {prev && (
        <div className="fixed top-1/2 left-6 z-30 hidden -translate-y-1/2 lg:block">
          <Link
            href={`/${prev.path}`}
            className="group flex items-center rounded-3xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl p-5 shadow-2xl border border-white/30 dark:border-gray-700/30 transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25)] hover:scale-110 hover:bg-white dark:hover:bg-gray-800 hover:-translate-x-2"
          >
            <div className="flex items-center justify-center w-12 h-12 rounded-2xl bg-gradient-to-r from-primary-500 via-blue-500 to-primary-600 text-white mr-4 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12 group-hover:shadow-lg">
              <ChevronLeft className="h-6 w-6 transition-transform duration-300 group-hover:-translate-x-0.5" />
            </div>
            <div className="max-w-xs opacity-0 -translate-x-4 transition-all duration-500 ease-out group-hover:opacity-100 group-hover:translate-x-0">
              <div className="text-xs font-bold text-primary-600 dark:text-primary-400 uppercase tracking-wider mb-2 flex items-center">
                <div className="w-2 h-2 rounded-full bg-primary-500 mr-2 animate-pulse" />
                Previous Article
                <Sparkles className="ml-2 h-3 w-3 animate-pulse" />
              </div>
              <div className="text-sm font-bold text-gray-900 dark:text-gray-100 line-clamp-2 leading-tight transition-colors duration-300 group-hover:text-primary-600 dark:group-hover:text-primary-400">
                {prev.title}
              </div>
            </div>
          </Link>
        </div>
      )}

      {/* Enhanced Right side - Next post */}
      {next && (
        <div className="fixed right-6 top-1/2 z-30 hidden -translate-y-1/2 lg:block">
          <Link
            href={`/${next.path}`}
            className="group flex items-center rounded-3xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl p-5 shadow-2xl border border-white/30 dark:border-gray-700/30 transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25)] hover:scale-110 hover:bg-white dark:hover:bg-gray-800 hover:translate-x-2"
          >
            <div className="max-w-xs text-right opacity-0 translate-x-4 transition-all duration-500 ease-out group-hover:opacity-100 group-hover:translate-x-0">
              <div className="text-xs font-bold text-primary-600 dark:text-primary-400 uppercase tracking-wider mb-2 flex items-center justify-end">
                <Sparkles className="mr-2 h-3 w-3 animate-pulse" />
                Next Article
                <div className="w-2 h-2 rounded-full bg-primary-500 ml-2 animate-pulse" />
              </div>
              <div className="text-sm font-bold text-gray-900 dark:text-gray-100 line-clamp-2 leading-tight transition-colors duration-300 group-hover:text-primary-600 dark:group-hover:text-primary-400">
                {next.title}
              </div>
            </div>
            <div className="flex items-center justify-center w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 via-primary-500 to-blue-600 text-white ml-4 transition-all duration-500 group-hover:scale-125 group-hover:-rotate-12 group-hover:shadow-lg">
              <ChevronRight className="h-6 w-6 transition-transform duration-300 group-hover:translate-x-0.5" />
            </div>
          </Link>
        </div>
      )}

      {/* Enhanced Bottom floating actions */}
      <div className="fixed bottom-8 right-8 z-40 flex flex-col gap-4">
        {/* Like button */}
        <div className="relative">
          <button
            onClick={handleLike}
            className="group flex h-16 w-16 items-center justify-center rounded-2xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl text-gray-600 dark:text-gray-400 shadow-2xl border border-white/30 dark:border-gray-700/30 transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25)] hover:scale-125 hover:rotate-12"
            aria-label={isLiked ? "Remove like" : "Like post"}
          >
            <Heart 
              className={`h-6 w-6 transition-all duration-500 group-hover:scale-125 ${
                isLiked 
                  ? 'fill-red-500 text-red-500 animate-pulse' 
                  : 'group-hover:text-red-500 group-hover:fill-red-100'
              }`} 
            />
            {isLiked && (
              <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-gradient-to-r from-red-400 to-pink-500 flex items-center justify-center animate-bounce">
                <Star className="h-2 w-2 text-white" />
              </div>
            )}
          </button>
        </div>

        {/* Enhanced Share button */}
        <div className="relative">
          <button
            onClick={handleShare}
            className="group flex h-16 w-16 items-center justify-center rounded-2xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl text-gray-600 dark:text-gray-400 shadow-2xl border border-white/30 dark:border-gray-700/30 transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25)] hover:scale-125 hover:rotate-12 hover:text-blue-500"
            aria-label="Share post"
          >
            {copied ? (
              <div className="relative">
                <Check className="h-6 w-6 text-green-500 animate-bounce" />
                <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-green-400 animate-ping" />
              </div>
            ) : (
              <Share className="h-6 w-6 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12" />
            )}
          </button>
          {copied && (
            <div className="absolute top-1/2 right-20 -translate-y-1/2 px-4 py-3 rounded-xl bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-sm font-bold whitespace-nowrap shadow-2xl animate-fade-in-up border border-gray-700 dark:border-gray-300">
              Link copied!
              <div className="absolute top-1/2 -right-1.5 -translate-y-1/2 w-3 h-3 bg-gray-900 dark:bg-gray-100 rotate-45" />
              <Sparkles className="inline ml-2 h-3 w-3 animate-pulse" />
            </div>
          )}
        </div>

        {/* Enhanced Bookmark button */}
        <button
          onClick={handleBookmark}
          className="group flex h-16 w-16 items-center justify-center rounded-2xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl text-gray-600 dark:text-gray-400 shadow-2xl border border-white/30 dark:border-gray-700/30 transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25)] hover:scale-125 hover:rotate-12"
          aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
        >
          <Bookmark 
            className={`h-6 w-6 transition-all duration-500 group-hover:scale-125 ${
              isBookmarked 
                ? 'fill-amber-500 text-amber-500 animate-pulse' 
                : 'group-hover:text-amber-500 group-hover:fill-amber-100'
            }`} 
          />
          {isBookmarked && (
            <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 flex items-center justify-center animate-bounce">
              <Star className="h-2 w-2 text-white" />
            </div>
          )}
        </button>

        {/* Enhanced Back to top */}
        <button
          onClick={scrollToTop}
          className="group flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary-500 via-blue-500 to-primary-600 text-white shadow-2xl transition-all duration-500 hover:shadow-[0_20px_60px_-12px_rgba(59,130,246,0.5)] hover:scale-125 hover:rotate-12 hover:from-primary-600 hover:to-blue-600"
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-6 w-6 transition-all duration-500 group-hover:scale-125 group-hover:-translate-y-1 group-hover:rotate-12" />
          <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white/30 animate-ping" />
        </button>
      </div>

      {/* Enhanced Mobile floating actions */}
      <div className="fixed bottom-6 left-6 right-6 z-40 flex justify-center lg:hidden">
        <div className="flex items-center gap-4 px-8 py-4 rounded-3xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl shadow-2xl border border-white/30 dark:border-gray-700/30">
          {/* Mobile Like */}
          <button
            onClick={handleLike}
            className="group flex h-12 w-12 items-center justify-center rounded-xl text-gray-600 dark:text-gray-400 transition-all duration-300 hover:scale-125 hover:rotate-12"
            aria-label={isLiked ? "Remove like" : "Like post"}
          >
            <Heart className={`h-5 w-5 transition-all duration-300 ${isLiked ? 'fill-red-500 text-red-500' : 'group-hover:text-red-500'}`} />
          </button>

          {/* Mobile Share */}
          <button
            onClick={handleShare}
            className="group flex h-12 w-12 items-center justify-center rounded-xl text-gray-600 dark:text-gray-400 transition-all duration-300 hover:scale-125 hover:rotate-12 hover:text-blue-500"
            aria-label="Share post"
          >
            {copied ? <Check className="h-5 w-5 text-green-500" /> : <Share className="h-5 w-5 transition-transform duration-300 group-hover:rotate-12" />}
          </button>

          {/* Mobile Bookmark */}
          <button
            onClick={handleBookmark}
            className="group flex h-12 w-12 items-center justify-center rounded-xl text-gray-600 dark:text-gray-400 transition-all duration-300 hover:scale-125 hover:rotate-12 hover:text-amber-500"
            aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
          >
            <Bookmark className={`h-5 w-5 transition-all duration-300 ${isBookmarked ? 'fill-amber-500 text-amber-500' : ''}`} />
          </button>

          {/* Mobile Back to top */}
          <button
            onClick={scrollToTop}
            className="group flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-primary-500 to-blue-500 text-white transition-all duration-300 hover:scale-125 hover:rotate-12 hover:from-primary-600 hover:to-blue-600"
            aria-label="Scroll to top"
          >
            <ArrowUp className="h-5 w-5 transition-all duration-300 group-hover:-translate-y-0.5 group-hover:rotate-12" />
          </button>
        </div>
      </div>
    </>
  )
}
