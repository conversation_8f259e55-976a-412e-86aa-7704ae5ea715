'use client'

import { useState, useEffect, memo } from 'react'
import { Eye } from 'lucide-react'

interface ViewCounterProps {
  slug: string
  initialCount?: number
  className?: string
}

function ViewCounter({ slug, initialCount = 0, className = '' }: ViewCounterProps) {
  const [viewCount, setViewCount] = useState(initialCount)
  const [loading, setLoading] = useState(!initialCount)

  useEffect(() => {
    // If we don't have initial count, fetch it
    if (!initialCount) {
      fetchViewCount()
    }
  }, [slug, initialCount])

  const fetchViewCount = async () => {
    try {
      const response = await fetch('/api/posts')
      if (response.ok) {
        const posts = await response.json()
        const post = posts.find((p: { slug: string; view_count?: number }) => p.slug === slug)
        if (post) {
          setViewCount(post.view_count || 0)
        }
      }
    } catch (error) {
      console.error('Failed to fetch view count:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={`flex items-center space-x-1 text-gray-500 dark:text-gray-400 ${className}`}>
        <Eye className="h-4 w-4" />
        <span className="text-sm">Loading...</span>
      </div>
    )
  }

  return (
    <div className={`flex items-center space-x-1 text-gray-500 dark:text-gray-400 ${className}`}>
      <Eye className="h-4 w-4" />
      <span className="text-sm">
        {viewCount.toLocaleString()} {viewCount === 1 ? 'view' : 'views'}
      </span>
    </div>
  )
}

export default memo(ViewCounter)
