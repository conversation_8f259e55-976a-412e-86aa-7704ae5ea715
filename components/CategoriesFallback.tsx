import Link from '@/components/Link'
import PageHeader from '@/components/PageHeader'
import { 
  Layers, 
  ArrowRight,
  Code,
  Server,
  Cloud,
  Wrench,
  Folder
} from 'lucide-react'

const FALLBACK_CATEGORIES = [
  {
    id: 'frontend',
    name: 'Frontend Development',
    slug: 'frontend',
    description: 'Client-side development, UI/UX, and modern frameworks',
    icon: 'Code'
  },
  {
    id: 'backend', 
    name: 'Backend Development',
    slug: 'backend',
    description: 'Server-side development, APIs, and databases',
    icon: 'Server'
  },
  {
    id: 'devops',
    name: 'DevOps & Cloud', 
    slug: 'devops',
    description: 'Deployment, CI/CD, containerization, and cloud services',
    icon: 'Cloud'
  },
  {
    id: 'tools',
    name: 'Tools & Frameworks',
    slug: 'tools', 
    description: 'Development tools, frameworks, and productivity',
    icon: 'Wrench'
  }
]

const ICON_MAP: Record<string, any> = {
  Code,
  Server,
  Cloud,
  Wrench,
  Folder
}

export default function CategoriesFallback() {
  return (
    <>
      <PageHeader
        title="Categories"
        description="Explore main categories covering all aspects of modern web development"
        icon={<Layers className="w-4 h-4" />}
        badge="Categories"
      />

      <div className="bg-slate-50 dark:bg-slate-900 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="space-y-8">
            {/* Simple Stats */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-slate-900 dark:text-white">{FALLBACK_CATEGORIES.length}</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">Categories</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-slate-900 dark:text-white">-</div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">Articles</div>
                </div>
              </div>
            </div>

            {/* Categories Grid - Minimal */}
            <div className="grid gap-4">
              {FALLBACK_CATEGORIES.map((category) => {
                const Icon = ICON_MAP[category.icon] || Folder

                return (
                  <Link
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="group block"
                  >
                    <div className="p-6 rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 transition-all duration-200 group-hover:shadow-sm group-hover:border-slate-300 dark:group-hover:border-slate-600">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="p-2 rounded-lg bg-slate-100 dark:bg-slate-700">
                            <Icon className="w-5 h-5 text-slate-600 dark:text-slate-400" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1">{category.name}</h3>
                            <p className="text-sm text-slate-600 dark:text-slate-400">{category.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div className="text-sm text-slate-500 dark:text-slate-400">Coming soon</div>
                          </div>
                          <ArrowRight className="w-4 h-4 text-slate-400 transition-transform group-hover:translate-x-1" />
                        </div>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>

            {/* Notice */}
            <div className="text-center py-8">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-800 rounded-lg">
                <Folder className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                <span className="text-sm text-amber-700 dark:text-amber-300">
                  Categories system is being set up. Please run the database migration.
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
