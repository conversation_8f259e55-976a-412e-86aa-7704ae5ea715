'use client'

import { useState, useEffect } from 'react'
import { Eye, Edit3, Trash2, FileText, Calendar, User, BarChart3, ExternalLink } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { BlogPost } from '../lib/supabase'

interface AdminPostsTableProps {
  onEditPost?: (post: BlogPost) => void
  onPostChanged?: () => void
}

export function AdminPostsTable({ onEditPost, onPostChanged }: AdminPostsTableProps) {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft' | 'disabled'>('all')
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true)
        // Use admin API to get all posts including disabled ones
        const response = await fetch('/api/admin/posts')
        if (response.ok) {
          const data = await response.json()
          setPosts(data)
        } else {
          toast.error('Failed to load posts')
        }
      } catch (error) {
        console.error('Error fetching posts:', error)
        toast.error('Failed to load posts')
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [refreshKey])

  const handleDelete = async (id: string | undefined, title: string) => {
    if (!id) return
    if (!confirm(`Are you sure you want to delete "${title}"?`)) return

    try {
      const response = await fetch(`/api/posts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setPosts(posts.filter((p) => p.id !== id))
        toast.success('Post deleted successfully')
        // Notify parent component about the change
        onPostChanged?.()
        // Also refresh the current posts list
        refreshPosts()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete post')
      }
    } catch (error) {
      console.error('Error deleting post:', error)
      toast.error('Error deleting post')
    }
  }

  // Add refresh function to reload posts after changes
  const refreshPosts = async () => {
    // Force re-fetch by updating refresh key
    setRefreshKey(prev => prev + 1)
  }

  const filteredPosts = posts.filter((post) => {
    if (statusFilter === 'all') return true
    return post.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
      case 'draft':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'disabled':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <FileText className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Posts</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 mb-4"></div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Loading posts...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <FileText className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Posts</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{posts.length} total posts</p>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex gap-2">
          {(['all', 'published', 'draft', 'disabled'] as const).map((status) => {
            const count = status === 'all' ? posts.length : posts.filter((p) => p.status === status).length
            return (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                  statusFilter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-600/50 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-500/50'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {/* Posts List */}
      {filteredPosts.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No posts found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {posts.length === 0 ? 'No posts created yet' : `No ${statusFilter} posts`}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredPosts.map((post) => (
            <div key={post.id} className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4 last:border-b-0 hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-lg px-3 py-2 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Status and Meta Info */}
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(post.status || 'published')}`}>
                        {post.status || 'published'}
                      </span>
                      {post.featured && (
                        <span className="px-2 py-1 bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400 rounded text-xs font-medium">
                          Featured
                        </span>
                      )}
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(post.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {/* Title and Description */}
                    <div className="mb-3">
                      <h3 className="font-semibold text-base text-gray-900 dark:text-white mb-1">
                        {post.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{post.description}</p>
                    </div>
                    
                    {/* Stats */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <BarChart3 className="h-3 w-3" />
                        <span>{post.view_count || 0} views</span>
                      </div>
                      <span>{post.readTime}</span>
                      <span>{post.tags?.length || 0} tags</span>
                    </div>
                  </div>

                                      {/* Action Buttons */}
                  <div className="flex items-center space-x-1 ml-4">
                    <button
                      onClick={() => window.open(`/blog/preview/${post.slug}`, '_blank')}
                      className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"
                      title="Preview Post"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    
                    {post.status === 'published' && (
                      <button
                        onClick={() => window.open(`/blog/${post.slug}`, '_blank')}
                        className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200"
                        title="View Live Post"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => onEditPost?.(post)}
                      className="p-2 text-gray-500 hover:text-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-all duration-200"
                      title="Edit Post"
                    >
                      <Edit3 className="h-4 w-4" />
                    </button>
                    {post.id && (
                      <button
                        onClick={() => handleDelete(post.id, post.title)}
                        className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                        title="Delete Post"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  )
}
