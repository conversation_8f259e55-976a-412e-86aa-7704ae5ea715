import Link from 'next/link'
import { slug } from 'github-slugger'

interface Props {
  text: string
  variant?: 'default' | 'technology' | 'topic' | 'skill' | 'project'
  size?: 'sm' | 'md' | 'lg'
  count?: number
  interactive?: boolean
}

// Professional tag styles with sophisticated colors
const tagStyles = {
  default: {
    bg: 'bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600',
    text: 'text-slate-700 dark:text-slate-300',
    border: 'border-slate-200 dark:border-slate-600',
  },
  technology: {
    bg: 'bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:hover:bg-indigo-900/50',
    text: 'text-indigo-800 dark:text-indigo-300',
    border: 'border-indigo-200 dark:border-indigo-700',
  },
  topic: {
    bg: 'bg-emerald-100 hover:bg-emerald-200 dark:bg-emerald-900/30 dark:hover:bg-emerald-900/50',
    text: 'text-emerald-800 dark:text-emerald-300',
    border: 'border-emerald-200 dark:border-emerald-700',
  },
  skill: {
    bg: 'bg-purple-100 hover:bg-purple-200 dark:bg-purple-900/30 dark:hover:bg-purple-900/50',
    text: 'text-purple-800 dark:text-purple-300',
    border: 'border-purple-200 dark:border-purple-700',
  },
  project: {
    bg: 'bg-amber-100 hover:bg-amber-200 dark:bg-amber-900/30 dark:hover:bg-amber-900/50',
    text: 'text-amber-800 dark:text-amber-300',
    border: 'border-amber-200 dark:border-amber-700',
  },
}

const sizeStyles = {
  sm: 'px-2.5 py-1 text-xs',
  md: 'px-3 py-1.5 text-sm',
  lg: 'px-4 py-2 text-base',
}

// Auto-detect tag category based on content
const detectTagCategory = (text: string): Props['variant'] => {
  const lowerText = text.toLowerCase()

  // Technology tags
  if (
    [
      'javascript',
      'typescript',
      'react',
      'nextjs',
      'node',
      'python',
      'docker',
      'kubernetes',
      'aws',
      'api',
      'database',
      'sql',
      'mongodb',
      'postgres',
      'redis',
      'graphql',
      'rest',
      'web',
      'mobile',
      'ios',
      'android',
      'vue',
      'angular',
      'svelte',
    ].some((tech) => lowerText.includes(tech))
  ) {
    return 'technology'
  }

  // Skill tags
  if (
    [
      'frontend',
      'backend',
      'fullstack',
      'devops',
      'ui',
      'ux',
      'design',
      'security',
      'performance',
      'testing',
      'deployment',
      'architecture',
      'patterns',
      'algorithms',
    ].some((skill) => lowerText.includes(skill))
  ) {
    return 'skill'
  }

  // Project tags
  if (
    ['project', 'demo', 'showcase', 'portfolio', 'app', 'website', 'tool', 'utility', 'open-source'].some((proj) =>
      lowerText.includes(proj)
    )
  ) {
    return 'project'
  }

  // Topic tags (general categories)
  if (
    [
      'tutorial',
      'guide',
      'tips',
      'best-practices',
      'career',
      'productivity',
      'learning',
      'review',
      'opinion',
      'news',
      'thoughts',
      'insights',
    ].some((topic) => lowerText.includes(topic))
  ) {
    return 'topic'
  }

  return 'default'
}

const Tag = ({ text, variant, size = 'md', count, interactive = true }: Props) => {
  const detectedVariant = variant || detectTagCategory(text) || 'default'
  const styles = tagStyles[detectedVariant]
  const sizeStyle = sizeStyles[size]

  const tagContent = (
    <span
      className={`inline-flex items-center gap-1.5 rounded-xl border font-semibold transition-all duration-300 ${styles.bg} ${styles.text} ${styles.border} ${sizeStyle} ${
        interactive ? 'cursor-pointer hover:scale-105 hover:shadow-md' : ''
      } relative overflow-hidden group`}
    >
      {/* Professional shimmer effect on hover */}
      <span className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent group-hover:translate-x-full transition-transform duration-700"></span>
      
      {/* Category icon */}
      <span className="relative flex items-center gap-1">
        <span
          className={`w-1.5 h-1.5 rounded-full ${
            detectedVariant === 'technology'
              ? 'bg-indigo-500'
              : detectedVariant === 'topic'
                ? 'bg-emerald-500'
                : detectedVariant === 'skill'
                  ? 'bg-purple-500'
                  : detectedVariant === 'project'
                    ? 'bg-amber-500'
                    : 'bg-slate-500'
          }`}
        />
        <span className="relative z-10">{text.split(' ').join('-')}</span>
      </span>
      
      {count && (
        <span className={`text-xs px-1.5 py-0.5 rounded-full font-bold ${
          detectedVariant === 'technology'
            ? 'bg-indigo-500/20 text-indigo-700 dark:text-indigo-300'
            : detectedVariant === 'topic'
              ? 'bg-emerald-500/20 text-emerald-700 dark:text-emerald-300'
              : detectedVariant === 'skill'
                ? 'bg-purple-500/20 text-purple-700 dark:text-purple-300'
                : detectedVariant === 'project'
                  ? 'bg-amber-500/20 text-amber-700 dark:text-amber-300'
                  : 'bg-slate-500/20 text-slate-700 dark:text-slate-300'
        }`}>
          {count}
        </span>
      )}
    </span>
  )

  if (!interactive) {
    return tagContent
  }

  return (
    <Link
      href={`/tags/${slug(text)}`}
      className="mr-2 mb-2 inline-block"
      title={`View all posts tagged with ${text} (${detectedVariant})`}
    >
      {tagContent}
    </Link>
  )
}

export default Tag
