'use client'

import Link from './Link'
import siteMetadata from '@/data/siteMetadata'
import SocialIcon from '@/components/social-icons'
import NewsletterForm from './NewsletterForm'
import { usePathname } from 'next/navigation'
import { Heart, Coffee } from 'lucide-react'

export default function Footer() {
  const pathname = usePathname()
  const isAdminPage = pathname?.startsWith('/admin')

  return (
    <footer className="relative mt-6 bg-white/50 dark:bg-gray-950/50 backdrop-blur-sm">
      {/* Background Pattern */}
      <div className="to-primary-50/30 dark:to-primary-900/20 absolute inset-0 bg-gradient-to-br from-gray-50 via-white dark:from-gray-900 dark:via-gray-800" />
      <div className="absolute inset-0 opacity-40">
        <svg
          width="40"
          height="40"
          viewBox="0 0 40 40"
          xmlns="http://www.w3.org/2000/svg"
          className="absolute inset-0 h-full w-full"
        >
          <defs>
            <pattern
              id="footer-pattern"
              x="0"
              y="0"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <circle cx="20" cy="20" r="2" fill="#9C92AC" fillOpacity="0.1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#footer-pattern)" />
        </svg>
      </div>

      <div className="relative">
        <div className="mx-auto max-w-3xl xl:max-w-5xl px-6 py-4 sm:py-6">
          {/* Centered Main Content */}
          <div className="flex flex-col items-center justify-center text-center space-y-6">
            {/* Newsletter Section - Hidden on admin pages */}
            {!isAdminPage && (
              <div className="w-full max-w-md">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                      Stay Connected
                    </h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      Get updates delivered to your inbox
                    </p>
                  </div>
                  <NewsletterForm
                    compact={true}
                    source="footer"
                    title=""
                    description=""
                    placeholder="Enter your email"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            {/* Centered Social Links */}
            <div className="flex justify-center space-x-3">
              {[
                { kind: 'mail' as const, href: `mailto:${siteMetadata.email}` },
                { kind: 'github' as const, href: siteMetadata.github },
                { kind: 'twitter' as const, href: siteMetadata.twitter },
                { kind: 'linkedin' as const, href: siteMetadata.linkedin },
              ].map((social) => (
                <div
                  key={social.kind}
                  className="group hover:bg-primary-50 dark:hover:bg-primary-900/20 relative rounded-lg bg-white/50 p-2 shadow-md transition-all duration-300 hover:scale-110 hover:shadow-lg dark:bg-gray-800/50"
                >
                  <SocialIcon kind={social.kind} href={social.href} size={5} />
                </div>
              ))}
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-6 border-t border-gray-200 pt-4 dark:border-gray-700">
            <div className="flex flex-col items-center justify-center space-y-3 text-center sm:flex-row sm:space-y-0 sm:space-x-6">
              {/* Home Button */}
              <Link
                href="/"
                className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg"
              >
                <span>← Back to Home</span>
              </Link>

              {/* Copyright */}
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <span>© {new Date().getFullYear()}</span>
                <Link
                  href="/"
                  className="hover:text-primary-600 dark:hover:text-primary-400 font-medium text-gray-900 transition-colors duration-200 dark:text-white"
                >
                  {siteMetadata.title}
                </Link>
              </div>

              {/* Made with Love */}
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <span>Made with</span>
                <Heart className="h-4 w-4 animate-pulse text-red-500" />
                <span>and</span>
                <Coffee className="h-4 w-4 text-amber-600" />
                <span>by {siteMetadata.author}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
