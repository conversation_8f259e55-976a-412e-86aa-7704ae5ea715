import siteMetadata from '@/data/siteMetadata'

interface StructuredDataProps {
  type: 'website' | 'article' | 'breadcrumb'
  data?: {
    title?: string
    description?: string
    datePublished?: string
    dateModified?: string
    author?: string
    tags?: string[]
    url?: string
    image?: string
    wordCount?: number
    readingTime?: number
    breadcrumbs?: Array<{
      name: string
      url: string
    }>
  }
}

export function StructuredData({ type, data }: StructuredDataProps) {
  let structuredData: any = {}

  switch (type) {
    case 'website':
      structuredData = {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: siteMetadata.title,
        description: siteMetadata.description,
        url: siteMetadata.siteUrl,
        author: {
          '@type': 'Person',
          name: siteMetadata.author,
          email: siteMetadata.email,
        },
        publisher: {
          '@type': 'Person',
          name: siteMetadata.author,
        },
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${siteMetadata.siteUrl}/blog?search={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
      }
      break

    case 'article':
      if (data) {
        structuredData = {
          '@context': 'https://schema.org',
          '@type': 'BlogPosting',
          headline: data.title,
          description: data.description,
          image: data.image || siteMetadata.socialBanner,
          datePublished: data.datePublished,
          dateModified: data.dateModified || data.datePublished,
          author: {
            '@type': 'Person',
            name: data.author || siteMetadata.author,
          },
          publisher: {
            '@type': 'Person',
            name: siteMetadata.author,
            logo: {
              '@type': 'ImageObject',
              url: `${siteMetadata.siteUrl}${siteMetadata.siteLogo}`,
            },
          },
          url: data.url,
          mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': data.url,
          },
          keywords: data.tags?.join(', '),
          ...(data.wordCount && { wordCount: data.wordCount }),
          ...(data.readingTime && { 
            timeRequired: `PT${data.readingTime}M`,
            readingTime: `${data.readingTime} minutes`
          }),
        }
      }
      break

    case 'breadcrumb':
      if (data?.breadcrumbs) {
        structuredData = {
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          itemListElement: data.breadcrumbs.map((crumb, index) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: crumb.name,
            item: crumb.url,
          })),
        }
      }
      break
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}
