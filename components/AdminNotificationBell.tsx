'use client'

import { useState, useRef, useEffect } from 'react'
import { useAdminNotifications } from './hooks/useAdminNotifications'

export default function AdminNotificationBell() {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const { counts, recentActivity, loading, markAsChecked, refreshNotifications } =
    useAdminNotifications()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleBellClick = () => {
    setIsOpen(!isOpen)
    if (!isOpen && counts.totalNotifications > 0) {
      markAsChecked()
    }
  }

  const formatTimeAgo = (timeString: string) => {
    const now = new Date()
    const time = new Date(timeString)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  const getActivityIcon = (type: 'comment' | 'contact') => {
    if (type === 'comment') {
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
          <svg
            className="h-4 w-4 text-blue-600 dark:text-blue-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.96 8.96 0 01-4.906-1.431L3 21l2.069-5.094A8.96 8.96 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
            />
          </svg>
        </div>
      )
    }

    return (
      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
        <svg
          className="h-4 w-4 text-green-600 dark:text-green-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        onClick={handleBellClick}
        className="relative flex items-center justify-center rounded-full p-2 text-gray-600 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
        aria-label="Notifications"
      >
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-5-5V9.09c0-1-.68-1.92-1.66-2.08A6 6 0 007 9v3l-5 5h5m7-7v.01M8 21l4-4 4 4M16 4h2a2 2 0 012 2v14a2 2 0 01-2 2h-2"
          />
        </svg>

        {/* Notification Badge */}
        {counts.totalNotifications > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full border border-white bg-red-500 text-xs font-bold text-white shadow-sm">
            {counts.totalNotifications > 99 ? '99+' : counts.totalNotifications}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="ring-opacity-5 absolute right-0 z-50 mt-2 w-80 rounded-lg bg-white shadow-lg ring-1 ring-black dark:bg-gray-800 dark:ring-gray-700">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
            <button
              onClick={refreshNotifications}
              className="text-gray-400 transition-colors hover:text-gray-600 dark:hover:text-gray-300"
              disabled={loading}
              aria-label="Refresh notifications"
            >
              <svg
                className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>

          {/* Summary */}
          <div className="border-b border-gray-200 px-4 py-3 dark:border-gray-700">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {counts.pendingComments}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Pending Comments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {counts.newContacts}
                </div>
                <div className="text-gray-600 dark:text-gray-400">New Messages</div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="max-h-80 overflow-y-auto">
            {recentActivity.length > 0 ? (
              <div className="py-2">
                {recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className={`flex items-start space-x-3 px-4 py-3 transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 ${
                      activity.isNew ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    {getActivityIcon(activity.type)}
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.title}
                      </div>
                      <div className="truncate text-sm text-gray-500 dark:text-gray-400">
                        {activity.subtitle}
                      </div>
                      <div className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                        {formatTimeAgo(activity.time)}
                      </div>
                    </div>
                    {activity.isNew && <div className="h-2 w-2 rounded-full bg-blue-500"></div>}
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                <svg
                  className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                  />
                </svg>
                <div className="mt-2 text-sm">No recent activity</div>
              </div>
            )}
          </div>

          {/* Footer */}
          {recentActivity.length > 0 && (
            <div className="border-t border-gray-200 px-4 py-3 dark:border-gray-700">
              <button
                onClick={() => setIsOpen(false)}
                className="w-full text-center text-sm font-medium text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Close
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
