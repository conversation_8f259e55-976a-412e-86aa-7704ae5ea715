'use client'

import { useState, useEffect } from 'react'
import { Mail, Check, X, Trash2, Calendar, User, CheckCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { NewsletterSubscriber } from '../lib/supabase'

export function AdminNewsletterTable() {
  const [subscribers, setSubscribers] = useState<NewsletterSubscriber[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'active' | 'unsubscribed'>('all')

  useEffect(() => {
    const fetchSubscribers = async () => {
      try {
        const response = await fetch('/api/newsletter/admin')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            setSubscribers(result.data)
          } else {
            setSubscribers([])
          }
        }
      } catch (error) {
        console.error('Error fetching subscribers:', error)
        toast.error('Failed to load newsletter subscribers')
      } finally {
        setLoading(false)
      }
    }

    fetchSubscribers()
  }, [])

  const handleStatusUpdate = async (id: string, status: NewsletterSubscriber['status']) => {
    try {
      const response = await fetch('/api/newsletter/admin', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, status }),
      })

      if (response.ok) {
        setSubscribers(subscribers.map((s) => (s.id === id ? { ...s, status } : s)))
        toast.success(`Subscriber ${status}`)
      } else {
        toast.error('Failed to update subscriber')
      }
    } catch (error) {
      console.error('Error updating subscriber:', error)
      toast.error('Error updating subscriber')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this subscriber?')) return

    try {
      const response = await fetch('/api/newsletter/admin', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id }),
      })

      if (response.ok) {
        setSubscribers(subscribers.filter((s) => s.id !== id))
        toast.success('Subscriber deleted')
      } else {
        toast.error('Failed to delete subscriber')
      }
    } catch (error) {
      console.error('Error deleting subscriber:', error)
      toast.error('Error deleting subscriber')
    }
  }

  const filteredSubscribers = subscribers.filter((subscriber) => {
    if (statusFilter === 'all') return true
    return subscriber.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'unsubscribed':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Mail className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Newsletter</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 mb-4"></div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Loading subscribers...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Mail className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Newsletter</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{subscribers.length} total subscribers</p>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex gap-2">
          {(['all', 'pending', 'active', 'unsubscribed'] as const).map((status) => {
            const count = status === 'all' ? subscribers.length : subscribers.filter((s) => s.status === status).length
            return (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                  statusFilter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-600/50 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-500/50'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {/* Subscribers List */}
      {filteredSubscribers.length === 0 ? (
        <div className="text-center py-12">
          <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No subscribers found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {subscribers.length === 0 ? 'No subscribers yet' : `No ${statusFilter} subscribers`}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredSubscribers.map((subscriber) => (
            <div key={subscriber.id} className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4 last:border-b-0 hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-lg px-3 py-2 transition-all duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Status and Meta Info */}
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(subscriber.status)}`}>
                        {subscriber.status}
                      </span>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(subscriber.subscribed_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {/* Email and Confirmation */}
                    <div className="mb-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">{subscriber.email}</span>
                        {subscriber.confirmed_at && (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="h-3 w-3 text-green-500" />
                            <span className="text-xs text-green-600 dark:text-green-400">
                              Confirmed {new Date(subscriber.confirmed_at).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-1 ml-4">
                    {subscriber.status === 'pending' && (
                      <button
                        onClick={() => handleStatusUpdate(subscriber.id, 'active')}
                        className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200"
                        title="Activate Subscriber"
                      >
                        <Check className="h-4 w-4" />
                      </button>
                    )}
                    {subscriber.status === 'active' && (
                      <button
                        onClick={() => handleStatusUpdate(subscriber.id, 'unsubscribed')}
                        className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                        title="Unsubscribe"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(subscriber.id)}
                      className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                      title="Delete Subscriber"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  )
}
