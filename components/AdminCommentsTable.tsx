'use client'

import { useState, useEffect } from 'react'
import { MessageSquare, Check, X, Trash2, Calendar, User, FileText } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Comment } from '../lib/supabase'

export function AdminCommentsTable() {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all')

  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await fetch('/api/comments?admin=true')
        if (response.ok) {
          const data = await response.json()
          setComments(data)
        } else {
          toast.error('Failed to load comments')
        }
      } catch (error) {
        console.error('Error fetching comments:', error)
        toast.error('Failed to load comments')
      } finally {
        setLoading(false)
      }
    }

    fetchComments()
  }, [])

  const handleStatusUpdate = async (id: string, status: 'approved' | 'rejected') => {
    try {
      const response = await fetch('/api/comments', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, status }),
      })

      if (response.ok) {
        const updatedComment = await response.json()
        setComments(comments.map((c) => (c.id === id ? updatedComment : c)))
        toast.success(`Comment ${status}`)
      } else {
        toast.error('Failed to update comment')
      }
    } catch (error) {
      console.error('Error updating comment:', error)
      toast.error('Error updating comment')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) return

    try {
      const response = await fetch(`/api/comments?id=${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setComments(comments.filter((c) => c.id !== id))
        toast.success('Comment deleted')
      } else {
        toast.error('Failed to delete comment')
      }
    } catch (error) {
      console.error('Error deleting comment:', error)
      toast.error('Error deleting comment')
    }
  }

  const filteredComments = comments.filter((comment) => {
    if (statusFilter === 'all') return true
    return comment.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'rejected':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <MessageSquare className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Comments</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 mb-4"></div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Loading comments...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <MessageSquare className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Comments</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{comments.length} total comments</p>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex gap-2">
          {(['all', 'pending', 'approved', 'rejected'] as const).map((status) => {
            const count = status === 'all' ? comments.length : comments.filter((c) => c.status === status).length
            return (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                  statusFilter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-600/50 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-500/50'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {/* Comments List */}
      {filteredComments.length === 0 ? (
        <div className="text-center py-12">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No comments found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {comments.length === 0 ? 'No comments yet' : `No ${statusFilter} comments`}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredComments.map((comment) => (
            <div key={comment.id} className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4 last:border-b-0 hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-lg px-3 py-2 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Status and Meta Info */}
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(comment.status)}`}>
                        {comment.status}
                      </span>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(comment.created_at || '').toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {/* Author Info */}
                    <div className="mb-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">{comment.author_name}</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">{comment.author_email}</span>
                      </div>
                    </div>
                    
                    {/* Comment Content */}
                    <div className="mb-3">
                      <p className="text-gray-700 dark:text-gray-300 mb-2 leading-relaxed">
                        {comment.content}
                      </p>
                    </div>
                    
                    {/* Post Reference */}
                    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <FileText className="h-3 w-3" />
                      <span>Post: {comment.post_slug}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-1 ml-4">
                    {comment.status === 'pending' && (
                      <>
                        <button
                          onClick={() => comment.id && handleStatusUpdate(comment.id, 'approved')}
                          className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200"
                          title="Approve Comment"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => comment.id && handleStatusUpdate(comment.id, 'rejected')}
                          className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                          title="Reject Comment"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => comment.id && handleDelete(comment.id)}
                      className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                      title="Delete Comment"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  )
}
