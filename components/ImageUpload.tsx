'use client'

import { useState, useCallback } from 'react'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import toast from 'react-hot-toast'

interface ImageUploadProps {
  onImageUploaded: (url: string) => void
  folder?: string
  className?: string
  accept?: string
  maxSizeMB?: number
}

export default function ImageUpload({
  onImageUploaded,
  folder = 'blog-images',
  className = '',
  accept = 'image/*',
  maxSizeMB = 5,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  const handleUpload = async (file: File) => {
    if (file.size > maxSizeMB * 1024 * 1024) {
      toast.error(`File too large. Maximum size is ${maxSizeMB}MB.`)
      return
    }

    setIsUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', folder)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed')
      }

      onImageUploaded(data.url)
      toast.success('Image uploaded successfully!')
    } catch (error) {
      console.error('Upload error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to upload image')
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleUpload(file)
    }
  }

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const file = e.dataTransfer.files?.[0]
    if (file && file.type.startsWith('image/')) {
      handleUpload(file)
    } else {
      toast.error('Please drop an image file')
    }
  }, [])

  return (
    <div className={`relative ${className}`}>
      <div
        className={`rounded-lg border-2 border-dashed p-6 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500'
        } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          disabled={isUploading}
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
        />

        <div className="flex flex-col items-center gap-3">
          {isUploading ? (
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
          ) : (
            <ImageIcon className="h-8 w-8 text-gray-400" />
          )}

          <div>
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {isUploading ? 'Uploading...' : 'Click or drag image to upload'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              PNG, JPG, GIF, WebP up to {maxSizeMB}MB
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Image Preview Component
interface ImagePreviewProps {
  src: string
  alt: string
  onRemove?: () => void
  onClick?: () => void
  className?: string
}

export function ImagePreview({ src, alt, onRemove, onClick, className = '' }: ImagePreviewProps) {
  return (
    <div className={`group relative ${className}`} onClick={onClick}>
      <img
        src={src}
        alt={alt}
        className="h-48 w-full rounded-lg border border-gray-200 object-cover dark:border-gray-700"
      />
      {onRemove && (
        <button
          onClick={onRemove}
          className="absolute top-2 right-2 rounded-full bg-red-500 p-1 text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-600"
        >
          <X size={16} />
        </button>
      )}
      <div className="absolute right-2 bottom-2 left-2">
        <div className="rounded bg-black/50 p-2 text-xs text-white">
          <code className="break-all">{src}</code>
        </div>
      </div>
    </div>
  )
}
