'use client'

import { useState, useEffect } from 'react'
import { Mail, Check, Eye, Trash2, Calendar, User, MessageCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { ContactMessage } from '../lib/supabase'

export function AdminContactTable() {
  const [messages, setMessages] = useState<ContactMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<'all' | 'new' | 'read' | 'replied'>('all')

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const response = await fetch('/api/contact/admin')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            setMessages(result.data)
          } else {
            console.error('API returned error:', result.error)
            toast.error(result.error || 'Failed to load messages')
          }
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to load messages')
        }
      } catch (error) {
        console.error('Error fetching messages:', error)
        toast.error('Failed to load messages')
      } finally {
        setLoading(false)
      }
    }

    fetchMessages()
  }, [])

  const handleStatusUpdate = async (id: string, status: ContactMessage['status']) => {
    try {
      const response = await fetch('/api/contact/admin', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, status }),
      })

      if (response.ok) {
        setMessages(messages.map((m) => (m.id === id ? { ...m, status } : m)))
        toast.success(`Message marked as ${status}`)
      } else {
        toast.error('Failed to update message')
      }
    } catch (error) {
      console.error('Error updating message:', error)
      toast.error('Error updating message')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this message?')) return

    try {
      const response = await fetch(`/api/contact/admin?id=${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setMessages(messages.filter((m) => m.id !== id))
        toast.success('Message deleted')
      } else {
        toast.error('Failed to delete message')
      }
    } catch (error) {
      console.error('Error deleting message:', error)
      toast.error('Error deleting message')
    }
  }

  const filteredMessages = messages.filter((message) => {
    if (statusFilter === 'all') return true
    return message.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
      case 'read':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
      case 'replied':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Mail className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Contact Messages</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-primary-600 mb-4"></div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Loading messages...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Mail className="h-5 w-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Contact Messages</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">{messages.length} total messages</p>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex gap-2">
          {(['all', 'new', 'read', 'replied'] as const).map((status) => {
            const count = status === 'all' ? messages.length : messages.filter((m) => m.status === status).length
            return (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                  statusFilter === status
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-600/50 text-gray-600 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-500/50'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)} ({count})
              </button>
            )
          })}
        </div>
      </div>

      {/* Messages List */}
      {filteredMessages.length === 0 ? (
        <div className="text-center py-12">
          <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No messages found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {messages.length === 0 ? 'No messages yet' : `No ${statusFilter} messages`}
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredMessages.map((message) => (
            <div key={message.id} className="border-b border-gray-200/50 dark:border-gray-700/50 pb-4 last:border-b-0 hover:bg-gray-50/50 dark:hover:bg-gray-800/50 rounded-lg px-3 py-2 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Status and Meta Info */}
                    <div className="flex items-center space-x-3 mb-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(message.status)}`}>
                        {message.status}
                      </span>
                      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <Calendar className="h-3 w-3" />
                        <span>{new Date(message.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    {/* Contact Info */}
                    <div className="mb-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">{message.name}</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">{message.email}</span>
                      </div>
                    </div>
                    
                    {/* Subject */}
                    <div className="mb-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <MessageCircle className="h-4 w-4 text-gray-400" />
                        <h4 className="font-semibold text-gray-900 dark:text-white">{message.subject}</h4>
                      </div>
                    </div>
                    
                    {/* Message Content */}
                    <div className="mb-3">
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed line-clamp-3">
                        {message.message}
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-1 ml-4">
                    {message.status === 'new' && (
                      <button
                        onClick={() => handleStatusUpdate(message.id, 'read')}
                        className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"
                        title="Mark as Read"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    )}
                    {message.status !== 'replied' && (
                      <button
                        onClick={() => handleStatusUpdate(message.id, 'replied')}
                        className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200"
                        title="Mark as Replied"
                      >
                        <Check className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(message.id)}
                      className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                      title="Delete Message"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
    </div>
  )
}
