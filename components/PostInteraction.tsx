'use client'

import { useState, useEffect } from 'react'
import LikeButton from './LikeButton'
import CommentSection from './CommentSection'
import { MessageCircle, Heart, TrendingUp } from 'lucide-react'

interface PostInteractionProps {
  postSlug: string
  className?: string
}

interface InteractionData {
  comment_count: number
  like_count: number
  user_has_liked: boolean
}

export default function PostInteraction({ postSlug, className = '' }: PostInteractionProps) {
  const [interaction, setInteraction] = useState<InteractionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadInteractionData()
  }, [postSlug])

  const loadInteractionData = async () => {
    try {
      const response = await fetch(`/api/likes?slug=${encodeURIComponent(postSlug)}`)
      if (response.ok) {
        const data = await response.json()
        setInteraction(data)
      }
    } catch (error) {
      console.error('Error loading interaction data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-8">
          <div className="animate-pulse">
            <div className="mb-8 flex items-center gap-6">
              <div className="h-12 w-32 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-24 rounded-md bg-gray-200 dark:bg-gray-700"></div>
            </div>
            <div className="space-y-4">
              <div className="h-4 w-full rounded bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-4 w-3/4 rounded bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-32 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      <div className="rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-gray-800/50 dark:to-gray-900/50 px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary-100 dark:bg-primary-900/30">
                <TrendingUp className="h-5 w-5 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Join the Discussion
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Share your thoughts and engage with the community
                </p>
              </div>
            </div>
            
            {/* Engagement Stats */}
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30">
                <Heart className={`h-4 w-4 ${interaction?.like_count ? 'text-red-500' : 'text-gray-400'}`} />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {interaction?.like_count || 0}
                </span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30">
                <MessageCircle className={`h-4 w-4 ${interaction?.comment_count ? 'text-blue-500' : 'text-gray-400'}`} />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {interaction?.comment_count || 0}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Interaction Bar */}
        <div className="px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center justify-between">
            <LikeButton
              postSlug={postSlug}
              initialCount={interaction?.like_count || 0}
              initialLiked={interaction?.user_has_liked || false}
            />
            
            <div className="text-right">
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {interaction?.comment_count || 0}{' '}
                {(interaction?.comment_count || 0) === 1 ? 'Comment' : 'Comments'}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Be the first to share your thoughts
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="p-8">
          <CommentSection postSlug={postSlug} initialCount={interaction?.comment_count || 0} />
        </div>
      </div>
    </div>
  )
}
