'use client'

import { useEffect, useState } from 'react'
import { formatDate } from 'pliny/utils/formatDate'
import siteMetadata from '@/data/siteMetadata'
import { CombinedPost } from '../lib/combined-posts'
import { remark } from 'remark'
import remarkHtml from 'remark-html'
import remarkGfm from 'remark-gfm'
import ViewTracker from './ViewTracker'
import <PERSON>Counter from './ViewCounter'
import PostInteraction from './PostInteraction'
import { StructuredData } from './StructuredData'
import { AffiliateDisclosure } from './AffiliateLink'
import Tag from './Tag'

import PostTableOfContents from './PostTableOfContents'
import PostNavigationFloat from './PostNavigationFloat'
import { useCodeCopyButtons } from './hooks/useCodeCopyButtons'
import Zoom from 'react-medium-image-zoom'
import 'react-medium-image-zoom/dist/styles.css'
import React from 'react'
import parse, { domToReact, HTMLReactParserOptions, Element } from 'html-react-parser'

interface SupabasePostRendererProps {
  post: CombinedPost
}

import { isHtmlContent } from '../lib/content-utils'

// Helper to wrap images in Zoom
function wrapImagesWithZoom(html: string) {
  // Use DOMParser to parse HTML and wrap <img> tags
  if (typeof window === 'undefined') return html // SSR fallback
  const parser = new window.DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  const images = doc.querySelectorAll('img')
  images.forEach((img) => {
    const wrapper = doc.createElement('span')
    wrapper.setAttribute('data-zoom-wrapper', 'true')
    img.parentNode?.insertBefore(wrapper, img)
    wrapper.appendChild(img)
  })
  return doc.body.innerHTML
}

function HtmlWithZoom({ html }: { html: string }) {
  const options: HTMLReactParserOptions = {
    replace: (domNode) => {
      // Handle table elements
      if (domNode instanceof Element && domNode.name === 'table') {
        return (
          <div className="table-wrapper">
            <table>
              {domToReact(domNode.children as Element[], options)}
            </table>
          </div>
        )
      }

      // Lift images out of paragraphs
      if (domNode instanceof Element && domNode.name === 'p') {
        if (
          domNode.children.length === 1 &&
          domNode.children[0] instanceof Element &&
          domNode.children[0].name === 'img'
        ) {
          const img = domNode.children[0] as Element
          const { src, alt } = img.attribs
          return (
            <span style={{ display: 'block', margin: '1.5em 0' }}>
              <Zoom>
                <img src={src} alt={alt} className="max-w-full h-auto rounded-lg shadow-sm mb-4" />
              </Zoom>
            </span>
          )
        }
      }
      if (domNode instanceof Element && domNode.name === 'img') {
        const { src, alt } = domNode.attribs
        return (
          <span style={{ display: 'inline-block' }}>
            <Zoom>
              <img src={src} alt={alt} className="max-w-full h-auto rounded-lg shadow-sm mb-4" />
            </Zoom>
          </span>
        )
      }
    },
  }
  return <>{parse(html, options)}</>
}

export default function SupabasePostRenderer({ post }: SupabasePostRendererProps) {
  const [htmlContent, setHtmlContent] = useState('')

  // Add copy buttons to code blocks
  useCodeCopyButtons()

  useEffect(() => {
    const processContent = async () => {
      if (post.content) {
        // If content is already HTML (from Rich Text Editor), use it directly
        if (isHtmlContent(post.content)) {
          setHtmlContent(post.content)
        } else {
          // Convert markdown to HTML with GFM support (including tables)
          const processedContent = await remark()
            .use(remarkGfm) // Add GitHub Flavored Markdown support including tables
            .use(remarkHtml)
            .process(post.content)
          setHtmlContent(processedContent.toString())
        }
      }
    }

    processContent()
  }, [post.content])

  return (
    <>
      {/* Navigation Components */}

      <PostTableOfContents />
      <PostNavigationFloat postTitle={post.title} />

      <article className="mx-auto max-w-3xl px-4 sm:px-6 xl:max-w-5xl xl:px-0">
        {/* SEO Structured Data */}
        <StructuredData
          type="article"
          data={{
            title: post.title,
            description: post.description || post.summary,
            url: `${siteMetadata.siteUrl}/blog/${post.slug}`,
            author: post.author,
            datePublished: new Date(post.date).toISOString(),
            tags: post.tags,
          }}
        />

        <ViewTracker slug={post.slug} source="supabase" />
        <div className="xl:divide-y xl:divide-gray-200 xl:dark:divide-gray-700">
          <header className="pt-6 xl:pb-6">
            <div className="space-y-1 text-center">
              <dl className="space-y-10">
                <div>
                  <dt className="sr-only">Published on</dt>
                  <dd className="text-base leading-6 font-medium text-gray-500 dark:text-gray-400">
                    <time dateTime={post.date}>{formatDate(post.date, siteMetadata.locale)}</time>
                  </dd>
                </div>
              </dl>
              <div>
                <h1 className="text-3xl leading-9 font-extrabold tracking-tight text-gray-900 sm:text-4xl sm:leading-10 md:text-5xl md:leading-14 dark:text-gray-100">
                  {post.title}
                </h1>
              </div>
              <div className="flex flex-wrap justify-center gap-2 pt-2">
                {post.tags.map((tag) => (
                  <Tag key={tag} text={tag} />
                ))}
              </div>
              <div className="flex justify-center space-x-4 pt-2 text-sm text-gray-500 dark:text-gray-400">
                <span>By {post.author}</span>
                <span>•</span>
                <span>{post.readTime}</span>
                {post.view_count !== undefined && (
                  <>
                    <span>•</span>
                    <span>{post.view_count} views</span>
                  </>
                )}
              </div>
            </div>
          </header>
          <div className="grid-rows-[auto_1fr] divide-y divide-gray-200 pb-8 xl:grid xl:grid-cols-4 xl:gap-x-6 xl:divide-y-0 dark:divide-gray-700">
            <div className="divide-y divide-gray-200 xl:col-span-3 xl:row-span-2 xl:pb-0 dark:divide-gray-700">
              <div className="prose prose-reading-container dark:prose-invert max-w-none pt-10 pb-8">
                {post.description && (
                  <p className="text-xl leading-8 text-gray-600 dark:text-gray-400">
                    {post.description}
                  </p>
                )}
                <div className="prose-content prose-reading">
                  <HtmlWithZoom html={htmlContent} />
                </div>

                {/* Affiliate Disclosure - Conditionally show based on post flag */}
                {post.showAffiliateDisclosure && <AffiliateDisclosure className="mt-8" />}
              </div>
              {/* Post Interaction - Likes and Comments */}
              <PostInteraction postSlug={post.slug} />
            </div>
            <footer>
              <div className="divide-gray-200 text-sm leading-5 font-medium xl:col-start-1 xl:row-start-2 xl:divide-y dark:divide-gray-700">
                <div className="py-4 xl:py-8">
                  <h2 className="text-xs tracking-wide text-gray-500 uppercase dark:text-gray-400">
                    Author
                  </h2>
                  <div className="text-gray-900 dark:text-gray-100">{post.author}</div>
                </div>
                <div className="py-4 xl:py-8">
                  <h2 className="text-xs tracking-wide text-gray-500 uppercase dark:text-gray-400">
                    Published
                  </h2>
                  <div className="text-gray-900 dark:text-gray-100">
                    {formatDate(post.date, siteMetadata.locale)}
                  </div>
                </div>
                {post.tags.length > 0 && (
                  <div className="py-4 xl:py-8">
                    <h2 className="text-xs tracking-wide text-gray-500 uppercase dark:text-gray-400">
                      Tags
                    </h2>
                    <div className="flex flex-wrap">
                      {post.tags.map((tag) => (
                        <Tag key={tag} text={tag} />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </footer>
          </div>
        </div>
      </article>
    </>
  )
}
