'use client'

import React, { useState } from 'react'
import { Shield, Sparkles, ArrowR<PERSON>, Loader } from 'lucide-react'

interface AdminAuthProps {
  onLogin?: (success: boolean) => void
}

export function AdminAuth({ onLogin }: AdminAuthProps) {
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!password) {
      setError('Please enter your password')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      })

      const result = await response.json()

      if (response.ok) {
        onLogin?.(true)
      } else {
        setError(result.error || 'Authentication failed')
        onLogin?.(false)
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('Network error occurred')
      onLogin?.(false)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
  return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-950 dark:via-gray-900/50 dark:to-primary-900/20">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-warm opacity-5">
          <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="auth-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
                <rect x="15" y="15" width="8" height="8" rx="2" fill="currentColor" opacity="0.4"/>
                <circle cx="45" cy="15" r="3" fill="currentColor" opacity="0.3"/>
                <polygon points="15,45 23,45 19,37" fill="currentColor" opacity="0.2"/>
                <rect x="35" y="35" width="16" height="6" rx="3" fill="currentColor" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#auth-pattern)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-28 h-28 bg-blue-200/20 dark:bg-blue-800/20 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-200/20 dark:bg-purple-800/20 rounded-full blur-xl animate-float-delayed"></div>
          <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-indigo-200/20 dark:bg-indigo-800/20 rounded-full blur-xl animate-float" style={{ animationDelay: '1.5s' }}></div>
          <div className="absolute bottom-1/3 left-1/4 w-24 h-24 bg-pink-200/20 dark:bg-pink-800/20 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative z-10 text-center">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-primary rounded-3xl shadow-xl mb-6">
            <Shield className="w-10 h-10 text-white animate-pulse" />
          </div>
          <h3 className="text-xl font-semibold mb-2">
            <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
              Verifying Access
            </span>
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Please wait while we authenticate your session...
          </p>
          <div className="flex items-center justify-center">
            <Loader className="w-6 h-6 text-primary-500 animate-spin" />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-950 dark:via-gray-900/50 dark:to-primary-900/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-warm opacity-5">
        <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="auth-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
              <rect x="15" y="15" width="8" height="8" rx="2" fill="currentColor" opacity="0.4"/>
              <circle cx="45" cy="15" r="3" fill="currentColor" opacity="0.3"/>
              <polygon points="15,45 23,45 19,37" fill="currentColor" opacity="0.2"/>
              <rect x="35" y="35" width="16" height="6" rx="3" fill="currentColor" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#auth-pattern)" />
        </svg>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-28 h-28 bg-blue-200/20 dark:bg-blue-800/20 rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-200/20 dark:bg-purple-800/20 rounded-full blur-xl animate-float-delayed"></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-indigo-200/20 dark:bg-indigo-800/20 rounded-full blur-xl animate-float" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-1/3 left-1/4 w-24 h-24 bg-pink-200/20 dark:bg-pink-800/20 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="w-full max-w-md px-8 py-12 mx-4 sm:mx-0">
        {/* Main auth card */}
        <div className="relative">
          {/* Card background with glassmorphism */}
          <div className="absolute inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl rounded-3xl border border-white/40 dark:border-gray-700/40 shadow-2xl"></div>
          
          <div className="relative p-8 sm:p-10">
            {/* Logo section */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-primary rounded-3xl shadow-2xl mb-6 relative">
                <Shield className="w-10 h-10 text-white" />
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-3 h-3 text-white" />
                </div>
        </div>

              <h1 className="text-3xl font-bold mb-3">
                <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                  Admin Access
                </span>
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Enter your credentials to access the admin dashboard
              </p>
            </div>

            {/* Auth form */}
        <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <label 
                  htmlFor="password" 
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
              Password
            </label>
            <div className="relative">
              <input
                    id="password"
                    type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-600 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                placeholder="Enter admin password"
                required
                  />
                </div>
              </div>

              {error && (
                <div className="p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-700 rounded-xl">
                  <p className="text-sm text-red-700 dark:text-red-400 font-medium">
                    {error}
                  </p>
                </div>
              )}

              <button
                type="submit"
                disabled={loading || !password}
                className="w-full group bg-gradient-primary hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed px-6 py-4 rounded-xl text-white font-semibold transition-all duration-300 transform hover:scale-105 disabled:transform-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                <span className="flex items-center justify-center">
                  {loading ? (
                    <>
                      <Loader className="w-5 h-5 mr-2 animate-spin" />
                      Authenticating...
                    </>
                  ) : (
                    <>
                      Sign In
                      <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                    </>
                  )}
                </span>
              </button>
            </form>

            {/* Security badge */}
            <div className="mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50">
              <div className="flex items-center justify-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                <span>🔒 Secured with enterprise-grade encryption</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}



