'use client'

import { useRef, useState } from 'react'
import { Copy, Check, Download, Maximize2, Minimize2 } from 'lucide-react'

interface PreProps {
  children: React.ReactElement<{ children?: string }>
  className?: string
  'data-language'?: string
  'data-theme'?: string
  title?: string
  filename?: string
  raw?: string
}

export default function Pre({
  children,
  className = '',
  'data-language': language,
  'data-theme': theme,
  title,
  filename,
  raw,
  ...props
}: PreProps) {
  const [copied, setCopied] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const preRef = useRef<HTMLPreElement>(null)
  const textInput = useRef<HTMLDivElement>(null)

  const onCopy = async () => {
    if (preRef.current?.textContent) {
      try {
        await navigator.clipboard.writeText(preRef.current.textContent)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy:', err)
      }
    }
  }

  const onDownload = () => {
    if (preRef.current?.textContent) {
      const element = document.createElement('a')
      const file = new Blob([preRef.current.textContent], { type: 'text/plain' })
      element.href = URL.createObjectURL(file)
      element.download = filename || `code.${language || 'txt'}`
      document.body.appendChild(element)
      element.click()
      document.body.removeChild(element)
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  // Extract code content from children
  const codeContent = children?.props?.children || ''
  const lines = typeof codeContent === 'string' ? codeContent.trim().split('\n') : []

  return (
    <div
      className={`group relative my-6 overflow-hidden rounded-xl bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-0.5 shadow-lg transition-all hover:shadow-xl hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 animate-gradient-x ${
        isFullscreen
          ? 'fixed inset-4 z-50 h-[calc(100vh-2rem)] w-[calc(100vw-2rem)]'
          : ''
      }`}
    >
      <div className="relative overflow-hidden rounded-[10px] bg-white shadow-lg dark:bg-gray-900">
        {/* Enhanced Header */}
        <div className="flex items-center justify-between bg-gradient-to-r from-slate-50 to-slate-100 px-4 py-3 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center space-x-4">
            {/* macOS-style window controls */}
            <div className="flex space-x-2">
              <div className="h-3 w-3 rounded-full bg-gradient-to-br from-pink-500 via-red-500 to-yellow-400 animate-dot-glow shadow-dot-glow-red transition-all"></div>
              <div className="h-3 w-3 rounded-full bg-gradient-to-br from-yellow-400 via-green-400 to-teal-400 animate-dot-glow shadow-dot-glow-yellow transition-all"></div>
              <div className="h-3 w-3 rounded-full bg-gradient-to-br from-green-400 via-blue-400 to-purple-500 animate-dot-glow shadow-dot-glow-green transition-all"></div>
            </div>
            
            {/* File info */}
            <div className="flex items-center space-x-2">
              {language && (
                <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                  {language}
                </span>
              )}
              {(title || filename) && (
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {title || filename}
                </span>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onDownload}
              className="rounded-md p-2 text-gray-500 transition-colors hover:bg-gray-200 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
              title="Download code"
            >
              <Download className="h-4 w-4" />
            </button>
            
            <button
              onClick={toggleFullscreen}
              className="rounded-md p-2 text-gray-500 transition-colors hover:bg-gray-200 hover:text-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
              title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
            >
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </button>

            <button
              onClick={onCopy}
              className="flex items-center space-x-2 rounded-md bg-white px-3 py-2 text-sm font-medium text-gray-600 shadow-sm transition-all hover:bg-gray-50 hover:shadow-md dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
              title={copied ? 'Copied!' : 'Copy code'}
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">Copied!</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Code container with enhanced styling */}
        <div
          className={`relative overflow-auto ${
            isFullscreen ? 'h-[calc(100%-4rem)]' : 'max-h-[600px]'
          }`}
          ref={textInput}
        >
          <pre
            ref={preRef}
            className={`relative block overflow-x-auto bg-gradient-to-br from-gray-900 via-slate-900 to-blue-900/5 p-6 text-sm leading-relaxed dark:from-gray-950 dark:via-gray-900 dark:to-indigo-950/20 ${className}`}
            style={{
              fontFamily: '"Fira Code", "JetBrains Mono", "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace',
              fontSize: '14px',
              lineHeight: '1.6',
            }}
            {...props}
          >
            {children}
          </pre>

          {/* Line counter overlay */}
          {lines.length > 0 && (
            <div className="absolute left-0 top-0 flex flex-col border-r border-gray-700/50 bg-gray-800/30 p-6 text-right backdrop-blur-sm">
              {lines.map((_, index) => (
                <span
                  key={index}
                  className="block h-[1.6em] w-8 text-xs leading-relaxed text-gray-500 dark:text-gray-400"
                >
                  {index + 1}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Fullscreen overlay */}
      {isFullscreen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm"
          onClick={toggleFullscreen}
        />
      )}
    </div>
  )
}
