'use client'

import React, { useState, useEffect } from 'react'
import {
  Home,
  Plus,
  BarChart3,
  Settings,
  ArrowLeft,
  LogOut,
  FolderOpen,
  MessageCircle,
  RefreshCw,
  Mail,
  Users,
  Shield,
  Activity,
  Zap,
  TrendingUp,
} from 'lucide-react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { AdminPostsTable } from './AdminPostsTable'
import { BlogPostForm } from './BlogPostForm'
import { AdminAnalytics } from './AdminAnalytics'
import { AdminProjectsTable } from './AdminProjectsTable'
import { ProjectForm } from './ProjectForm'
import PostPreviewModal from './PostPreviewModal'
import { AdminCommentsTable } from './AdminCommentsTable'
import { AdminContactTable } from './AdminContactTable'
import { useAdminSession } from './hooks/useAdminSession'
import { useAdminNotifications } from './hooks/useAdminNotifications'
import AdminNotificationBell from './AdminNotificationBell'
import AdminNotificationSummary from './AdminNotificationSummary'
import { AdminNewsletterTable } from './AdminNewsletterTable'

import { BlogPost, Project } from '../lib/supabase'

type TabType =
  | 'posts'
  | 'create'
  | 'projects'
  | 'comments'
  | 'contact'
  | 'newsletter'
  | 'analytics'
  | 'settings'

interface AdminDashboardProps {
  onLogout?: () => void
}

export function AdminDashboard({ onLogout }: AdminDashboardProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get active tab from URL search params, default to 'posts'
  const getActiveTabFromUrl = (): TabType => {
    const tabParam = searchParams.get('tab')
    const validTabs: TabType[] = [
      'posts',
      'create',
      'projects',
      'comments',
      'contact',
      'newsletter',
      'analytics',
      'settings',
    ]
    return (validTabs.includes(tabParam as TabType) ? tabParam : 'posts') as TabType
  }

  const [activeTab, setActiveTab] = useState<TabType>(getActiveTabFromUrl())
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [showProjectForm, setShowProjectForm] = useState(false)
  const [previewPost, setPreviewPost] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [postsRefreshKey, setPostsRefreshKey] = useState(0)

  // Error handler to capture and display errors
  const addError = (error: string) => {
    setErrors(prev => [...prev, `${new Date().toLocaleTimeString()}: ${error}`])
  }

  // Capture global errors
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      addError(`Global Error: ${event.message}`)
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      addError(`Unhandled Promise Rejection: ${event.reason}`)
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // Update activeTab when URL changes
  useEffect(() => {
    setActiveTab(getActiveTabFromUrl())
  }, [searchParams])

  // Function to update URL when tab changes
  const updateTabInUrl = (tabId: TabType) => {
    if (typeof window !== 'undefined') {
      try {
        const url = new URL(window.location.href)
        url.searchParams.set('tab', tabId)
        router.replace(url.pathname + url.search, { scroll: false })
      } catch (error) {
        // Fallback if URL parsing fails
        console.warn('URL parsing failed, using fallback method:', error)
        router.replace(`/admin?tab=${tabId}`, { scroll: false })
      }
    }
  }

  // Use the session hook for real-time session management
  const { refreshSession } = useAdminSession({
    showRefreshToasts: true,
    checkInterval: 1 * 60 * 1000, // Check every minute for dashboard
  })

  // Use notifications hook
  const { counts, markAsChecked } = useAdminNotifications()

  const tabs = [
    { id: 'posts', label: 'Posts', icon: Home },
    { id: 'create', label: 'Create', icon: Plus },
    { id: 'projects', label: 'Projects', icon: FolderOpen },
    {
      id: 'comments',
      label: 'Comments',
      icon: MessageCircle,
      badge: counts.pendingComments > 0 ? counts.pendingComments : undefined,
    },
    {
      id: 'contact',
      label: 'Contact',
      icon: Mail,
      badge: counts.newContacts > 0 ? counts.newContacts : undefined,
    },
    { id: 'newsletter', label: 'Newsletter', icon: Users },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings },
  ]

  const handleEditPost = (post: BlogPost) => {
    setEditingPost(post)
    setActiveTab('create')
    updateTabInUrl('create')
  }

  const handlePostSuccess = () => {
    setEditingPost(null)
    setActiveTab('posts')
    updateTabInUrl('posts')
    // Trigger refresh of posts
    handlePostChanged()
  }

  const handlePostCancel = () => {
    setEditingPost(null)
    setActiveTab('posts')
    updateTabInUrl('posts')
  }

  // Add function to handle post changes
  const handlePostChanged = () => {
    // Force refresh AdminPostsTable by updating key
    setPostsRefreshKey(prev => prev + 1)
  }

  const handlePreview = (post: any) => {
    setPreviewPost(post)
    setShowPreview(true)
  }

  const handleClosePreview = () => {
    setShowPreview(false)
    setPreviewPost(null)
  }

  const handleEditProject = (project: Project) => {
    setEditingProject(project)
    setShowProjectForm(true)
  }

  const handleCreateProject = () => {
    setEditingProject(null)
    setShowProjectForm(true)
  }

  const handleProjectSave = () => {
    setShowProjectForm(false)
    setEditingProject(null)
  }

  const handleProjectCancel = () => {
    setShowProjectForm(false)
    setEditingProject(null)
  }

  const handleLogout = async () => {
    if (onLogout) {
      await onLogout()
    }
  }

  const handleRefreshSession = async () => {
    await refreshSession()
  }

  return (
    <div className="relative">
      {/* Admin Header */}
      <div className="mb-8 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl p-6">
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="h-10 w-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold sm:text-3xl">
                <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                  Admin Dashboard
                </span>
                </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage your content and analytics
              </p>
            </div>
              </div>

          {/* Desktop admin controls */}
          <div className="flex items-center space-x-4">
            {/* Session status */}
            <div className="flex items-center space-x-2 px-3 py-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200/50 dark:border-gray-600/50 shadow-sm">
              <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"></div>
              <span className="text-xs font-medium text-emerald-600 dark:text-emerald-400">
                Active
              </span>
            </div>

              {/* Notification Bell */}
              <AdminNotificationBell />

              {/* Logout Button */}
              <button
                onClick={handleLogout}
              className="inline-flex cursor-pointer items-center rounded-xl border border-red-200/50 dark:border-red-700/50 bg-red-50/80 dark:bg-red-900/30 backdrop-blur-sm px-4 py-2 text-sm font-medium text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/50 transition-all duration-200 group shadow-lg hover:shadow-xl"
              >
              <LogOut className="mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
                Logout
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
        <nav className="mt-6">
            {/* Mobile navigation - horizontal scroll */}
          <div className="flex space-x-1 overflow-x-auto px-1 pb-3 sm:hidden no-scrollbar">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const hasBadge = tab.badge !== undefined && tab.badge > 0
              const isActive = activeTab === tab.id
                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      const newTab = tab.id as TabType
                      setActiveTab(newTab)
                      updateTabInUrl(newTab)
                      if (tab.id !== 'create') {
                        setEditingPost(null)
                      }
                      if (tab.id === 'comments') {
                        markAsChecked('comments')
                      } else if (tab.id === 'contact') {
                        markAsChecked('contacts')
                      }
                    }}
                  className={`relative flex min-w-fit flex-col items-center justify-center rounded-xl px-4 py-3 text-xs font-medium whitespace-nowrap transition-all duration-200 cursor-pointer backdrop-blur-sm ${
                    isActive
                      ? 'bg-gradient-primary text-white shadow-lg transform scale-105 border border-primary-300/30'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-white/60 dark:hover:bg-gray-800/60 hover:text-primary-600 dark:hover:text-primary-400 border border-transparent hover:border-white/30 dark:hover:border-gray-700/30'
                    }`}
                  >
                    <div className="relative mb-1">
                      <Icon className="h-4 w-4" />
                      {hasBadge && tab.badge && (
                      <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white shadow-sm">
                          {tab.badge > 9 ? '9+' : tab.badge}
                        </span>
                      )}
                    </div>
                    <span className="text-center leading-tight">{tab.label}</span>
                  </button>
                )
              })}
            </div>

            {/* Desktop navigation */}
          <div className="hidden space-x-1 sm:flex">
              {tabs.map((tab) => {
                const Icon = tab.icon
                const hasBadge = tab.badge !== undefined && tab.badge > 0
              const isActive = activeTab === tab.id
                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      const newTab = tab.id as TabType
                      setActiveTab(newTab)
                      updateTabInUrl(newTab)
                      if (tab.id !== 'create') {
                        setEditingPost(null)
                      }
                      if (tab.id === 'comments') {
                        markAsChecked('comments')
                      } else if (tab.id === 'contact') {
                        markAsChecked('contacts')
                      }
                    }}
                  className={`relative flex items-center space-x-2 px-4 py-3 rounded-t-xl text-sm font-medium transition-all duration-200 border-b-2 cursor-pointer backdrop-blur-sm ${
                    isActive
                      ? 'bg-gradient-primary text-white border-primary-500 shadow-lg transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-white/60 dark:hover:bg-gray-800/60 hover:text-primary-600 dark:hover:text-primary-400 border-transparent hover:shadow-md'
                    }`}
                  >
                    <div className="relative">
                      <Icon className="h-5 w-5" />
                      {hasBadge && tab.badge && (
                      <span className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-semibold text-white shadow-sm">
                          {tab.badge > 99 ? '99+' : tab.badge}
                        </span>
                      )}
                    </div>
                    <span className="font-medium">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </nav>
        </div>

      {/* Content */}
        <div className="space-y-6">
        {/* Error Display */}
        {errors.length > 0 && (
          <div className="bg-red-50/90 dark:bg-red-900/30 backdrop-blur-sm border-l-4 border-red-400 p-4 rounded-lg shadow-lg border border-red-200/50 dark:border-red-700/50">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                Debug Errors ({errors.length})
              </h3>
              <button
                onClick={() => setErrors([])}
                className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm font-medium"
              >
                Clear
              </button>
            </div>
            <div className="max-h-40 overflow-y-auto">
              {errors.slice(-10).map((error, index) => (
                <div key={index} className="text-sm text-red-700 dark:text-red-300 mb-1 font-mono">
                  {error}
                </div>
              ))}
            </div>
          </div>
        )}

          {activeTab === 'posts' && (
            <div className="space-y-6">
              <AdminNotificationSummary />
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminPostsTable 
                key={postsRefreshKey}
                onEditPost={handleEditPost} 
                onPostChanged={handlePostChanged} 
              />
            </div>
            </div>
          )}

          {activeTab === 'create' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
            <BlogPostForm
              editingPost={editingPost}
              onSuccess={handlePostSuccess}
              onPreview={handlePreview}
              onCancel={handlePostCancel}
            />
          </div>
          )}

          {activeTab === 'projects' && !showProjectForm && (
            <div className="space-y-4">
              <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <h2 className="text-2xl font-bold mb-1">
                  <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                  Projects
                  </span>
                </h2>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Manage your portfolio projects
                </p>
              </div>
                <button
                  onClick={handleCreateProject}
                className="inline-flex cursor-pointer items-center justify-center rounded-xl bg-gradient-primary hover:shadow-lg text-white px-6 py-3 text-sm font-semibold shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:scale-105 group backdrop-blur-sm border border-primary-300/30"
                >
                <Plus className="mr-2 h-4 w-4 group-hover:rotate-90 transition-transform duration-200" />
                  Create Project
                </button>
              </div>
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminProjectsTable onEdit={handleEditProject} />
            </div>
            </div>
          )}

          {activeTab === 'projects' && showProjectForm && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
            <ProjectForm
              project={editingProject}
              onSave={handleProjectSave}
              onCancel={handleProjectCancel}
            />
          </div>
          )}

          {activeTab === 'comments' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminCommentsTable />
            </div>
          )}

          {activeTab === 'contact' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminContactTable />
            </div>
          )}

          {activeTab === 'newsletter' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminNewsletterTable />
            </div>
          )}

          {activeTab === 'analytics' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
              <AdminAnalytics />
            </div>
          )}

          {activeTab === 'settings' && (
          <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/20 shadow-xl">
            <div className="p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold mb-2">
                  <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                    Settings
                  </span>
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Admin settings and configuration options
              </p>
              </div>

              {/* Session Information */}
              <div className="bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-800/50 dark:via-gray-700/50 dark:to-primary-900/20 rounded-xl p-6 border border-gray-100 dark:border-gray-600">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="h-10 w-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Session Information
                </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Current admin session details
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Auto-refresh</span>
                      <div className="flex items-center space-x-2">
                        <Zap className="w-3 h-3 text-emerald-500" />
                        <span className="text-sm font-semibold text-emerald-600 dark:text-emerald-400">Enabled</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-600">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</span>
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="w-3 h-3 text-primary-600 dark:text-primary-400" />
                        <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">Active</span>
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={handleRefreshSession}
                  className="inline-flex cursor-pointer items-center rounded-xl bg-gradient-primary hover:shadow-lg text-white px-4 py-2 text-sm font-medium shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:scale-105 group backdrop-blur-sm border border-primary-300/30"
                >
                  <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
                  Refresh Session
                </button>
              </div>
              </div>
            </div>
          )}

      {/* Preview Modal */}
      {showPreview && previewPost && (
        <PostPreviewModal isOpen={showPreview} post={previewPost} onClose={handleClosePreview} />
      )}
      </div>
    </div>
  )
}
