'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'
import { Save, FileText, Eye, X, Plus, Image as ImageIcon, ArrowLeft } from 'lucide-react'
import { BlogPost } from '../lib/supabase'
import ImageUpload, { ImagePreview } from './ImageUpload'
import RichTextEditor from './RichTextEditor'
import { isHtmlContent } from '../lib/content-utils'

interface BlogPostFormProps {
  editingPost?: BlogPost | null
  onSuccess?: () => void
  onPreview?: (post: any) => void
  onCancel?: () => void
}

// Common blog tags for suggestions
const SUGGESTED_TAGS = [
  'technology',
  'programming',
  'javascript',
  'react',
  'nextjs',
  'web-development',
  'tutorial',
  'guide',
  'tips',
  'best-practices',
  'career',
  'productivity',
  'design',
  'ui-ux',
  'frontend',
  'backend',
  'fullstack',
  'database',
  'ai',
  'machine-learning',
  'devops',
  'testing',
  'security',
  'performance',
]

export function BlogPostForm({ editingPost, onSuccess, onPreview, onCancel }: BlogPostFormProps) {
  const [formData, setFormData] = useState({
    title: editingPost?.title || '',
    slug: editingPost?.slug || '',
    content: editingPost?.content || '',
    description: editingPost?.description || '',
    author: editingPost?.author || 'Admin',
    tags: editingPost?.tags || [],
    readTime: editingPost?.readTime || '5 min read',
    status: (editingPost?.status as 'published' | 'draft' | 'disabled') || 'draft',
    showAffiliateDisclosure: editingPost?.showAffiliateDisclosure || false,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tagInput, setTagInput] = useState('')
  const [showTagSuggestions, setShowTagSuggestions] = useState(false)
  const [showImageUpload, setShowImageUpload] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<string[]>([])

  // Set initial editor type based on existing content
  const getInitialEditorType = (): 'markdown' | 'rich' => {
    if (editingPost?.content) {
      return isHtmlContent(editingPost.content) ? 'rich' : 'markdown'
    }
    return 'rich' // Default to rich text for new posts
  }

  const [editorType, setEditorType] = useState<'markdown' | 'rich'>(getInitialEditorType())

  const handleSubmit = async (e: React.FormEvent, isDraft = false) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const submitData: any = {
        ...formData,
        status: isDraft ? 'draft' : formData.status,
        tags: formData.tags.join(', '), // Convert array back to string for API
        contentType: editorType, // Track which editor was used
      }

      // Debug logging
      console.log('Submitting post data:', {
        showAffiliateDisclosure: submitData.showAffiliateDisclosure,
        formData: formData.showAffiliateDisclosure
      })

      // If editing, include the post ID and use PUT method
      if (editingPost?.id) {
        submitData.id = editingPost.id
      }

      const response = await fetch('/api/posts', {
        method: editingPost?.id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (response.ok) {
        const savedPost = await response.json()
        if (editingPost) {
          toast.success(`Post ${isDraft ? 'saved as draft' : 'updated'}!`)
        } else {
          toast.success(`Post ${isDraft ? 'saved as draft' : 'created'}!`)
        }
        
        // Clear cache to ensure immediate updates on frontend
        if (typeof window !== 'undefined') {
          // Force cache refresh for posts API
          try {
            await fetch('/api/posts', { cache: 'no-store' })
          } catch (e) {
            // Ignore cache refresh errors
          }
        }
        
        if (!editingPost) {
          setFormData({
            title: '',
            slug: '',
            content: '',
            description: '',
            author: 'Admin',
            tags: [],
            readTime: '5 min read',
            status: 'draft',
            showAffiliateDisclosure: false,
          })
        }
        onSuccess?.()
      } else {
        const errorData = await response.json()
        if (errorData.details) {
          toast.error(errorData.error)
          console.log('Setup instructions:', errorData.details)
        } else {
          throw new Error('Failed to save post')
        }
      }
    } catch (error) {
      toast.error('Failed to save post')
      console.error('Error saving post:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePreview = () => {
    const previewData = {
      ...formData,
      date: editingPost?.date || new Date().toISOString().split('T')[0],
      id: editingPost?.id || 'preview',
      view_count: editingPost?.view_count || 0,
      path: `blog/${formData.slug || 'preview'}`,
      slug: formData.slug || 'preview',
      created_at: editingPost?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    onPreview?.(previewData)
  }

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase()
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, trimmedTag],
      }))
    }
    setTagInput('')
    setShowTagSuggestions(false)
  }

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }))
  }

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      if (tagInput.trim()) {
        addTag(tagInput)
      }
    }
  }

  const filteredSuggestions = SUGGESTED_TAGS.filter(
    (tag) => tag.toLowerCase().includes(tagInput.toLowerCase()) && !formData.tags.includes(tag)
  )

  return (
    <div className="rounded-lg bg-white shadow dark:bg-gray-800">

      <div className="border-b border-gray-200 px-4 py-3 sm:px-6 sm:py-4 dark:border-gray-700">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900 sm:text-xl dark:text-white">
              {editingPost ? 'Edit Post' : 'Create New Post'}
            </h2>
          </div>
          <div className="flex items-center space-x-2">
            {formData.title && formData.content && (
              <button
                type="button"
                onClick={handlePreview}
                className="inline-flex cursor-pointer items-center justify-center rounded-md bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 hover:from-blue-600 hover:to-indigo-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none sm:px-5 sm:py-2.5"
              >
                <Eye className="mr-2 h-4 w-4" />
                <span>Preview Post</span>
              </button>
            )}
            {!formData.title || !formData.content ? (
              <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                Add title and content to preview
              </div>
            ) : null}
          </div>
        </div>
      </div>

      <form onSubmit={(e) => handleSubmit(e)} className="space-y-4 p-4 sm:space-y-6 sm:p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Title
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => {
                const newTitle = e.target.value
                const generatedSlug = newTitle
                  .toLowerCase()
                  .replace(/[^a-z0-9\s-]/g, '')
                  .replace(/\s+/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-+|-+$/g, '')

                setFormData((prev) => ({
                  ...prev,
                  title: newTitle,
                  // Auto-update slug only if it's empty or matches the previous title's slug
                  slug:
                    !prev.slug ||
                    prev.slug ===
                      prev.title
                        .toLowerCase()
                        .replace(/[^a-z0-9\s-]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .replace(/^-+|-+$/g, '')
                      ? generatedSlug
                      : prev.slug,
                }))
              }}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Enter post title"
              required
            />
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Slug (URL)
            </label>
            <input
              type="text"
              value={formData.slug}
              onChange={(e) => setFormData((prev) => ({ ...prev, slug: e.target.value }))}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="url-friendly-slug"
            />
          </div>
        </div>

        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
            rows={3}
            className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            placeholder="Brief description"
            required
          />
        </div>

        {/* Tags Section */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Tags
          </label>

          {/* Selected Tags */}
          {formData.tags.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {formData.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="ml-2 inline-flex h-4 w-4 cursor-pointer items-center justify-center rounded-full text-blue-400 transition-colors duration-200 hover:bg-blue-200 hover:text-blue-600 focus:outline-none dark:text-blue-300 dark:hover:bg-blue-800"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}

          {/* Tag Input */}
          <div className="relative">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => {
                setTagInput(e.target.value)
                setShowTagSuggestions(e.target.value.length > 0)
              }}
              onKeyDown={handleTagInputKeyDown}
              onFocus={() => setShowTagSuggestions(tagInput.length > 0)}
              onBlur={() => setTimeout(() => setShowTagSuggestions(false), 150)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Add tags (press Enter or comma to add)"
            />

            {/* Tag Suggestions */}
            {showTagSuggestions && filteredSuggestions.length > 0 && (
              <div className="absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md border border-gray-300 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700">
                {filteredSuggestions.slice(0, 8).map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => addTag(tag)}
                    className="block w-full cursor-pointer px-3 py-2 text-left text-sm text-gray-700 transition-colors duration-200 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        <div>
          <div className="mb-4 flex items-center justify-between">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Content
            </label>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => setEditorType('markdown')}
                  className={`cursor-pointer rounded-md px-3 py-1 text-xs font-medium transition-colors duration-200 ${
                    editorType === 'markdown'
                      ? 'border border-blue-300 bg-blue-100 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                      : 'border border-gray-300 bg-gray-100 text-gray-600 hover:bg-gray-200 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  Markdown
                </button>
                <button
                  type="button"
                  onClick={() => setEditorType('rich')}
                  className={`cursor-pointer rounded-md px-3 py-1 text-xs font-medium transition-colors duration-200 ${
                    editorType === 'rich'
                      ? 'border border-blue-300 bg-blue-100 text-blue-700 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                      : 'border border-gray-300 bg-gray-100 text-gray-600 hover:bg-gray-200 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  Rich Text
                </button>
              </div>
              {editorType === 'markdown' && (
                <button
                  type="button"
                  onClick={() => setShowImageUpload(!showImageUpload)}
                  className="inline-flex cursor-pointer items-center rounded-md border border-blue-200 bg-blue-50 px-3 py-1 text-xs font-medium text-blue-600 transition-colors duration-200 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30"
                >
                  <ImageIcon className="mr-1 h-3 w-3" />
                  {showImageUpload ? 'Hide' : 'Add'} Images
                </button>
              )}
            </div>
          </div>

          {editorType === 'markdown' && showImageUpload && (
            <div className="mb-4 rounded-lg border border-gray-200 p-4 dark:border-gray-700">
              <ImageUpload
                onImageUploaded={(url) => {
                  setUploadedImages((prev) => [...prev, url])
                  // Auto-insert markdown at the end of content
                  const imageMarkdown = `\n\n![Image](${url})\n`
                  setFormData((prev) => ({
                    ...prev,
                    content: prev.content + imageMarkdown,
                  }))
                }}
                folder="blog-images"
                className="mb-4"
              />

              {uploadedImages.length > 0 && (
                <div>
                  <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Uploaded Images (click to copy markdown):
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {uploadedImages.map((url, index) => (
                      <ImagePreview
                        key={index}
                        src={url}
                        alt={`Uploaded image ${index + 1}`}
                        className="cursor-pointer"
                        onRemove={() => {
                          setUploadedImages((prev) => prev.filter((_, i) => i !== index))
                        }}
                        onClick={() => {
                          const markdown = `![Image](${url})`
                          navigator.clipboard.writeText(markdown)
                          toast.success('Markdown copied to clipboard!')
                        }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {editorType === 'rich' ? (
            <RichTextEditor
              content={formData.content}
              onChange={(content) => setFormData((prev) => ({ ...prev, content }))}
              placeholder="Start writing your amazing content..."
              className="w-full"
            />
          ) : (
            <textarea
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              rows={15}
              className="w-full rounded-md border border-gray-300 px-3 py-2 font-mono text-sm shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Write your post content in Markdown..."
              required
            />
          )}
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Status
            </label>
            <select
              value={formData.status}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  status: e.target.value as 'published' | 'draft' | 'disabled',
                }))
              }
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="disabled">Disabled</option>
            </select>
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Author
            </label>
            <input
              type="text"
              value={formData.author}
              onChange={(e) => setFormData((prev) => ({ ...prev, author: e.target.value }))}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Author name"
            />
          </div>
          <div className="sm:col-span-2 lg:col-span-1">
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Reading Time
            </label>
            <input
              type="text"
              value={formData.readTime}
              onChange={(e) => setFormData((prev) => ({ ...prev, readTime: e.target.value }))}
              className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="5 min read"
            />
          </div>
        </div>

        {/* Affiliate Disclosure Toggle */}
        <div className="border-t border-gray-200 pt-4 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show Affiliate Disclosure
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Display affiliate disclosure notice at the end of this post
              </p>
            </div>
            <label className="relative inline-flex cursor-pointer items-center">
              <input
                type="checkbox"
                checked={formData.showAffiliateDisclosure}
                onChange={(e) => {
                  console.log('Affiliate disclosure toggled:', e.target.checked)
                  setFormData((prev) => ({ ...prev, showAffiliateDisclosure: e.target.checked }))
                }}
                className="peer sr-only"
              />
              <div className="peer h-6 w-11 rounded-full bg-gray-200 peer-checked:bg-blue-600 peer-focus:ring-4 peer-focus:ring-blue-300 peer-focus:outline-none after:absolute after:top-[2px] after:left-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"></div>
            </label>
          </div>
          {/* Debug info */}
          <div className="mt-2 text-xs text-gray-500">
            Current value: {formData.showAffiliateDisclosure ? 'true' : 'false'}
          </div>
        </div>

        <div className="flex flex-col space-y-3 sm:flex-row sm:justify-between sm:space-y-0">
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
            {editingPost && onCancel && (
              <button
                type="button"
                onClick={onCancel}
                disabled={isSubmitting}
                className="inline-flex cursor-pointer items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors duration-200 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Cancel
              </button>
            )}

            <button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              disabled={isSubmitting}
              className="inline-flex cursor-pointer items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors duration-200 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            >
              <Save className="mr-2 h-4 w-4" />
              {isSubmitting ? 'Saving...' : 'Save as Draft'}
            </button>
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex cursor-pointer items-center justify-center rounded-md border border-transparent bg-blue-600 px-6 py-3 text-sm font-medium text-white shadow-sm transition-colors duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 sm:text-base"
          >
            <Save className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
            {isSubmitting ? 'Saving...' : formData.status === 'published' ? 'Publish' : 'Save'}
          </button>
        </div>
      </form>
    </div>
  )
}
