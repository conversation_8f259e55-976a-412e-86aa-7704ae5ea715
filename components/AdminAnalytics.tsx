'use client'

import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>hart<PERSON>, 
  TrendingUp, 
  Users, 
  MessageCircle, 
  Heart, 
  Eye, 
  Mail,
  Calendar,
  ArrowUp,
  <PERSON>Down,
  RefreshCw
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalPosts: number
    totalViews: number
    avgViews: number
    avgViewsPublished: number
    publishedPosts: number
    draftPosts: number
    recentPosts: number
    totalComments: number
    recentComments: number
    avgCommentsPerPost: number
    commentApprovalRate: string
    totalContactMessages: number
    newContactMessages: number
    recentContactMessages: number
  }
  postsByStatus: {
    published: number
    draft: number
    disabled: number
  }
  topPosts: Array<{
    id: string
    title: string
    slug: string
    views: number
    date: string
    tags: string[]
  }>
  viewsByMonth: Array<{
    month: string
    views: number
  }>
  popularTags: Array<{
    tag: string
    count: number
  }>
  performanceMetrics: {
    postsWithViews: number
    postsWithoutViews: number
    highPerformingPosts: number
    engagementRate: string
  }
  recentActivity: Array<{
    id: string
    title: string
    status: string
    date: string
    views: number
  }>
  commentAnalytics: {
    commentsByStatus: {
      approved: number
      pending: number
      rejected: number
    }
    mostCommentedPosts: Array<{
      id: string
      title: string
      slug: string
      comments: number
    }>
    commentsByMonth: Array<{
      month: string
      comments: number
    }>
    topCommenters: Array<{
      name: string
      email: string
      comments: number
    }>
  }
  contactAnalytics: {
    totalContactMessages: number
    newContactMessages: number
    readContactMessages: number
    repliedContactMessages: number
    recentContactMessages: number
    contactMessagesByMonth: Array<{
      month: string
      contacts: number
    }>
  }
}

export function AdminAnalytics() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/analytics')
      if (!response.ok) {
        throw new Error('Failed to fetch analytics')
      }
      
      const analyticsData = await response.json()
      setData(analyticsData)
    } catch (err) {
      console.error('Analytics error:', err)
      setError(err instanceof Error ? err.message : 'Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [])

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                Analytics
              </span>
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Loading your site analytics...
            </p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-600 animate-pulse">
              <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
              <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                Analytics
              </span>
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Monitor your site performance and engagement
            </p>
          </div>
          <button
            onClick={fetchAnalytics}
            className="inline-flex items-center rounded-xl bg-gradient-primary hover:shadow-lg text-white px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 group"
          >
            <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
            Retry
          </button>
        </div>
        
        <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BarChart3 className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">
                <strong>Error loading analytics:</strong> {error}
              </p>
              <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                Make sure the analytics API endpoint is working properly.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const statsCards = [
    {
      title: 'Total Views',
      value: data?.overview.totalViews || 0,
      recent: 0, // API doesn't provide recent views directly
      icon: Eye,
      color: 'blue',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      iconColor: 'text-blue-600 dark:text-blue-400'
    },
    {
      title: 'Total Posts',
      value: data?.overview.totalPosts || 0,
      recent: data?.overview.recentPosts || 0,
      icon: Heart,
      color: 'red',
      bgColor: 'bg-red-100 dark:bg-red-900/20',
      iconColor: 'text-red-600 dark:text-red-400'
    },
    {
      title: 'Comments',
      value: data?.overview.totalComments || 0,
      recent: data?.overview.recentComments || 0,
      icon: MessageCircle,
      color: 'green',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      iconColor: 'text-green-600 dark:text-green-400'
    },
    {
      title: 'Contact Messages',
      value: data?.overview.totalContactMessages || 0,
      recent: data?.overview.recentContactMessages || 0,
      icon: Mail,
      color: 'purple',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      iconColor: 'text-purple-600 dark:text-purple-400'
    }
  ]

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold mb-2">
            <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
              Analytics
            </span>
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor your site performance and engagement
          </p>
        </div>
        <button
          onClick={fetchAnalytics}
          className="inline-flex items-center rounded-xl bg-gradient-primary hover:shadow-lg text-white px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 group"
        >
          <RefreshCw className="mr-2 h-4 w-4 group-hover:rotate-180 transition-transform duration-500" />
          Refresh
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {statsCards.map((stat) => {
          const Icon = stat.icon
          
          return (
            <div
              key={stat.title}
              className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 border border-gray-200/30 dark:border-gray-600/30 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all duration-200"
            >
              <div className="flex items-center justify-between mb-3">
                <Icon className={`h-5 w-5 ${stat.iconColor}`} />
                {stat.recent > 0 && (
                  <span className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full">
                    +{stat.recent}
                  </span>
                )}
              </div>
              
              <div>
                <p className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                  {stat.value.toLocaleString()}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
              </div>
            </div>
          )
        })}
      </div>

      {/* Main Analytics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Overview Stats */}
        <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-200/30 dark:border-gray-600/30">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
            Overview
          </h3>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
              <span className="text-sm text-gray-600 dark:text-gray-400">Published</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {data?.overview.publishedPosts || 0}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
              <span className="text-sm text-gray-600 dark:text-gray-400">Avg Views</span>
              <span className="font-semibold text-gray-900 dark:text-white">
                {data?.overview.avgViews || 0}
              </span>
            </div>
            <div className="flex justify-between items-center py-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">Approval Rate</span>
              <span className="font-semibold text-green-600 dark:text-green-400">
                {data?.overview.commentApprovalRate || 0}%
              </span>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        {data?.performanceMetrics && (
          <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-200/30 dark:border-gray-600/30">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
              Performance
            </h3>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
                <span className="text-sm text-gray-600 dark:text-gray-400">With Views</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {data.performanceMetrics.postsWithViews}
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
                <span className="text-sm text-gray-600 dark:text-gray-400">High Performing</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {data.performanceMetrics.highPerformingPosts}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Engagement</span>
                <span className="font-semibold text-blue-600 dark:text-blue-400">
                  {data.performanceMetrics.engagementRate}%
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Comments Summary */}
        {data?.commentAnalytics && (
          <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-200/30 dark:border-gray-600/30">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <MessageCircle className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
              Comments
            </h3>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
                <span className="text-sm text-gray-600 dark:text-gray-400">Approved</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {data.commentAnalytics.commentsByStatus.approved}
                </span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200/50 dark:border-gray-600/50">
                <span className="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                <span className="font-semibold text-yellow-600 dark:text-yellow-400">
                  {data.commentAnalytics.commentsByStatus.pending}
                </span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Rejected</span>
                <span className="font-semibold text-red-600 dark:text-red-400">
                  {data.commentAnalytics.commentsByStatus.rejected}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Posts */}
        {data?.topPosts && data.topPosts.length > 0 && (
          <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-200/30 dark:border-gray-600/30">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Eye className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
              Top Posts
            </h3>
            
            <div className="space-y-3">
              {data.topPosts.slice(0, 3).map((post, index) => (
                <div key={post.id} className="flex items-center justify-between p-3 bg-white/30 dark:bg-gray-700/30 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="flex items-center justify-center w-6 h-6 bg-primary-600 text-white rounded-full text-xs font-bold">
                      {index + 1}
                    </span>
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-gray-900 dark:text-white text-sm truncate">
                        {post.title}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
                    <Eye className="h-3 w-3" />
                    <span>{post.views}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Popular Tags */}
        {data?.popularTags && data.popularTags.length > 0 && (
          <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-200/30 dark:border-gray-600/30">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary-600 dark:text-primary-400" />
              Popular Tags
            </h3>
            
            <div className="space-y-3">
              {data.popularTags.slice(0, 5).map((tag, index) => (
                <div key={tag.tag} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-primary-600"></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {tag.tag}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-12 bg-gray-200/50 dark:bg-gray-600/50 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full" 
                        style={{
                          width: `${Math.min((tag.count / (data.popularTags[0]?.count || 1)) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                    <span className="text-sm font-semibold text-gray-600 dark:text-gray-400 min-w-[1.5rem] text-right">
                      {tag.count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
