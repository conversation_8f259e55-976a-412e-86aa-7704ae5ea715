'use client'

import { useState, useEffect } from 'react'
import { Heart } from 'lucide-react'
import toast from 'react-hot-toast'

interface LikeButtonProps {
  postSlug: string
  initialCount?: number
  initialLiked?: boolean
  className?: string
}

export default function LikeButton({
  postSlug,
  initialCount = 0,
  initialLiked = false,
  className = '',
}: LikeButtonProps) {
  const [liked, setLiked] = useState(initialLiked)
  const [count, setCount] = useState(initialCount)
  const [isLoading, setIsLoading] = useState(false)

  const handleLike = async () => {
    if (isLoading) return

    setIsLoading(true)

    try {
      const response = await fetch('/api/likes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ post_slug: postSlug }),
      })

      if (!response.ok) {
        throw new Error('Failed to toggle like')
      }

      const data = await response.json()
      setLiked(data.liked)
      setCount(data.count)

      toast.success(data.liked ? '\u2764\uFE0F Liked!' : '\uD83D\uDC94 Unliked', {
        duration: 1500,
        position: 'bottom-center',
      })
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to update like')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={handleLike}
      disabled={isLoading}
      className={`flex items-center gap-2 rounded-lg border px-3 py-2 transition-all duration-200 hover:scale-105 disabled:cursor-not-allowed disabled:opacity-50 ${
        liked
          ? 'border-red-200 bg-red-50 text-red-600 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30'
          : 'border-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
      } ${className}`}
    >
      <Heart
        size={18}
        className={`transition-all duration-200 ${
          liked ? 'fill-current' : ''
        } ${isLoading ? 'animate-pulse' : ''}`}
      />
      <span className="text-sm font-medium">
        {count} {count === 1 ? 'Like' : 'Likes'}
      </span>
    </button>
  )
}
