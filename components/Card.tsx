import Image from './Image'
import Link from './Link'
import { ArrowRight, ExternalLink } from 'lucide-react'

interface CardProps {
  title: string
  description: string
  imgSrc?: string
  href?: string
  className?: string
  variant?: 'default' | 'featured' | 'minimal'
}

const Card = ({
  title,
  description,
  imgSrc,
  href,
  className = '',
  variant = 'default',
}: CardProps) => {
  const cardClasses = {
    default: 'card-modern overflow-hidden group',
    featured:
      'card-gradient overflow-hidden group border-2 border-primary-200 dark:border-primary-700',
    minimal:
      'bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-medium border border-gray-100 dark:border-gray-700 transition-all duration-300 overflow-hidden group',
  }

  return (
    <div className={`${cardClasses[variant]} ${className} max-w-[544px]`}>
      {/* Image Section */}
      {imgSrc && (
        <div className="relative overflow-hidden">
          {href ? (
            <Link href={href} aria-label={`Link to ${title}`}>
              <div className="relative">
                <Image
                  alt={title}
                  src={imgSrc}
                  className="h-48 w-full object-cover object-center transition-transform duration-700 group-hover:scale-110 md:h-56"
                  width={544}
                  height={306}
                />
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                {/* Hover Icon */}
                <div className="absolute top-4 right-4 translate-x-2 transform opacity-0 transition-all duration-300 group-hover:translate-x-0 group-hover:opacity-100">
                  <div className="shadow-medium rounded-full bg-white/90 p-2 backdrop-blur-sm dark:bg-gray-800/90">
                    <ExternalLink className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                  </div>
                </div>
              </div>
            </Link>
          ) : (
            <div className="relative">
              <Image
                alt={title}
                src={imgSrc}
                className="h-48 w-full object-cover object-center transition-transform duration-700 group-hover:scale-110 md:h-56"
                width={544}
                height={306}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
            </div>
          )}
        </div>
      )}

      {/* Content Section */}
      <div className="p-6 sm:p-8">
        <div className="space-y-4">
          {/* Title */}
          <h3 className="text-xl leading-tight font-bold tracking-tight sm:text-2xl">
            {href ? (
              <Link
                href={href}
                aria-label={`Link to ${title}`}
                className="group-hover:from-primary-600 group-hover:to-accent-blue transition-all duration-300 group-hover:bg-gradient-to-r group-hover:bg-clip-text group-hover:text-transparent"
              >
                {title}
              </Link>
            ) : (
              <span className="text-gray-900 dark:text-gray-100">{title}</span>
            )}
          </h3>

          {/* Description */}
          <p className="line-clamp-3 leading-relaxed text-gray-600 dark:text-gray-400">
            {description}
          </p>

          {/* Call to Action */}
          {href && (
            <div className="pt-2">
              <Link
                href={href}
                className="group/link text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 inline-flex items-center font-medium transition-all duration-300"
                aria-label={`Learn more about ${title}`}
              >
                <span>Learn more</span>
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover/link:translate-x-1" />
              </Link>
            </div>
          )}
        </div>

        {/* Featured Badge */}
        {variant === 'featured' && (
          <div className="absolute top-4 left-4">
            <div className="bg-gradient-accent shadow-medium inline-flex items-center rounded-full px-3 py-1 text-sm font-medium text-white">
              Featured
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Card
