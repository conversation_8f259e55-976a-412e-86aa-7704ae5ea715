'use client'

import { useEffect, useRef } from 'react'
import hljs from 'highlight.js'

// Import specific languages you want to support
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import python from 'highlight.js/lib/languages/python'
import bash from 'highlight.js/lib/languages/bash'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import json from 'highlight.js/lib/languages/json'
import sql from 'highlight.js/lib/languages/sql'
import yaml from 'highlight.js/lib/languages/yaml'
import dockerfile from 'highlight.js/lib/languages/dockerfile'

// Register languages
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('typescript', typescript)
hljs.registerLanguage('python', python)
hljs.registerLanguage('bash', bash)
hljs.registerLanguage('css', css)
hljs.registerLanguage('html', html)
hljs.registerLanguage('json', json)
hljs.registerLanguage('sql', sql)
hljs.registerLanguage('yaml', yaml)
hljs.registerLanguage('dockerfile', dockerfile)

interface SyntaxHighlighterProps {
  code: string
  language?: string
  showLineNumbers?: boolean
  highlightLines?: number[]
  className?: string
}

export default function SyntaxHighlighter({
  code,
  language = 'text',
  showLineNumbers = true,
  highlightLines = [],
  className = '',
}: SyntaxHighlighterProps) {
  const codeRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (codeRef.current) {
      // Clear previous highlighting
      codeRef.current.innerHTML = code

      // Apply highlighting if language is supported
      if (language && language !== 'text' && hljs.getLanguage(language)) {
        const highlighted = hljs.highlight(code, { language })
        codeRef.current.innerHTML = highlighted.value
      }

      // Add line numbers and highlighting
      if (showLineNumbers || highlightLines.length > 0) {
        const lines = codeRef.current.innerHTML.split('\n')
        const numberedLines = lines.map((line, index) => {
          const lineNumber = index + 1
          const isHighlighted = highlightLines.includes(lineNumber)
          
          return `
            <div class="code-line ${isHighlighted ? 'highlighted' : ''}" data-line="${lineNumber}">
              ${showLineNumbers ? `<span class="line-number">${lineNumber}</span>` : ''}
              <span class="line-content">${line}</span>
            </div>
          `
        }).join('')

        codeRef.current.innerHTML = numberedLines
      }
    }
  }, [code, language, showLineNumbers, highlightLines])

  return (
    <code
      ref={codeRef}
      className={`block overflow-x-auto ${className}`}
      data-language={language}
    />
  )
}
