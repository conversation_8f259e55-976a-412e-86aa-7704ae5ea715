'use client'

import { useEffect } from 'react'

export function useCodeCopyButtons() {
  useEffect(() => {
    const addCopyButtons = () => {
      // Find all pre elements that don't already have a copy button
      const preElements = document.querySelectorAll('.prose-content pre:not([data-copy-added])')

      preElements.forEach((pre) => {
        const codeElement = pre.querySelector('code')
        if (!codeElement) return

        // Get the code text content
        const codeText = codeElement.textContent || ''

        // Mark this pre as having a copy button added
        pre.setAttribute('data-copy-added', 'true')

        // Create copy button
        const copyButton = document.createElement('button')
        copyButton.className =
          'copy-code-btn absolute top-3 right-3 flex h-8 w-8 items-center justify-center rounded-md border border-gray-300 bg-white/90 text-gray-600 transition-all duration-200 hover:bg-white hover:text-gray-900 hover:shadow-md dark:border-gray-600 dark:bg-gray-800/90 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100'
        copyButton.innerHTML = `
          <svg class="copy-icon h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
          <svg class="check-icon h-4 w-4 text-green-500 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        `
        copyButton.title = 'Copy code'
        copyButton.setAttribute('aria-label', 'Copy code to clipboard')

        // Add copy functionality
        copyButton.addEventListener('click', async () => {
          try {
            await navigator.clipboard.writeText(codeText)

            // Show success state
            const copyIcon = copyButton.querySelector('.copy-icon') as HTMLElement
            const checkIcon = copyButton.querySelector('.check-icon') as HTMLElement

            if (copyIcon && checkIcon) {
              copyIcon.classList.add('hidden')
              checkIcon.classList.remove('hidden')
              copyButton.title = 'Copied!'

              // Reset after 2 seconds
              setTimeout(() => {
                copyIcon.classList.remove('hidden')
                checkIcon.classList.add('hidden')
                copyButton.title = 'Copy code'
              }, 2000)
            }
          } catch (err) {
            console.error('Failed to copy code:', err)
          }
        })

        // Make the pre element relative positioned and add group class for hover effects
        ;(pre as HTMLElement).style.position = 'relative'
        pre.classList.add('group')

        // Append the button to the pre element
        pre.appendChild(copyButton)
      })
    }

    // Add copy buttons immediately
    addCopyButtons()

    // Also add copy buttons when new content is loaded (for dynamic content)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Small delay to ensure DOM is updated
          setTimeout(addCopyButtons, 100)
        }
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => {
      observer.disconnect()
    }
  }, [])
}
