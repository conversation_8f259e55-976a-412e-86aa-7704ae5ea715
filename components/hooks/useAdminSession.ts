'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from 'react-hot-toast'

interface SessionData {
  authenticated: boolean
  expiresAt?: number
  sessionDuration?: number
  refreshThreshold?: number
  refreshed?: boolean
}

interface UseAdminSessionOptions {
  onSessionExpired?: () => void
  checkInterval?: number // in milliseconds
  showRefreshToasts?: boolean
}

interface UseAdminSessionReturn {
  isAuthenticated: boolean
  isLoading: boolean
  sessionData: SessionData | null
  timeRemaining: string
  isNearExpiry: boolean
  refreshSession: () => Promise<boolean>
  logout: () => Promise<boolean>
}

export function useAdminSession(options: UseAdminSessionOptions = {}): UseAdminSessionReturn {
  const {
    onSessionExpired,
    checkInterval = 2 * 60 * 1000, // Check every 2 minutes
    showRefreshToasts = false,
  } = options

  const [sessionData, setSessionData] = useState<SessionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastRefreshRef = useRef<number>(0)

  const checkSession = useCallback(
    async (showToasts = false) => {
      try {
        const response = await fetch('/api/auth/admin')
        const data = await response.json()

        if (data.authenticated) {
          setSessionData(data)

          // Show refresh notification if session was refreshed
          if (data.refreshed && showToasts && showRefreshToasts) {
            toast.success('Session refreshed automatically', {
              duration: 3000,
              position: 'bottom-right',
            })
          }

          return true
        } else {
          setSessionData({ authenticated: false })
          if (onSessionExpired) {
            onSessionExpired()
          }
          return false
        }
      } catch (error) {
        console.error('Session check failed:', error)
        setSessionData({ authenticated: false })
        return false
      }
    },
    [onSessionExpired, showRefreshToasts]
  )

  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/admin', {
        method: 'PUT',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSessionData((prev) =>
          prev
            ? {
                ...prev,
                expiresAt: data.expiresAt,
                refreshed: true,
              }
            : null
        )

        lastRefreshRef.current = Date.now()
        toast.success('Session refreshed successfully')
        return true
      } else {
        toast.error(data.error || 'Failed to refresh session')
        return false
      }
    } catch (error) {
      console.error('Manual refresh failed:', error)
      toast.error('Failed to refresh session')
      return false
    }
  }, [])

  const logout = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/admin', {
        method: 'DELETE',
      })

      if (response.ok) {
        setSessionData({ authenticated: false })
        toast.success('Logged out successfully')
        return true
      } else {
        toast.error('Logout failed')
        return false
      }
    } catch (error) {
      console.error('Logout error:', error)
      toast.error('Logout failed')
      return false
    }
  }, [])

  // Format time remaining
  const getTimeRemaining = useCallback((): string => {
    if (!sessionData?.expiresAt) return ''

    const remaining = sessionData.expiresAt - Date.now()
    if (remaining <= 0) return 'Expired'

    const days = Math.floor(remaining / (1000 * 60 * 60 * 24))
    const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
      return `${days}d ${hours}h`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }, [sessionData?.expiresAt])

  // Check if session is near expiry (within refresh threshold)
  const isNearExpiry = useCallback((): boolean => {
    if (!sessionData?.expiresAt || !sessionData?.refreshThreshold) return false

    const remaining = sessionData.expiresAt - Date.now()
    return remaining <= sessionData.refreshThreshold
  }, [sessionData?.expiresAt, sessionData?.refreshThreshold])

  // Initial session check
  useEffect(() => {
    checkSession().finally(() => setIsLoading(false))
  }, [checkSession])

  // Set up periodic session checking
  useEffect(() => {
    if (sessionData?.authenticated) {
      intervalRef.current = setInterval(() => checkSession(true), checkInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [sessionData?.authenticated, checkSession, checkInterval])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    isAuthenticated: sessionData?.authenticated ?? false,
    isLoading,
    sessionData,
    timeRemaining: getTimeRemaining(),
    isNearExpiry: isNearExpiry(),
    refreshSession,
    logout,
  }
}
