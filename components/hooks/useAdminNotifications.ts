'use client'

import { useState, useEffect, useCallback } from 'react'

interface NotificationCounts {
  newComments: number
  newContacts: number
  pendingComments: number
  totalNotifications: number
}

interface RecentActivity {
  id: string
  type: 'comment' | 'contact'
  title: string
  subtitle: string
  time: string
  isNew: boolean
}

export function useAdminNotifications() {
  const [counts, setCounts] = useState<NotificationCounts>({
    newComments: 0,
    newContacts: 0,
    pendingComments: 0,
    totalNotifications: 0,
  })

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [lastChecked, setLastChecked] = useState<Date>(new Date())

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch contact messages
      const contactResponse = await fetch('/api/contact/admin')
      const contactData = contactResponse.ok ? await contactResponse.json() : { data: [] }

      // Fetch comments (admin)
      const commentsResponse = await fetch('/api/comments?admin=true')
      const commentsData = commentsResponse.ok ? await commentsResponse.json() : []

      if (contactData.success && Array.isArray(commentsData)) {
        const contacts = contactData.data || []
        const comments = commentsData || []

        // Calculate counts
        const newContacts = contacts.filter((c: any) => c.status === 'new').length
        const pendingComments = comments.filter((c: any) => c.status === 'pending').length

        // Get recent comments (last 24 hours)
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
        const newComments = comments.filter(
          (c: any) => c.status === 'pending' && new Date(c.created_at) > oneDayAgo
        ).length

        const totalNotifications = newContacts + pendingComments

        setCounts({
          newComments,
          newContacts,
          pendingComments,
          totalNotifications,
        })

        // Build recent activity list
        const activity: RecentActivity[] = []

        // Add recent contacts
        contacts
          .filter((c: any) => c.status === 'new')
          .slice(0, 3)
          .forEach((contact: any) => {
            activity.push({
              id: contact.id,
              type: 'contact',
              title: `New message from ${contact.name}`,
              subtitle: contact.subject,
              time: new Date(contact.created_at).toLocaleString(),
              isNew: new Date(contact.created_at) > lastChecked,
            })
          })

        // Add recent comments
        comments
          .filter((c: any) => c.status === 'pending')
          .slice(0, 3)
          .forEach((comment: any) => {
            activity.push({
              id: comment.id,
              type: 'comment',
              title: `New comment from ${comment.author_name}`,
              subtitle: `on "${comment.post_slug}"`,
              time: new Date(comment.created_at).toLocaleString(),
              isNew: new Date(comment.created_at) > lastChecked,
            })
          })

        // Sort by date (newest first)
        activity.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
        setRecentActivity(activity.slice(0, 5))
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }, [lastChecked])

  const markAsChecked = useCallback((type?: 'comments' | 'contacts') => {
    setLastChecked(new Date())

    if (!type) {
      // Mark all as checked
      setCounts((prev) => ({
        ...prev,
        totalNotifications: 0,
      }))
    } else if (type === 'comments') {
      setCounts((prev) => ({
        ...prev,
        newComments: 0,
        totalNotifications: prev.totalNotifications - prev.newComments,
      }))
    } else if (type === 'contacts') {
      setCounts((prev) => ({
        ...prev,
        newContacts: 0,
        totalNotifications: prev.totalNotifications - prev.newContacts,
      }))
    }
  }, [])

  const refreshNotifications = useCallback(() => {
    fetchNotifications()
  }, [fetchNotifications])

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchNotifications()

    const interval = setInterval(() => {
      fetchNotifications()
    }, 30000)

    return () => clearInterval(interval)
  }, [fetchNotifications])

  return {
    counts,
    recentActivity,
    loading,
    markAsChecked,
    refreshNotifications,
  }
}
