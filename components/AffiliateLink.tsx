'use client'

import { useState, useEffect } from 'react'

interface AffiliateLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  platform?: 'amazon' | 'generic'
  productName?: string
  disclosure?: boolean
  trackingId?: string
}

export function AffiliateLink({
  href,
  children,
  className = '',
  platform = 'generic',
  productName,
  disclosure = true,
  trackingId,
}: AffiliateLinkProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleClick = () => {
    // Track affiliate link clicks
    if (isClient && typeof window !== 'undefined') {
      try {
        // Analytics tracking
        if (window.gtag) {
          window.gtag('event', 'affiliate_click', {
            platform,
            product: productName,
            link: href,
          })
        }

        // Custom analytics or tracking
        fetch('/api/analytics/affiliate-click', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            platform,
            productName,
            href,
            timestamp: new Date().toISOString(),
          }),
        }).catch((err) => console.log('Analytics tracking failed:', err))

        // Store in localStorage for reporting
        const affiliateClicks = JSON.parse(localStorage.getItem('affiliate_clicks') || '[]')
        affiliateClicks.push({
          platform,
          productName,
          href,
          timestamp: new Date().toISOString(),
        })
        localStorage.setItem('affiliate_clicks', JSON.stringify(affiliateClicks.slice(-100))) // Keep last 100
      } catch (error) {
        console.log('Affiliate tracking error:', error)
      }
    }
  }

  const linkClasses = `
    ${className}
    affiliate-link
    relative
    inline-flex
    items-center
    text-blue-600
    hover:text-blue-800
    dark:text-blue-400
    dark:hover:text-blue-300
    font-medium
    transition-colors
    duration-200
    ${disclosure ? 'after:content-["🔗"] after:ml-1 after:text-xs after:opacity-70' : ''}
  `.trim()

  return (
    <>
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer nofollow sponsored"
        className={linkClasses}
        onClick={handleClick}
        data-affiliate={platform}
        data-product={productName}
        aria-label={productName ? `Affiliate link to ${productName}` : 'Affiliate link'}
      >
        {children}
      </a>

      {disclosure && (
        <span className="affiliate-disclosure ml-1 text-xs text-gray-500 italic dark:text-gray-400">
          (affiliate link)
        </span>
      )}
    </>
  )
}

// Disclosure component for the bottom of posts
export function AffiliateDisclosure({ className = '' }: { className?: string }) {
  return (
    <div
      className={`affiliate-disclosure-full rounded-lg border border-gray-200 bg-gray-50 p-4 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 ${className}`}
    >
      <h4 className="mb-2 font-semibold text-gray-800 dark:text-gray-200">Affiliate Disclosure</h4>
      <p>
        This post may contain affiliate links. As an Amazon Associate and partner of other affiliate
        programs, I earn from qualifying purchases. This means I may receive a small commission if
        you purchase through these links at no additional cost to you. I only recommend products and
        services I personally use and believe will add value to my readers. Thank you for supporting
        this blog!
      </p>
    </div>
  )
}

// Hook for managing affiliate statistics
export function useAffiliateStats() {
  const [stats, setStats] = useState({
    totalClicks: 0,
    clicksByPlatform: {} as Record<string, number>,
    recentClicks: [] as any[],
  })

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const clicks = JSON.parse(localStorage.getItem('affiliate_clicks') || '[]')
      const clicksByPlatform = clicks.reduce((acc: Record<string, number>, click: any) => {
        acc[click.platform] = (acc[click.platform] || 0) + 1
        return acc
      }, {})

      setStats({
        totalClicks: clicks.length,
        clicksByPlatform,
        recentClicks: clicks.slice(-10).reverse(),
      })
    }
  }, [])

  return stats
}
