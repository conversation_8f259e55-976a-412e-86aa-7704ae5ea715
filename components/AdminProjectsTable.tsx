'use client'

import { useState, useEffect } from 'react'
import { Edit, Trash2, ExternalLink, Folder, Star, ArrowUp, ArrowDown, Calendar, Code } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Project } from '../lib/supabase'

interface AdminProjectsTableProps {
  onEdit: (project: Project) => void
}

export function AdminProjectsTable({ onEdit }: AdminProjectsTableProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'archived' | 'draft'>('all')

  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      // Use admin API to get all projects including archived/draft ones
      const response = await fetch('/api/admin/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data)
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
      toast.error('Failed to load projects')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`))
      return

    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setProjects(projects.filter((p) => p.id !== id))
        toast.success('Project deleted successfully')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete project')
      }
    } catch (error) {
      console.error('Error deleting project:', error)
      toast.error('Error deleting project')
    }
  }

  const moveProject = async (projectId: string, direction: 'up' | 'down') => {
    const currentIndex = projects.findIndex((p) => p.id === projectId)
    if (currentIndex === -1) return

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= projects.length) return

    const updatedProjects = [...projects]
    const [movedProject] = updatedProjects.splice(currentIndex, 1)
    updatedProjects.splice(newIndex, 0, movedProject)

    // Update order_index for both projects
    const project1 = updatedProjects[Math.min(currentIndex, newIndex)]
    const project2 = updatedProjects[Math.max(currentIndex, newIndex)]

    try {
      await Promise.all([
        fetch('/api/projects', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: project1.id, order_index: Math.min(currentIndex, newIndex) }),
        }),
        fetch('/api/projects', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: project2.id, order_index: Math.max(currentIndex, newIndex) }),
        }),
      ])

      setProjects(updatedProjects)
      toast.success('Project order updated')
    } catch (error) {
      console.error('Error reordering projects:', error)
      toast.error('Error reordering projects')
    }
  }

  const filteredProjects = projects.filter((project) => {
    if (statusFilter === 'all') return true
    return project.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700'
      case 'archived':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 border-slate-200 dark:border-slate-700'
      case 'draft':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-700'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 border-slate-200 dark:border-slate-700'
    }
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 shadow-lg">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 rounded-xl flex items-center justify-center">
              <Folder className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-slate-900 dark:text-white">Projects</h2>
              <p className="text-sm text-slate-600 dark:text-slate-400">Manage projects</p>
            </div>
          </div>
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="mx-auto h-8 w-8 animate-spin rounded-full border-2 border-slate-200 border-t-indigo-600 mb-4"></div>
              <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Loading projects...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 shadow-lg overflow-hidden">
              {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <Folder className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Projects</h2>
              <p className="text-indigo-100">Manage projects</p>
            </div>
          </div>
          <div className="bg-white/20 rounded-xl px-4 py-2">
            <span className="text-white font-semibold">{projects.length} Total</span>
          </div>
        </div>
      </div>

              {/* Filter Tabs */}
      <div className="bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700 p-4">
        <div className="flex gap-2">
          {(['all', 'active', 'archived', 'draft'] as const).map((status) => {
            const count = status === 'all' ? projects.length : projects.filter((p) => p.status === status).length
            return (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-300 ${
                  statusFilter === status
                    ? 'bg-indigo-600 text-white shadow-lg'
                    : 'bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-indigo-50 dark:hover:bg-slate-600 border border-slate-200 dark:border-slate-600'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)} ({count})
              </button>
            )
          })}
        </div>
      </div>

              {/* Projects List */}
      <div className="p-6">
        {filteredProjects.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Folder className="h-8 w-8 text-slate-400" />
            </div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
              No projects found
            </h3>
            <p className="text-slate-600 dark:text-slate-400">
              {projects.length === 0 ? 'No projects created yet' : `No ${statusFilter} projects`}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="bg-slate-50 dark:bg-slate-700/50 rounded-xl border border-slate-200 dark:border-slate-600 p-4 hover:shadow-lg hover:border-indigo-300 dark:hover:border-indigo-600 transition-all duration-300 group">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Project Image */}
                    {project.imgSrc ? (
                      <img
                        src={project.imgSrc}
                        alt={project.title}
                        className="w-16 h-16 rounded-lg object-cover border border-slate-200 dark:border-slate-600"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-600 dark:to-slate-700 rounded-lg flex items-center justify-center">
                        <Folder className="h-8 w-8 text-slate-400" />
                      </div>
                    )}
                    
                    {/* Project Details */}
                    <div className="flex-1 min-w-0">
                      {/* Status and Order */}
                      <div className="flex items-center space-x-3 mb-3">
                        <span className={`px-3 py-1 rounded-lg text-xs font-semibold border ${getStatusBadge(project.status)}`}>
                          {project.status}
                        </span>
                        {project.featured && (
                          <span className="px-3 py-1 bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 dark:from-yellow-900/30 dark:to-amber-900/30 dark:text-yellow-300 rounded-lg text-xs font-semibold border border-yellow-200 dark:border-yellow-700 flex items-center space-x-1">
                            <Star className="h-3 w-3" />
                            <span>Featured</span>
                          </span>
                        )}
                        <div className="flex items-center space-x-1 text-xs text-slate-500 dark:text-slate-400">
                          <span>Order: {project.order_index}</span>
                        </div>
                      </div>
                      
                      {/* Title and Description */}
                      <div className="mb-3">
                        <h3 className="font-bold text-lg text-slate-900 dark:text-white mb-1 group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300">
                          {project.title}
                        </h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">{project.description}</p>
                        {project.href && (
                          <a
                            href={project.href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center space-x-1 mt-2 text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 transition-colors"
                          >
                            <ExternalLink className="h-3 w-3" />
                            <span>View Project</span>
                          </a>
                        )}
                      </div>
                      
                      {/* Technologies */}
                      {project.technologies && project.technologies.length > 0 && (
                        <div className="flex items-center space-x-2 text-xs">
                          <Code className="h-3 w-3 text-slate-400" />
                          <div className="flex flex-wrap gap-1">
                            {project.technologies.slice(0, 4).map((tech, i) => (
                              <span
                                key={i}
                                className="px-2 py-1 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300 rounded text-xs font-medium border border-indigo-200 dark:border-indigo-700"
                              >
                                {tech}
                              </span>
                            ))}
                            {project.technologies.length > 4 && (
                              <span className="text-slate-500 dark:text-slate-400">
                                +{project.technologies.length - 4} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4">
                    {/* Order Controls */}
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => moveProject(project.id!, 'up')}
                        disabled={index === 0}
                        className="p-1.5 bg-slate-100 hover:bg-slate-200 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-600 dark:text-slate-400 rounded-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move Up"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => moveProject(project.id!, 'down')}
                        disabled={index === filteredProjects.length - 1}
                        className="p-1.5 bg-slate-100 hover:bg-slate-200 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-600 dark:text-slate-400 rounded-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Move Down"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </button>
                    </div>
                    
                    {/* Edit and Delete */}
                    <button
                      onClick={() => onEdit(project)}
                      className="p-2 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900/30 dark:hover:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400 rounded-lg transition-all duration-300 hover:scale-105"
                      title="Edit Project"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    {project.id && (
                      <button
                        onClick={() => handleDelete(project.id!, project.title)}
                        className="p-2 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-600 dark:text-red-400 rounded-lg transition-all duration-300 hover:scale-105"
                        title="Delete Project"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
