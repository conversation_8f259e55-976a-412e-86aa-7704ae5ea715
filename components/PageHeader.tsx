interface PageHeaderProps {
  title: string
  description: string
  icon?: React.ReactNode
  badge?: string
}

export default function PageHeader({ title, description, icon, badge }: PageHeaderProps) {
  return (
    <header className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center space-y-6">
          {/* Badge */}
          {badge && (
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-full text-sm font-medium">
              {icon}
              <span aria-label={`${badge} section`}>{badge}</span>
            </div>
          )}
          
          {/* Title */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white tracking-tight">
            {title}
          </h1>
          
          {/* Description */}
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>
      </div>
      
      {/* Schema markup for better SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'WebPage',
            'name': title,
            'description': description,
            'breadcrumb': {
              '@type': 'BreadcrumbList',
              'itemListElement': [
                {
                  '@type': 'ListItem',
                  'position': 1,
                  'name': 'Home',
                  'item': '/'
                },
                {
                  '@type': 'ListItem', 
                  'position': 2,
                  'name': title
                }
              ]
            }
          })
        }}
      />
    </header>
  )
}
