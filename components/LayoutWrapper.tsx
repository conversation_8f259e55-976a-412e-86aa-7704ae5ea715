import { Inter } from 'next/font/google'
import SectionContainer from './SectionContainer'
import Footer from './Footer'
import { ReactNode } from 'react'
import Header from './Header'

interface Props {
  children: ReactNode
}

const inter = Inter({
  subsets: ['latin'],
})

const LayoutWrapper = ({ children }: Props) => {
  return (
    <div className={`${inter.className} flex min-h-screen flex-col font-sans`}>
      <div className="flex-1">
        {children}
      </div>
    </div>
  )
}

export default LayoutWrapper
