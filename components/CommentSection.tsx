'use client'

import { useState, useEffect } from 'react'
import { MessageCircle, Send, User, Mail } from 'lucide-react'
import toast from 'react-hot-toast'

interface Comment {
  id: string
  author_name: string
  content: string
  created_at: string
}

interface CommentSectionProps {
  postSlug: string
  initialComments?: Comment[]
  initialCount?: number
  className?: string
}

export default function CommentSection({
  postSlug,
  initialComments = [],
  initialCount = 0,
  className = '',
}: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>(initialComments)
  const [commentCount, setCommentCount] = useState(initialCount)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showForm, setShowForm] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    author_name: '',
    author_email: '',
    content: '',
  })

  // Load comments on mount
  useEffect(() => {
    loadComments()
  }, [postSlug])

  const loadComments = async () => {
    if (isLoading) return

    setIsLoading(true)
    try {
      const response = await fetch(`/api/comments?slug=${encodeURIComponent(postSlug)}`)
      if (response.ok) {
        const data = await response.json()
        setComments(data)
        setCommentCount(data.length)
      }
    } catch (error) {
      console.error('Error loading comments:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.author_name.trim() || !formData.author_email.trim() || !formData.content.trim()) {
      toast.error('Please fill in all fields')
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          post_slug: postSlug,
          ...formData,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit comment')
      }

      const data = await response.json()

      // Reset form
      setFormData({ author_name: '', author_email: '', content: '' })
      setShowForm(false)

      toast.success('Comment submitted! It will appear after approval.', {
        duration: 4000,
      })
    } catch (error) {
      console.error('Error submitting comment:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to submit comment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className={`mt-8 ${className}`}>
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageCircle size={20} className="text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Comments ({commentCount})
          </h3>
        </div>

        <button
          onClick={() => setShowForm(!showForm)}
          className="rounded-lg bg-blue-600 px-4 py-2 text-sm text-white transition-colors duration-200 hover:bg-blue-700"
        >
          {showForm ? 'Cancel' : 'Add Comment'}
        </button>
      </div>

      {/* Comment Form */}
      {showForm && (
        <div className="mb-8 rounded-lg border bg-gray-50 p-6 dark:bg-gray-800">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label
                  htmlFor="author_name"
                  className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Name *
                </label>
                <div className="relative">
                  <User
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400"
                  />
                  <input
                    type="text"
                    id="author_name"
                    value={formData.author_name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, author_name: e.target.value }))
                    }
                    className="w-full rounded-lg border border-gray-300 bg-white py-2 pr-3 pl-10 text-gray-900 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    placeholder="Your name"
                    required
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="author_email"
                  className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Email *
                </label>
                <div className="relative">
                  <Mail
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400"
                  />
                  <input
                    type="email"
                    id="author_email"
                    value={formData.author_email}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, author_email: e.target.value }))
                    }
                    className="w-full rounded-lg border border-gray-300 bg-white py-2 pr-3 pl-10 text-gray-900 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="content"
                className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Comment *
              </label>
              <textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
                rows={4}
                className="w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-gray-900 focus:border-transparent focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Share your thoughts..."
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                * Your email will not be published. Comments are moderated.
              </p>

              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-2 text-white transition-colors duration-200 hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <Send size={16} />
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Comments List */}
      <div className="space-y-6">
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-500 dark:text-gray-400">Loading comments...</p>
          </div>
        ) : comments.length === 0 ? (
          <div className="py-8 text-center">
            <MessageCircle size={48} className="mx-auto mb-4 text-gray-300 dark:text-gray-600" />
            <p className="text-gray-500 dark:text-gray-400">
              No comments yet. Be the first to share your thoughts!
            </p>
          </div>
        ) : (
          comments.map((comment) => (
            <div
              key={comment.id}
              className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="mb-3 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600">
                    <span className="text-sm font-medium text-white">
                      {comment.author_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {comment.author_name}
                    </h4>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(comment.created_at)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                {comment.content}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}
