'use client'

import { useState, useRef } from 'react'
import { Copy, Check, Terminal, FileText, Code, Braces } from 'lucide-react'

interface CodeBlockProps {
  children: string
  language?: string
  title?: string
  filename?: string
  showLineNumbers?: boolean
  highlightLines?: number[]
  maxHeight?: string
  className?: string
}

const languageIcons = {
  javascript: Code,
  typescript: Code,
  jsx: Braces,
  tsx: Braces,
  python: FileText,
  bash: Terminal,
  shell: Terminal,
  json: Braces,
  html: Code,
  css: Code,
  scss: Code,
  yaml: FileText,
  yml: FileText,
  markdown: FileText,
  md: FileText,
  sql: FileText,
  graphql: Code,
  docker: Terminal,
  dockerfile: Terminal,
}

export default function CodeBlock({
  children,
  language = 'text',
  title,
  filename,
  showLineNumbers = false,
  highlightLines = [],
  maxHeight = '600px',
  className = '',
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const codeRef = useRef<HTMLPreElement>(null)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(children)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy code:', err)
    }
  }

  const lines = children.trim().split('\n')
  const shouldShowExpander = lines.length > 25
  const displayLines = isExpanded ? lines : lines.slice(0, 25)

  const IconComponent = languageIcons[language as keyof typeof languageIcons] || Code

  return (
    <div className="group relative my-8 overflow-hidden rounded-2xl bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 p-1.5 shadow-2xl transition-all hover:shadow-2xl hover:from-pink-600 hover:via-purple-600 hover:to-blue-600 animate-gradient-x shimmer-border">
      <div className="relative overflow-hidden rounded-[18px] bg-white/80 dark:bg-gray-900/80 shadow-xl ring-1 ring-gray-900/10 dark:ring-white/10 backdrop-blur-md">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-3 bg-gradient-to-r from-fuchsia-500 via-sky-500 to-emerald-400 animate-gradient-x rounded-t-[16px] shadow-md">
          <div className="flex items-center space-x-4">
            {/* Animated Dots */}
            <div className="flex space-x-2">
              <div className="h-4 w-4 rounded-full bg-gradient-to-br from-pink-400 via-red-500 to-yellow-400 animate-dot-glow shadow-dot-glow-red"></div>
              <div className="h-4 w-4 rounded-full bg-gradient-to-br from-yellow-300 via-green-400 to-teal-400 animate-dot-glow shadow-dot-glow-yellow"></div>
              <div className="h-4 w-4 rounded-full bg-gradient-to-br from-green-400 via-blue-400 to-purple-500 animate-dot-glow shadow-dot-glow-green"></div>
            </div>
            {/* Language Badge */}
            <span className="px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 text-white shadow-md border border-white/30 backdrop-blur-sm uppercase tracking-wide">
              {language}
            </span>
            {/* Filename/Title */}
            {(filename || title) && (
              <span className="ml-2 text-sm font-semibold text-gray-900 dark:text-gray-100 drop-shadow-sm">
                {filename || title}
              </span>
            )}
          </div>
          {/* Floating Copy Button */}
          <div className="relative">
            <button
              onClick={copyToClipboard}
              className="copy-float-btn group/copy absolute -top-6 right-0 z-10 flex items-center justify-center rounded-full bg-white/80 dark:bg-gray-900/80 border-2 border-gradient-to-r from-pink-400 via-purple-400 to-blue-400 shadow-lg p-2 transition-all duration-200 hover:scale-110 hover:shadow-xl backdrop-blur-md"
              title={copied ? 'Copied!' : 'Copy code'}
              style={{ minWidth: 40, minHeight: 40 }}
            >
              {copied ? (
                <Check className="h-5 w-5 text-green-500 animate-ping-once" />
              ) : (
                <Copy className="h-5 w-5 text-blue-500 group-hover/copy:scale-110 transition-transform" />
              )}
            </button>
          </div>
        </div>
        {/* Code Content */}
        <div className="relative">
          <pre
            ref={codeRef}
            className={`relative overflow-x-auto bg-gradient-to-br from-gray-900/80 via-gray-900/70 to-blue-900/30 p-6 text-sm leading-relaxed text-gray-100 border-t border-gray-700/30 dark:from-gray-950/80 dark:via-gray-900/70 dark:to-indigo-900/30 dark:border-gray-600/30 ${className}`}
            style={{
              maxHeight: isExpanded ? 'none' : maxHeight,
              fontFamily: '"Fira Code", "JetBrains Mono", "Monaco", "Consolas", monospace',
              backdropFilter: 'blur(6px)',
              backgroundBlendMode: 'overlay',
            }}
          >
            <code className="block">
              {displayLines.map((line, index) => {
                const lineNumber = index + 1
                const isHighlighted = highlightLines.includes(lineNumber)
                return (
                  <div
                    key={index}
                    className={`group/line flex ${
                      isHighlighted
                        ? 'highlight-line-gradient'
                        : 'hover:bg-gray-800/50'
                    }`}
                  >
                    {showLineNumbers && (
                      <span className="mr-6 inline-block w-8 select-none text-right text-xs font-mono text-blue-400 dark:text-blue-300 bg-gradient-to-r from-blue-100/30 to-purple-100/10 rounded-md">
                        {lineNumber}
                      </span>
                    )}
                    <span className="flex-1 whitespace-pre-wrap break-words">
                      {line || ' '}
                    </span>
                  </div>
                )
              })}
            </code>
          </pre>
          {/* Modern Expand/Collapse Button */}
          {shouldShowExpander && (
            <div className="absolute bottom-0 left-0 right-0 flex justify-center bg-gradient-to-t from-gray-900/80 to-transparent p-4">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="flex items-center space-x-2 rounded-full bg-white/80 dark:bg-gray-900/80 px-5 py-2 text-sm font-semibold text-gray-800 dark:text-gray-100 border-2 border-gradient-to-r from-pink-400 via-purple-400 to-blue-400 shadow transition-all hover:scale-105 hover:shadow-lg backdrop-blur-md"
              >
                <span>{isExpanded ? 'Show Less' : `Show ${lines.length - 25} More Lines`}</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
