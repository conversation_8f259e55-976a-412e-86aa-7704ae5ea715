import siteMetadata from '@/data/siteMetadata'
import headerNavLinks from '@/data/headerNavLinks'
import Logo from '@/data/logo.svg'
import Link from './Link'
import MobileNav from './MobileNav'
import ThemeSwitch from './ThemeSwitch'
import SearchButton from './SearchButton'
import { Settings } from 'lucide-react'

const Header = () => {
  let headerClass = 'border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950'

  if (siteMetadata.stickyNav) {
    headerClass += ' sticky top-0 z-50'
  }

  return (
    <header className={headerClass}>
      {/* Top Bar */}
      <div className="border-b border-gray-100 dark:border-gray-800 py-2">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4 text-gray-600 dark:text-gray-400">
              <span>Breaking: Latest tech news and insights</span>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeSwitch />
              <SearchButton />
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="py-4">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between">

            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" aria-label={siteMetadata.headerTitle} className="flex items-center space-x-3">
                <Logo className="h-8 w-8" />
                {typeof siteMetadata.headerTitle === 'string' ? (
                  <div className="text-2xl font-display font-bold text-gray-900 dark:text-white">
                    {siteMetadata.headerTitle}
                  </div>
                ) : (
                  siteMetadata.headerTitle
                )}
              </Link>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {headerNavLinks
                .filter((link) => link.href !== '/')
                .map((link) => (
                  <Link
                    key={link.title}
                    href={link.href}
                    className="text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 uppercase tracking-wide"
                  >
                    {link.title}
                  </Link>
                ))}
            </nav>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              <Link
                href="/admin"
                className="hidden md:flex items-center text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
                aria-label="Admin Panel"
              >
                <Settings className="h-4 w-4 mr-1" />
                Admin
              </Link>

              {/* Mobile Navigation */}
              <div className="md:hidden">
                <MobileNav />
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
