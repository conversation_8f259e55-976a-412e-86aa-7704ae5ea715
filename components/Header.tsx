import siteMetadata from '@/data/siteMetadata'
import headerNavLinks from '@/data/headerNavLinks'
import Logo from '@/data/logo.svg'
import Link from './Link'
import MobileNav from './MobileNav'
import ThemeSwitch from './ThemeSwitch'
import SearchButton from './SearchButton'
import { Settings } from 'lucide-react'

const Header = () => {
  let headerClass = 'border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'

  if (siteMetadata.stickyNav) {
    headerClass += ' sticky top-0 z-50'
  }

  return (
    <header className={headerClass}>
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-between py-4">

          {/* Logo - Wired Style */}
          <div className="flex items-center">
            <Link href="/" aria-label={siteMetadata.headerTitle} className="flex items-center space-x-2">
              <Logo className="h-6 w-6" />
              {typeof siteMetadata.headerTitle === 'string' ? (
                <div className="text-2xl font-display font-black text-gray-900 dark:text-white tracking-tight">
                  {siteMetadata.headerTitle.toUpperCase()}
                </div>
              ) : (
                siteMetadata.headerTitle
              )}
            </Link>
          </div>

          {/* Center Navigation - Wired Style */}
          <nav className="hidden lg:flex items-center space-x-8">
            {headerNavLinks
              .filter((link) => link.href !== '/')
              .map((link) => (
                <Link
                  key={link.title}
                  href={link.href}
                  className="text-sm font-semibold text-gray-900 dark:text-white hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 uppercase tracking-wider"
                >
                  {link.title}
                </Link>
              ))}
          </nav>

          {/* Right Side - Clean */}
          <div className="flex items-center space-x-4">
            <SearchButton />
            <ThemeSwitch />

            <Link
              href="/admin"
              className="hidden md:flex items-center text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
              aria-label="Admin Panel"
            >
              <Settings className="h-4 w-4" />
            </Link>

            {/* Mobile Navigation */}
            <div className="lg:hidden">
              <MobileNav />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
