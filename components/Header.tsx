import siteMetadata from '@/data/siteMetadata'
import headerNavLinks from '@/data/headerNavLinks'
import Logo from '@/data/logo.svg'
import Link from './Link'
import MobileNav from './MobileNav'
import ThemeSwitch from './ThemeSwitch'
import SearchButton from './SearchButton'
import { Settings, Sparkles } from 'lucide-react'

const Header = () => {
  let headerClass = 'flex items-center w-full justify-center py-3'

  if (siteMetadata.stickyNav) {
    headerClass +=
      ' sticky top-0 z-50 glass-card backdrop-blur-lg border-b border-white/10 dark:border-gray-800/50 bg-white/95 dark:bg-gray-950/95'
  } else {
    headerClass +=
      ' bg-white/80 dark:bg-gray-950/80 backdrop-blur-lg rounded-xl mt-1 shadow-soft border border-white/20 dark:border-gray-800/50'
  }

  return (
    <header className={headerClass}>
      <div className="w-full max-w-3xl xl:max-w-5xl mx-auto">
        <div className="flex items-center justify-between">
          
          {/* Left: Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" aria-label={siteMetadata.headerTitle} className="group flex items-center">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Logo className="h-10 w-10 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
                  <div className="bg-gradient-accent absolute -top-1 -right-1 h-3 w-3 rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                    <Sparkles className="absolute top-0.5 left-0.5 h-2 w-2 text-white" />
                  </div>
                </div>
                {typeof siteMetadata.headerTitle === 'string' ? (
                  <div className="bg-gradient-to-r from-gray-900 via-blue-600 to-gray-900 dark:from-white dark:via-blue-400 dark:to-white bg-clip-text text-xl font-bold text-transparent transition-all duration-300 group-hover:scale-105">
                    {siteMetadata.headerTitle}
                  </div>
                ) : (
                  siteMetadata.headerTitle
                )}
              </div>
            </Link>
          </div>

          {/* Center: Desktop Navigation */}
          <nav className="hidden md:flex items-center justify-center">
            <div className="flex items-center space-x-6">
              {headerNavLinks
                .filter((link) => link.href !== '/')
                .map((link) => (
                  <Link
                    key={link.title}
                    href={link.href}
                    className="group hover:text-primary-600 dark:hover:text-primary-400 relative rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-all duration-300 hover:bg-white/50 dark:text-gray-300 dark:hover:bg-gray-800/50"
                  >
                    <span className="relative z-10">{link.title}</span>
                    <div className="from-primary-500/10 to-accent-blue/10 absolute inset-0 scale-0 rounded-lg bg-gradient-to-r transition-transform duration-300 group-hover:scale-100" />
                    <div className="from-primary-500 to-accent-blue absolute bottom-0 left-1/2 h-0.5 w-0 bg-gradient-to-r transition-all duration-300 group-hover:left-0 group-hover:w-full" />
                  </Link>
                ))}
            </div>
          </nav>

          {/* Right: Action Items */}
          <div className="flex items-center space-x-3">
            {/* Search Button */}
            <div className="group relative">
              <SearchButton />
            </div>

            {/* Admin Panel */}
            <Link
              href="/admin"
              className="group hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-200 dark:hover:border-primary-700 shadow-soft hover:shadow-medium relative flex h-9 w-9 items-center justify-center rounded-lg border border-white/20 bg-white/50 backdrop-blur-sm transition-all duration-300 hover:scale-105 dark:border-gray-700/50 dark:bg-gray-800/50"
              aria-label="Admin Panel"
            >
              <Settings className="group-hover:text-primary-600 dark:group-hover:text-primary-400 h-4 w-4 text-gray-600 transition-colors duration-300 dark:text-gray-400" />
              <div className="bg-gradient-accent absolute -top-1 -right-1 h-2 w-2 rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            </Link>

            {/* Theme Switch */}
            <div className="relative">
              <ThemeSwitch />
            </div>

            {/* Mobile Navigation */}
            <div className="md:hidden">
              <MobileNav />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
