import siteMetadata from '@/data/siteMetadata'
import headerNavLinks from '@/data/headerNavLinks'
import Logo from '@/data/logo.svg'
import Link from './Link'
import MobileNav from './MobileNav'
import ThemeSwitch from './ThemeSwitch'
import SearchButton from './SearchButton'
import { Settings } from 'lucide-react'

const Header = () => {
  let headerClass = 'flex items-center w-full justify-center py-4 px-4'

  if (siteMetadata.stickyNav) {
    headerClass +=
      ' sticky top-0 z-50 backdrop-blur-md border-b border-gray-200/80 dark:border-gray-800/80 bg-white/95 dark:bg-gray-950/95'
  } else {
    headerClass +=
      ' bg-white/95 dark:bg-gray-950/95 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-800/50'
  }

  return (
    <header className={headerClass}>
      <div className="w-full max-w-7xl mx-auto">
        <div className="flex items-center justify-between">

          {/* Left: Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" aria-label={siteMetadata.headerTitle} className="group flex items-center">
              <div className="flex items-center space-x-3">
                <Logo className="h-8 w-8 transition-transform duration-200 group-hover:scale-105" />
                {typeof siteMetadata.headerTitle === 'string' ? (
                  <div className="text-xl font-display font-bold text-gray-900 dark:text-white transition-colors duration-200">
                    {siteMetadata.headerTitle}
                  </div>
                ) : (
                  siteMetadata.headerTitle
                )}
              </div>
            </Link>
          </div>

          {/* Center: Desktop Navigation */}
          <nav className="hidden md:flex items-center justify-center">
            <div className="flex items-center space-x-1">
              {headerNavLinks
                .filter((link) => link.href !== '/')
                .map((link) => (
                  <Link
                    key={link.title}
                    href={link.href}
                    className="relative px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 rounded-md hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
                  >
                    {link.title}
                  </Link>
                ))}
            </div>
          </nav>

          {/* Right: Action Items */}
          <div className="flex items-center space-x-2">
            {/* Search Button */}
            <SearchButton />

            {/* Admin Panel */}
            <Link
              href="/admin"
              className="flex h-9 w-9 items-center justify-center rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/80 dark:hover:bg-gray-800/80 transition-colors duration-200"
              aria-label="Admin Panel"
            >
              <Settings className="h-4 w-4" />
            </Link>

            {/* Theme Switch */}
            <ThemeSwitch />

            {/* Mobile Navigation */}
            <div className="md:hidden">
              <MobileNav />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
