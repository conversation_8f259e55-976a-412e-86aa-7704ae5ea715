'use client'

import { useState, useEffect } from 'react'
import { X, ExternalLink, Eye, Calendar, User, Clock, Tag as TagIcon } from 'lucide-react'
import { formatDate } from 'pliny/utils/formatDate'
import siteMetadata from '@/data/siteMetadata'
import Tag from './Tag'
import { isHtmlContent } from '../lib/content-utils'

interface PostPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  post: any
}

export default function PostPreviewModal({ isOpen, onClose, post }: PostPreviewModalProps) {
  const [htmlContent, setHtmlContent] = useState('')

  useEffect(() => {
    if (post?.content) {
      // If content is already HTML (from Rich Text Editor), use it directly
      if (isHtmlContent(post.content)) {
        setHtmlContent(post.content)
      } else {
        // Enhanced markdown to HTML conversion for preview
        const convertMarkdownToHtml = (markdown: string) => {
          return markdown
            // Headers
            .replace(/^### (.*$)/gm, '<h3 class="text-xl font-semibold mb-3 mt-6 text-gray-900 dark:text-gray-100">$1</h3>')
            .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-semibold mb-4 mt-8 text-gray-900 dark:text-gray-100">$1</h2>')
            .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold mb-6 mt-10 text-gray-900 dark:text-gray-100">$1</h1>')
            // Bold and italic
            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900 dark:text-gray-100">$1</strong>')
            .replace(/\*(.*?)\*/g, '<em class="italic text-gray-800 dark:text-gray-200">$1</em>')
            // Inline code
            .replace(
              /\`(.*?)\`/g,
              '<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono text-gray-900 dark:text-gray-100 border">$1</code>'
            )
            // Code blocks
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4 border"><code class="text-sm font-mono text-gray-900 dark:text-gray-100">$2</code></pre>')
            // Links
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>')
            // Images
            .replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-sm mb-4" />')
            // Lists
            .replace(/^\* (.+)$/gm, '<li class="mb-1 text-gray-700 dark:text-gray-300">$1</li>')
            .replace(/(<li.*<\/li>)/g, '<ul class="list-disc list-inside mb-4 space-y-1">$1</ul>')
            // Blockquotes
            .replace(/^> (.+)$/gm, '<blockquote class="border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 mb-4">$1</blockquote>')
            // Line breaks and paragraphs
            .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">')
            .replace(/\n/g, '<br>')
            .replace(/^(.+)$/gm, '<p class="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">$1</p>')
            .replace(/(<\/p><p[^>]*>){2,}/g, '</p><p class="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">')
        }
        setHtmlContent(convertMarkdownToHtml(post.content))
      }
    }
  }, [post])

  if (!isOpen) return null

  const handleOpenInNewTab = () => {
    if (post?.slug) {
      window.open(`/blog/preview/${post.slug}`, '_blank')
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="bg-opacity-75 fixed inset-0 bg-gray-900 transition-opacity backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Center modal */}
        <span className="hidden sm:inline-block sm:h-screen sm:align-middle">&#8203;</span>

        <div className="inline-block w-full max-w-6xl transform overflow-hidden rounded-xl bg-white text-left align-bottom shadow-2xl transition-all sm:my-8 sm:align-middle dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-800 px-6 py-4 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Preview Post</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  This is how your post will appear to readers
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {post?.slug && (
                <button
                  onClick={handleOpenInNewTab}
                  className="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Open in New Tab
                </button>
              )}
              <button
                onClick={onClose}
                className="rounded-lg p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Preview Content */}
          <div className="max-h-[70vh] overflow-y-auto">
            {post && (
              <article className="mx-auto max-w-4xl px-6 py-8">
                {/* Post Header */}
                <header className="mb-8">
                  <div className="space-y-4 border-b border-gray-200 pb-8 dark:border-gray-700">
                    {/* Meta information */}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <time dateTime={post.date}>
                          {formatDate(post.date, siteMetadata.locale)}
                        </time>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4" />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{post.readTime}</span>
                      </div>
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          post.status === 'published'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : post.status === 'draft'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}
                      >
                        {post.status?.charAt(0).toUpperCase() + post.status?.slice(1)}
                      </span>
                    </div>
                    
                    {/* Title */}
                    <div>
                      <h1 className="text-3xl font-bold leading-tight tracking-tight text-gray-900 sm:text-4xl md:text-5xl dark:text-gray-100">
                        {post.title}
                      </h1>
                    </div>
                    
                    {/* Description */}
                    {post.description && (
                      <p className="text-lg leading-relaxed text-gray-600 dark:text-gray-400">
                        {post.description}
                      </p>
                    )}
                  </div>
                </header>

                {/* Tags */}
                {post.tags && post.tags.length > 0 && (
                  <div className="mb-8">
                    <div className="flex items-center space-x-2 mb-3">
                      <TagIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Tags</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag: string) => (
                        <Tag key={tag} text={tag} />
                      ))}
                    </div>
                  </div>
                )}

                {/* Post Content */}
                <div className="prose prose-lg dark:prose-invert max-w-none">
                  <div
                    dangerouslySetInnerHTML={{ __html: htmlContent }}
                    className="prose max-w-none text-gray-900 dark:text-gray-100"
                  />
                </div>

                {/* Affiliate Disclosure */}
                {post.showAffiliateDisclosure && (
                  <div className="mt-8 rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-900/20">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/30">
                          <span className="text-xs font-bold text-amber-600 dark:text-amber-400">!</span>
                        </div>
                      </div>
                      <div className="text-sm text-amber-800 dark:text-amber-200">
                        <p className="font-medium mb-1">Affiliate Disclosure</p>
                        <p>
                          This post may contain affiliate links. If you make a purchase through these links, 
                          I may earn a commission at no additional cost to you. I only recommend products 
                          and services I personally use and believe will add value to my readers.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Post Footer */}
                <footer className="mt-12 border-t border-gray-200 pt-8 dark:border-gray-700">
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-4">
                      <span>By {post.author}</span>
                      <span>•</span>
                      <span>{post.readTime}</span>
                      {post.view_count !== undefined && (
                        <>
                          <span>•</span>
                          <span>{post.view_count} views</span>
                        </>
                      )}
                    </div>
                    <div className="text-xs text-gray-400 dark:text-gray-500">
                      Preview Mode
                    </div>
                  </div>
                </footer>
              </article>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex justify-between">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                💡 Tip: Use "Open in New Tab" for a full-screen preview experience
              </div>
              <button
                onClick={onClose}
                className="rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                Close Preview
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
