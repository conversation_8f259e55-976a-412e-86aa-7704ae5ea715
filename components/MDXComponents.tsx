import TOCInline from 'pliny/ui/TOCInline'
import BlogNewsletterForm from 'pliny/ui/BlogNewsletterForm'
import type { MDXComponents } from 'mdx/types'
import Image from './Image'
import CustomLink from './Link'
import TableWrapper from './TableWrapper'
import EnhancedPre from './EnhancedPre'
import { isMarkdownTable } from '@/lib/markdown-utils'

// Enhanced table component that handles different table formats
const EnhancedTable = (props: React.HTMLAttributes<HTMLTableElement>) => {
  // Check if this is a direct table or needs a wrapper
  return <TableWrapper>{props.children}</TableWrapper>;
};

// Enhanced pre component that can detect and render markdown tables
const EnhancedPreWithTable = (props: any) => {
  // Check if the content might be a markdown table
  if (props.children?.props?.children && typeof props.children.props.children === 'string') {
    const content = props.children.props.children;
    if (isMarkdownTable(content)) {
      // This looks like a markdown table, render with TableWrapper
      return <TableWrapper>{props.children}</TableWrapper>;
    }
  }
  
  // Not a markdown table, use regular EnhancedPre
  return <EnhancedPre {...props} />;
};

export const components: MDXComponents = {
  Image,
  TOCInline,
  a: CustomLink,
  pre: EnhancedPreWithTable,
  table: EnhancedTable,
  BlogNewsletterForm,
}
