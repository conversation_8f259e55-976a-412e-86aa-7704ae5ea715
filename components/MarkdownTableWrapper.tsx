// MarkdownTableWrapper.tsx
import React, { useEffect, useRef } from 'react';

interface MarkdownTableProps {
  content: string;
}

const MarkdownTableWrapper: React.FC<MarkdownTableProps> = ({ content }) => {
  const tableRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (tableRef.current) {
      const tableContent = content.trim();
      
      // Parse the markdown table
      const rows = tableContent.split('\n');
      if (rows.length < 3) return; // Need at least header, separator, and one data row
      
      // Extract headers
      const headers = rows[0].split('|')
        .filter(cell => cell.trim() !== '')
        .map(header => header.trim());
      
      // Create HTML table
      const table = document.createElement('table');
      table.className = 'markdown-table';
      
      // Create header
      const thead = document.createElement('thead');
      const headerRow = document.createElement('tr');
      
      headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
      });
      
      thead.appendChild(headerRow);
      table.appendChild(thead);
      
      // Create body
      const tbody = document.createElement('tbody');
      
      // Skip the first two rows (header and separator)
      for (let i = 2; i < rows.length; i++) {
        if (!rows[i].trim()) continue;
        
        const row = document.createElement('tr');
        const cells = rows[i].split('|')
          .filter(cell => cell.trim() !== '')
          .map(cell => cell.trim());
        
        cells.forEach(cell => {
          const td = document.createElement('td');
          td.textContent = cell;
          row.appendChild(td);
        });
        
        tbody.appendChild(row);
      }
      
      table.appendChild(tbody);
      
      // Clear and append
      tableRef.current.innerHTML = '';
      tableRef.current.appendChild(table);
    }
  }, [content]);
  
  return (
    <div className="w-full overflow-x-auto my-8 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      <div ref={tableRef} />
    </div>
  );
};

export default MarkdownTableWrapper;
