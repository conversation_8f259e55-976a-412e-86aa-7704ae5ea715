'use client'

import { useEffect, useRef } from 'react'

interface ViewTrackerProps {
  slug: string
  source?: 'supabase' | 'mdx'
}

export default function ViewTracker({ slug, source = 'supabase' }: ViewTrackerProps) {
  const hasTracked = useRef(false)

  useEffect(() => {
    // Only track for Supabase posts and only once per page load
    if (source === 'supabase' && !hasTracked.current && slug) {
      trackView(slug)
      hasTracked.current = true
    }
  }, [slug, source])

  const trackView = async (postSlug: string) => {
    try {
      // Check if we've already tracked this post in this session
      const sessionKey = `viewed_${postSlug}`
      const hasViewedInSession = sessionStorage.getItem(sessionKey)

      if (!hasViewedInSession) {
        await fetch('/api/views', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ slug: postSlug }),
        })

        // Mark as viewed in this session to prevent duplicate tracking
        sessionStorage.setItem(sessionKey, 'true')
      }
    } catch (error) {
      console.error('Failed to track view:', error)
    }
  }

  // This component doesn't render anything
  return null
}
