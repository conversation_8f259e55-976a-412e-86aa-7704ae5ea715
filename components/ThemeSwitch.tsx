'use client'

import { Fragment, useEffect, useState } from 'react'
import { useTheme } from 'next-themes'
import { createPortal } from 'react-dom'
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Radio,
  RadioGroup,
  Transition,
} from '@headlessui/react'

const Sun = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="currentColor"
    className="h-5 w-5 transition-all duration-300"
  >
    <path
      fillRule="evenodd"
      d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
      clipRule="evenodd"
    />
  </svg>
)

const Moon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="currentColor"
    className="h-5 w-5 transition-all duration-300"
  >
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
  </svg>
)

const Monitor = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 20 20"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-5 w-5 transition-all duration-300"
  >
    <rect x="3" y="3" width="14" height="10" rx="2" ry="2"></rect>
    <line x1="7" y1="17" x2="13" y2="17"></line>
    <line x1="10" y1="13" x2="10" y2="17"></line>
  </svg>
)

const Blank = () => <div className="h-5 w-5" />

const ThemeSwitch = () => {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme, resolvedTheme } = useTheme()

  // When mounted on client, now we can show the UI
  useEffect(() => setMounted(true), [])

  const getCurrentIcon = () => {
    if (!mounted) return <Blank />
    return resolvedTheme === 'dark' ? <Moon /> : <Sun />
  }

  return (
    <div className="relative">
      <Menu as="div" className="relative inline-block text-left">
        <div>
          <MenuButton
            className="group hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-200 dark:hover:border-primary-700 shadow-soft hover:shadow-medium relative flex h-10 w-10 items-center justify-center rounded-lg border border-white/20 bg-white/50 text-gray-600 backdrop-blur-sm transition-all duration-300 hover:scale-105 dark:border-gray-700/50 dark:bg-gray-800/50 dark:text-gray-400"
            aria-label="Theme switcher"
          >
            <div className="relative">
              {getCurrentIcon()}
              <div className="bg-gradient-accent absolute -top-1 -right-1 h-2 w-2 rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            </div>
          </MenuButton>
        </div>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-200"
          enterFrom="transform opacity-0 scale-95 translate-y-1"
          enterTo="transform opacity-100 scale-100 translate-y-0"
          leave="transition ease-in duration-150"
          leaveFrom="transform opacity-100 scale-100 translate-y-0"
          leaveTo="transform opacity-0 scale-95 translate-y-1"
        >
          <MenuItems 
            anchor="bottom end"
            className="shadow-strong z-[99999] w-48 rounded-xl border border-white/20 bg-white/95 ring-1 ring-black/5 backdrop-blur-lg focus:outline-none dark:border-gray-700/50 dark:bg-gray-800/95 dark:ring-white/10"
            style={{ position: 'fixed', zIndex: 99999 }}
          >
            <div className="p-2">
              <RadioGroup value={theme} onChange={setTheme}>
                <div className="space-y-1">
                  {[
                    { value: 'light', icon: Sun, label: 'Light', desc: 'Bright and clean' },
                    { value: 'dark', icon: Moon, label: 'Dark', desc: 'Easy on the eyes' },
                    { value: 'system', icon: Monitor, label: 'System', desc: 'Follow device' },
                  ].map((option) => (
                    <Radio key={option.value} value={option.value}>
                      <MenuItem>
                        {({ focus }) => {
                          const isSelected = theme === option.value
                          return (
                            <button
                              className={`group flex w-full items-center rounded-lg px-3 py-2.5 text-sm transition-all duration-200 ${
                                focus || isSelected
                                  ? 'from-primary-500 to-primary-600 shadow-medium bg-gradient-to-r text-white'
                                  : 'text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-700/50'
                              }`}
                            >
                              <div
                                className={`mr-3 transition-all duration-200 ${
                                  focus || isSelected
                                    ? 'scale-110 text-white'
                                    : 'text-gray-500 dark:text-gray-400'
                                }`}
                              >
                                <option.icon />
                              </div>
                              <div className="text-left">
                                <div className="font-medium">{option.label}</div>
                                <div
                                  className={`text-xs ${
                                    focus || isSelected
                                      ? 'text-white/80'
                                      : 'text-gray-500 dark:text-gray-400'
                                  }`}
                                >
                                  {option.desc}
                                </div>
                              </div>
                              {isSelected && (
                                <div className="ml-auto">
                                  <div className="h-2 w-2 animate-pulse rounded-full bg-white" />
                                </div>
                              )}
                            </button>
                          )
                        }}
                      </MenuItem>
                    </Radio>
                  ))}
                </div>
              </RadioGroup>
            </div>
          </MenuItems>
        </Transition>
      </Menu>
    </div>
  )
}

export default ThemeSwitch
