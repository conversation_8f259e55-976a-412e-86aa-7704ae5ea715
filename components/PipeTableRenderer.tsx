// PipeTableRenderer.tsx
import React from 'react';

interface PipeTableRendererProps {
  markdown: string;
}

/**
 * A component that renders markdown pipe tables directly
 */
const PipeTableRenderer: React.FC<PipeTableRendererProps> = ({ markdown }) => {
  // Parse the markdown table
  const lines = markdown.trim().split('\n');
  
  // Extract headers (first row)
  const headerLine = lines[0];
  const headers = headerLine
    .split('|')
    .filter(cell => cell.trim() !== '')
    .map(header => header.trim());
  
  // Skip the separator line (second row)
  
  // Extract data rows (remaining rows)
  const rows = lines.slice(2).filter(line => line.trim() !== '');
  
  return (
    <div className="w-full overflow-x-auto my-8 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      <table className="w-full markdown-table">
        <thead>
          <tr>
            {headers.map((header, index) => (
              <th key={`header-${index}`}>{header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => {
            const cells = row
              .split('|')
              .filter(cell => cell.trim() !== '')
              .map(cell => cell.trim());
              
            return (
              <tr key={`row-${rowIndex}`}>
                {cells.map((cell, cellIndex) => (
                  <td key={`cell-${rowIndex}-${cellIndex}`}>{cell}</td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default PipeTableRenderer;
