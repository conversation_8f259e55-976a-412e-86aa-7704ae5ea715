'use client'

import { useEffect, useState } from 'react'
import { ChevronRight, List, Clock, BookOpen, Eye, EyeOff } from 'lucide-react'

interface TOCItem {
  id: string
  title: string
  level: number
}

interface PostTableOfContentsProps {
  className?: string
}

export default function PostTableOfContents({ className = '' }: PostTableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TOCItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [estimatedReadTime, setEstimatedReadTime] = useState(0)

  useEffect(() => {
    // Generate TOC from article headings
    const generateTOC = () => {
      const article = document.querySelector('article')
      const proseContent = document.querySelector('.prose-content')

      if (!article || !proseContent) return

      // Look for headings in the prose content specifically
      const headings = proseContent.querySelectorAll('h1, h2, h3, h4, h5, h6')
      const items: TOCItem[] = []

      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1))
        let id = heading.id

        // Generate ID if not present
        if (!id) {
          id = `heading-${index}`
          heading.id = id
        }

        items.push({
          id,
          title: heading.textContent || '',
          level,
        })
      })

      setTocItems(items)
    }

    // Calculate reading time
    const calculateReadingTime = () => {
      const proseContent = document.querySelector('.prose-content')
      if (!proseContent) return

      const text = proseContent.textContent || ''
      const wordsPerMinute = 200 // Average reading speed
      const words = text.trim().split(/\s+/).length
      const time = Math.ceil(words / wordsPerMinute)
      setEstimatedReadTime(time)
    }

    // Wait for content to be loaded before generating TOC
    const initializeTOC = () => {
      const proseContent = document.querySelector('.prose-content')
      if (proseContent && proseContent.children.length > 0) {
        generateTOC()
        calculateReadingTime()
      } else {
        // Retry after a short delay if content isn't ready
        setTimeout(initializeTOC, 500)
      }
    }

    // Initial attempt
    initializeTOC()

    // Also listen for content changes (when the HTML content is injected)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if prose-content was added or updated
          const addedElements = Array.from(mutation.addedNodes)
          const hasProseContent = addedElements.some(
            (node) =>
              node instanceof Element &&
              (node.classList.contains('prose-content') || node.querySelector('.prose-content'))
          )

          if (hasProseContent) {
            setTimeout(() => {
              generateTOC()
              calculateReadingTime()
            }, 100)
          }
        }
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      // Show TOC after scrolling past header
      const scrolled = window.scrollY > 300
      setIsVisible(scrolled)

      // Calculate reading progress
      const article = document.querySelector('article')
      if (!article) return

      const articleTop = article.offsetTop
      const articleHeight = article.offsetHeight
      const windowHeight = window.innerHeight
      const scrollTop = window.scrollY

      const progress = Math.min(
        Math.max((scrollTop - articleTop + windowHeight / 2) / articleHeight, 0),
        1
      )
      setReadingProgress(progress * 100)

      // Update active heading
      const headings = document.querySelectorAll(
        '.prose-content h1, .prose-content h2, .prose-content h3, .prose-content h4, .prose-content h5, .prose-content h6'
      )
      let activeHeading = ''

      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect()
        if (rect.top <= 100 && rect.bottom >= 0) {
          activeHeading = heading.id
        }
      })

      setActiveId(activeHeading)
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Initial call

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      const yOffset = -80 // Account for fixed header
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset
      window.scrollTo({ top: y, behavior: 'smooth' })
    }
  }

  if (!isVisible || tocItems.length === 0) {
    return null
  }

  return (
    <div className={`fixed top-24 right-4 z-40 hidden xl:block transition-all duration-300 ${className}`}>
      <div
        className={`rounded-2xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-md shadow-xl border border-white/20 dark:border-gray-700/50 transition-all duration-300 hover:shadow-2xl ${
          isExpanded ? 'w-84' : 'w-72'
        } max-h-[calc(100vh-8rem)] overflow-hidden`}
      >
        {/* Header with Toggle */}
        <div className="border-b border-gray-200/50 dark:border-gray-700/50 p-4 bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-gray-800/50 dark:to-gray-900/50">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="group flex w-full items-center justify-between text-sm font-semibold text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
          >
            <div className="flex items-center gap-3">
              <div className="p-1.5 rounded-lg bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400">
                <BookOpen className="h-4 w-4" />
              </div>
              <span>Table of Contents</span>
            </div>
            <div className="flex items-center gap-2">
              {isExpanded ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
              <ChevronRight
                className={`h-4 w-4 transition-transform duration-300 ${isExpanded ? 'rotate-90' : ''}`}
              />
            </div>
          </button>

          {/* Reading Progress - restored */}
          <div className="mt-4 space-y-3">
            <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
                <span>{estimatedReadTime} min read</span>
              </div>
              <span className="font-medium">{Math.round(readingProgress)}% complete</span>
            </div>
            
            {/* Progress Bar */}
            <div className="relative h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden">
              <div
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full transition-all duration-500 ease-out shadow-sm"
                style={{ width: `${readingProgress}%` }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
            </div>
          </div>
        </div>

        {/* Table of Contents - expandable */}
        <div className={`transition-all duration-300 ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
          <div className="p-4">
            <nav className="space-y-1 max-h-80 overflow-y-auto custom-scrollbar">
              {tocItems.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => scrollToHeading(item.id)}
                  className={`group w-full rounded-lg px-3 py-2.5 text-left text-sm transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/50 ${
                    activeId === item.id
                      ? 'bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/30 dark:to-blue-900/30 text-primary-700 dark:text-primary-300 border-l-3 border-primary-500 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                  }`}
                  style={{
                    paddingLeft: `${(item.level - 1) * 12 + 12}px`,
                    animationDelay: `${index * 0.05}s`,
                  }}
                >
                  <div className="flex items-start gap-2">
                    {item.level > 1 && (
                      <ChevronRight className="h-3 w-3 opacity-50 mt-0.5 flex-shrink-0" />
                    )}
                    <span className="line-clamp-2 leading-relaxed group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {item.title}
                    </span>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Collapsed State Indicators */}
        {!isExpanded && tocItems.length > 0 && (
          <div className="p-4 border-t border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>{tocItems.length} sections</span>
              <div className="flex gap-1">
                {tocItems.slice(0, 5).map((_, index) => (
                  <div
                    key={index}
                    className={`h-1 w-3 rounded-full transition-colors duration-300 ${
                      index < (readingProgress / 100) * tocItems.length
                        ? 'bg-primary-500'
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
