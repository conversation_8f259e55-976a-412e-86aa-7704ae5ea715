@reference "./tailwind.css";

/**
 * Enhanced CSS Styles for code highlighting.
 * Based on Night Owl theme with modern improvements
 * Feel free to customize token styles 
 * by copying from a prismjs compatible theme:
 * https://github.com/PrismJS/prism-themes
 */

/* Enhanced Code title styles */
.remark-code-title {
  @apply rounded-t-xl bg-gradient-to-r from-gray-700 to-gray-800 px-6 py-4 font-mono text-sm font-bold text-gray-200 shadow-lg;
  @apply dark:from-gray-800 dark:to-gray-900;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.remark-code-title + div > pre {
  @apply mt-0 rounded-t-none;
}

/* Enhanced Code block styles */
.code-highlight {
  @apply float-left min-w-full;
}

.code-line {
  @apply -mx-4 block border-l-4 border-transparent pr-4 pl-4 transition-colors duration-200;
}

.code-line:hover {
  @apply bg-gray-800/30;
}

.code-line.inserted {
  @apply bg-green-500/20 border-l-green-400;
}

.code-line.deleted {
  @apply bg-red-500/20 border-l-red-400;
}

.code-line.highlighted {
  @apply border-l-blue-400 bg-blue-500/20;
}

.highlight-line {
  @apply border-primary-500 -mx-4 border-l-4 bg-primary-500/20;
}

.line-number {
  @apply mr-6 inline-block w-8 select-none text-right text-xs text-gray-500 font-mono;
  @apply dark:text-gray-400;
}

.line-number::before {
  @apply mr-4 -ml-2 inline-block w-4 text-right text-gray-400;
  content: attr(line);
}

/* Enhanced Code Block Container */
.prose-code-block {
  @apply relative my-8 overflow-hidden rounded-xl shadow-lg transition-shadow duration-300 hover:shadow-xl;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 400% 400%;
  animation: gradient-x 3s ease infinite;
  padding: 2px;
}

.prose-code-block > * {
  @apply bg-white dark:bg-gray-900 rounded-[10px];
}

.prose-code-header {
  @apply flex items-center justify-between bg-gradient-to-r from-gray-50 to-gray-100;
  @apply dark:from-gray-800 dark:to-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-700;
}

/* macOS-style window controls */
.window-controls {
  @apply flex space-x-2;
}

.window-dot {
  @apply h-3 w-3 rounded-full transition-colors duration-200;
}

.window-dot.red { @apply bg-red-400 hover:bg-red-500; }
.window-dot.yellow { @apply bg-yellow-400 hover:bg-yellow-500; }
.window-dot.green { @apply bg-green-400 hover:bg-green-500; }

/* Token styles */
/**
 * MIT License
 * Copyright (c) 2018 Sarah Drasner
 * Sarah Drasner's[@sdras] Night Owl
 * Ported by Sara vieria [@SaraVieira]
 * Added by Souvik Mandal [@SimpleIndian]
 */
.token.comment,
.token.prolog,
.token.cdata {
  color: rgb(99, 119, 119);
  font-style: italic;
}

.token.punctuation {
  color: rgb(199, 146, 234);
}

.namespace {
  color: rgb(178, 204, 214);
}

.token.deleted {
  color: rgba(239, 83, 80, 0.56);
  font-style: italic;
}

.token.symbol,
.token.property {
  color: rgb(128, 203, 196);
}

.token.tag,
.token.operator,
.token.keyword {
  color: rgb(127, 219, 202);
}

.token.boolean {
  color: rgb(255, 88, 116);
}

.token.number {
  color: rgb(247, 140, 108);
}

.token.constant,
.token.function,
.token.builtin,
.token.char {
  color: rgb(130, 170, 255);
}

.token.selector,
.token.doctype {
  color: rgb(199, 146, 234);
  font-style: italic;
}

.token.attr-name,
.token.inserted {
  color: rgb(173, 219, 103);
  font-style: italic;
}

.token.string,
.token.url,
.token.entity,
.language-css .token.string,
.style .token.string {
  color: rgb(173, 219, 103);
}

.token.class-name,
.token.atrule,
.token.attr-value {
  color: rgb(255, 203, 139);
}

.token.regex,
.token.important,
.token.variable {
  color: rgb(214, 222, 235);
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.table {
  display: inline;
}
