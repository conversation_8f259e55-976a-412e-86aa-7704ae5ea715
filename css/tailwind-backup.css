@import 'tailwindcss';
@plugin "@tailwindcss/forms";
@plugin '@tailwindcss/typography';
@source '../node_modules/pliny';
@custom-variant dark (&:where(.dark, .dark *));

:root {
  --toast-bg: white;
  --toast-color: #374151;
}

:root.dark,
.dark {
  --toast-bg: #1f2937;
  --toast-color: #f9fafb;
}

@theme {
  --font-sans: var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --color-primary-50: oklch(0.971 0.014 343.198);
  --color-primary-100: oklch(0.948 0.028 342.258);
  --color-primary-200: oklch(0.899 0.061 343.231);
  --color-primary-300: oklch(0.823 0.12 346.018);
  --color-primary-400: oklch(0.718 0.202 349.761);
  --color-primary-500: oklch(0.656 0.241 354.308);
  --color-primary-600: oklch(0.592 0.249 0.584);
  --color-primary-700: oklch(0.525 0.223 3.958);
  --color-primary-800: oklch(0.459 0.187 3.815);
  --color-primary-900: oklch(0.408 0.153 2.432);
  --color-primary-950: oklch(0.284 0.109 3.907);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  --line-height-11: 2.75rem;
  --line-height-12: 3rem;
  --line-height-13: 3.25rem;
  --line-height-14: 3.5rem;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  a,
  button {
    outline-color: var(--color-primary-500);
  }

  a:focus-visible,
  button:focus-visible {
    outline: 2px solid;
    border-radius: var(--radius-sm);
    outline-color: var(--color-primary-500);
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .prose a {
    color: var(--color-primary-500);
    text-decoration: none;
    font-weight: 500;
  }

  .prose a:hover {
    color: var(--color-primary-600);
  }

  .prose a code {
    color: var(--color-primary-400);
    background: rgba(var(--color-primary-100), 0.3);
    padding: 0.2rem 0.4rem;
    border-radius: 0.375rem;
  }

  .prose :where(h1, h2) {
    font-weight: 700;
    letter-spacing: var(--tracking-tight);
    color: var(--color-gray-900);
  }

  .prose h3 {
    font-weight: 600;
    color: var(--color-gray-800);
  }

  .prose :where(code):not(pre code) {
    color: var(--color-indigo-500);
    background: rgba(var(--color-indigo-100), 0.3);
    padding: 0.2rem 0.4rem;
    border-radius: 0.375rem;
    font-weight: 500;
  }

  .prose blockquote {
    border-left: 4px solid var(--color-primary-500);
    background: rgba(var(--color-primary-50), 0.5);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 1.5rem 0;
  }

  .prose-invert a {
    color: var(--color-primary-400);
  }

  .prose-invert a:hover {
    color: var(--color-primary-300);
  }

  .prose-invert a code {
    color: var(--color-primary-400);
    background: rgba(var(--color-primary-900), 0.3);
  }

  .prose-invert :where(h1, h2, h3, h4, h5, h6) {
    color: var(--color-gray-100);
  }

  .prose-invert blockquote {
    border-left: 4px solid var(--color-primary-400);
    background: rgba(var(--color-primary-900), 0.3);
  }
}
