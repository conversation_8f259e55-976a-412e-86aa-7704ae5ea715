@import 'tailwindcss';
@plugin "@tailwindcss/forms";
@plugin '@tailwindcss/typography';
@source '../node_modules/pliny';
@custom-variant dark (&:where(.dark, .dark *));

/* Enhanced CSS Variables */
:root {
  --toast-bg: white;
  --toast-color: #374151;
}

:root.dark,
.dark {
  --toast-bg: #1f2937;
  --toast-color: #f9fafb;
}

/* Core theme configuration */
@theme {
  /* Font families */
  --font-sans: var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  /* Enhanced color palette */
  --color-primary-50: oklch(0.971 0.014 343.198);
  --color-primary-100: oklch(0.948 0.028 342.258);
  --color-primary-200: oklch(0.899 0.061 343.231);
  --color-primary-300: oklch(0.823 0.12 346.018);
  --color-primary-400: oklch(0.718 0.202 349.761);
  --color-primary-500: oklch(0.656 0.241 354.308);
  --color-primary-600: oklch(0.592 0.249 0.584);
  --color-primary-700: oklch(0.525 0.223 3.958);
  --color-primary-800: oklch(0.459 0.187 3.815);
  --color-primary-900: oklch(0.408 0.153 2.432);
  --color-primary-950: oklch(0.284 0.109 3.907);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  /* Line heights */
  --line-height-11: 2.75rem;
  --line-height-12: 3rem;
  --line-height-13: 3.25rem;
  --line-height-14: 3.5rem;

  /* Z-index values */
  --z-60: 60;
  --z-70: 70;
  --z-80: 80;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  a,
  button {
    outline-color: var(--color-primary-500);
  }

  a:focus-visible,
  button:focus-visible {
    outline: 2px solid;
    border-radius: var(--radius-sm);
    outline-color: var(--color-primary-500);
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  /* Enhanced prose styling */
  .prose {
    & a {
      color: var(--color-primary-500);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        color: var(--color-primary-600);
        transform: translateY(-1px);
      }
      
      & code {
        color: var(--color-primary-400);
        background: rgba(var(--color-primary-100), 0.3);
        padding: 0.2rem 0.4rem;
        border-radius: 0.375rem;
        border: 2px solid transparent;
        background-clip: padding-box;
        position: relative;
      }
      
      & code::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
        background-size: 200% 200%;
        animation: gradient-x 3s ease infinite;
        border-radius: 8px;
        z-index: -1;
      }
    }
    
    & :where(h1, h2) {
      font-weight: 700;
      letter-spacing: var(--tracking-tight);
      color: var(--color-gray-900);
    }
    
    & h3 {
      font-weight: 600;
      color: var(--color-gray-800);
    }
    
    & :where(code):not(pre code) {
      color: var(--color-indigo-500);
      background: rgba(var(--color-indigo-100), 0.3);
      padding: 0.2rem 0.4rem;
      border-radius: 0.375rem;
      font-weight: 500;
      border: 2px solid transparent;
      background-clip: padding-box;
      position: relative;
    }

    & :where(code):not(pre code)::before {
      content: '';
      position: absolute;
      inset: -2px;
      background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
      background-size: 200% 200%;
      animation: gradient-x 3s ease infinite;
      border-radius: 8px;
      z-index: -1;
    }

    & blockquote {
      border-left: 4px solid var(--color-primary-500);
      background: linear-gradient(135deg, 
        rgba(var(--color-primary-50), 0.5), 
        rgba(var(--color-blue-50), 0.1));
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin: 1.5rem 0;
      position: relative;
      
      &::before {
        content: '"';
        position: absolute;
        top: -0.5rem;
        left: 1rem;
        font-size: 4rem;
        color: var(--color-primary-300);
        font-family: serif;
        line-height: 1;
      }
    }
  }

  .prose-invert {
    & a {
      color: var(--color-primary-400);
      
      &:hover {
        color: var(--color-primary-300);
      }
      
      & code {
        color: var(--color-primary-400);
        background: rgba(var(--color-primary-900), 0.3);
        border: 2px solid transparent;
        background-clip: padding-box;
        position: relative;
      }
      
      & code::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: linear-gradient(45deg, #60a5fa, #a78bfa, #f472b6);
        background-size: 200% 200%;
        animation: gradient-x 3s ease infinite;
        border-radius: 8px;
        z-index: -1;
      }
    }
    
    & :where(h1, h2, h3, h4, h5, h6) {
      color: var(--color-gray-100);
    }

    & blockquote {
      border-left: 4px solid var(--color-primary-400);
      background: linear-gradient(135deg, 
        rgba(var(--color-primary-900), 0.3), 
        rgba(var(--color-blue-900), 0.1));
      
      &::before {
        color: var(--color-primary-500);
      }
    }
  }

  /* Gradient utilities */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  }

  .bg-gradient-warm {
    background: linear-gradient(135deg, #f59e0b, #f97316, #ef4444);
  }

  .bg-gradient-cool {
    background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6);
  }

  .bg-gradient-accent {
    background: linear-gradient(135deg, #ec4899, #8b5cf6);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #10b981, #06b6d4);
  }

  /* Animation keyframes */
    .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 7s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }

  .animate-gradient-x {
    animation: gradient-x 3s ease infinite;
    background-size: 400% 400%;
  }

  .bg-size-200 {
    background-size: 200% 200%;
  }

  .bg-pos-0 {
    background-position: 0% 50%;
  }

  .bg-pos-100 {
    background-position: 100% 50%;
  }

  .animate-dot-glow {
    animation: dotGlow 2s ease-in-out infinite alternate;
  }
  .shadow-dot-glow-red {
    box-shadow: 0 0 8px 2px #f472b6, 0 0 16px 4px #fbbf24;
  }
  .shadow-dot-glow-yellow {
    box-shadow: 0 0 8px 2px #fbbf24, 0 0 16px 4px #34d399;
  }
  .shadow-dot-glow-green {
    box-shadow: 0 0 8px 2px #34d399, 0 0 16px 4px #818cf8;
  }

  /* Layout Balance Utilities */
  .balanced-layout {
    @apply min-h-screen flex flex-col;
  }
  
  .balanced-header {
    @apply flex-shrink-0;
  }
  
  .balanced-main {
    @apply flex-1 flex flex-col;
  }
  
  .balanced-content {
    @apply flex-1 py-8;
  }
  
  .balanced-footer {
    @apply flex-shrink-0 mt-auto;
  }
  
  .flex-2 {
    flex: 2;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(-10deg);
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes dotGlow {
  0% {
    filter: brightness(1) drop-shadow(0 0 2px #fff6) drop-shadow(0 0 4px #fff3);
  }
  100% {
    filter: brightness(1.3) drop-shadow(0 0 8px #fff8) drop-shadow(0 0 16px #fff5);
  }
}

.task-list-item::before {
  @apply hidden;
}

.task-list-item {
  @apply list-none;
}

.footnotes {
  @apply mt-12 border-t border-gray-200 pt-8 dark:border-gray-700;
}

.data-footnote-backref {
  @apply no-underline;
}

.csl-entry {
  @apply my-5;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* https://stackoverflow.com/questions/61083813/how-to-avoid-internal-autofill-selected-style-to-be-applied */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition:
    background-color 600000s 0s,
    color 600000s 0s;
}

.katex-display {
  overflow: auto hidden;
}

.content-header-link {
  opacity: 0;
  margin-left: -24px;
  padding-right: 4px;
}

.content-header:hover .content-header-link,
.content-header-link:hover {
  opacity: 1;
}

.linkicon {
  display: inline-block;
  vertical-align: middle;
}

/* Tiptap Editor Styles */
.ProseMirror {
  outline: none;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-weight: bold;
}

.ProseMirror h1 { font-size: 2rem; }
.ProseMirror h2 { font-size: 1.5rem; }
.ProseMirror h3 { font-size: 1.25rem; }

.ProseMirror p {
  margin: 1rem 0;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 1rem 0;
  padding-left: 1rem;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 4px solid #3b82f6;
  background: #eff6ff;
  padding: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

.dark .ProseMirror blockquote {
  background: #1e3a8a;
  color: #bfdbfe;
}

.ProseMirror code {
  background: #f1f5f9;
  color: #b91c1c;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-family: monospace;
}

.dark .ProseMirror code {
  background: #374151;
  color: #fca5a5;
}

.ProseMirror table {
  border-collapse: collapse;
  margin: 1rem 0;
  width: 100%;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  text-align: left;
}

.dark .ProseMirror table td,
.dark .ProseMirror table th {
  border-color: #4b5563;
}

.ProseMirror table .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.ProseMirror img {
  height: auto;
  max-width: 100%;
}

.ProseMirror .youtube-video {
  text-align: center;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Enhanced Prose Content Styling */
.prose-content {
  @apply text-gray-800 dark:text-gray-200 leading-relaxed;
  font-size: 1.125rem;
  line-height: 1.75;
}

.prose-content h1,
.prose-content h2,
.prose-content h3,
.prose-content h4,
.prose-content h5,
.prose-content h6 {
  @apply font-bold text-gray-900 dark:text-gray-100;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  line-height: 1.25;
  scroll-margin-top: 100px; /* Account for fixed header */
}

.prose-content h1 { 
  font-size: 2.5rem;
  background: linear-gradient(135deg, #1f2937 0%, #4b5563 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 2rem;
  font-weight: 800;
}

.prose-content h2 { 
  font-size: 2rem;
  color: #374151;
  border-bottom: 3px solid transparent;
  background: linear-gradient(to right, #e5e7eb, #e5e7eb) bottom / 100% 3px no-repeat,
              linear-gradient(135deg, #3b82f6, #8b5cf6) bottom / 60% 3px no-repeat;
  padding-bottom: 0.75rem;
  font-weight: 700;
}

.prose-content h3 { 
  font-size: 1.5rem;
  color: #4b5563;
  position: relative;
  padding-left: 1.25rem;
  font-weight: 600;
}

.prose-content h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.375rem;
  width: 4px;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.prose-content h4 {
  font-size: 1.25rem;
  color: #6b7280;
  font-weight: 600;
}

.prose-content h5 {
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 600;
}

.prose-content h6 {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dark mode headings */
.dark .prose-content h1 {
  background: linear-gradient(135deg, #f9fafb 0%, #d1d5db 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .prose-content h2 {
  color: #e5e7eb;
  background: linear-gradient(to right, #374151, #374151) bottom / 100% 3px no-repeat,
              linear-gradient(135deg, #60a5fa, #a78bfa) bottom / 60% 3px no-repeat;
}

.dark .prose-content h3 {
  color: #d1d5db;
}

.dark .prose-content h4,
.dark .prose-content h5,
.dark .prose-content h6 {
  color: #9ca3af;
}

/* Paragraphs */
.prose-content p {
  margin: 1.5rem 0;
  color: #374151;
}

.dark .prose-content p {
  color: #d1d5db;
}

/* Lists */
.prose-content ul,
.prose-content ol {
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

.prose-content li {
  margin-bottom: 0.5rem;
  position: relative;
}

/* Enhanced Blockquotes */
.prose-content blockquote {
  position: relative;
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: none;
  border-radius: 12px;
  font-style: italic;
  font-size: 1.125rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.prose-content blockquote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  font-family: serif;
  color: #3b82f6;
  opacity: 0.3;
  line-height: 1;
}

.prose-content blockquote p {
  margin: 0;
  color: #1e40af;
  font-weight: 500;
}

.dark .prose-content blockquote {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

.dark .prose-content blockquote::before {
  color: #60a5fa;
}

.dark .prose-content blockquote p {
  color: #bfdbfe;
}

/* Enhanced Code Styling */
.prose-content code {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 600;
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.prose-content code::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
  border-radius: 8px;
  z-index: -1;
}

.dark .prose-content code {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #fca5a5;
}

/* Enhanced Pre/Code Blocks */
.prose-content pre {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 12px;
  overflow-x: auto;
  margin: 2rem 0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.prose-content pre::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
  background-size: 400% 400%;
  animation: gradient-x 3s ease infinite;
  border-radius: 14px;
  z-index: -1;
}

.prose-content pre::after {
  content: '● ● ●';
  position: absolute;
  top: 12px;
  left: 16px;
  color: #64748b;
  font-size: 12px;
  letter-spacing: 4px;
  z-index: 1;
}



.prose-content pre code {
  background: none;
  color: inherit;
  padding: 0;
  border: none;
  font-size: inherit;
  margin-top: 40px;
  display: block;
}

.dark .prose-content pre {
  background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
  border-color: #374151;
}

.dark .prose-content pre::before {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-bottom-color: #4b5563;
}

.dark .prose-content pre code {
  color: #f3f4f6;
}

/* Enhanced Tables */
.prose-content table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 2rem 0;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.prose-content table td,
.prose-content table th {
  border: 1px solid #e5e7eb;
  padding: 1rem;
  text-align: left;
}

.prose-content table th {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .prose-content table {
  background: #1f2937;
}

.dark .prose-content table td,
.dark .prose-content table th {
  border-color: #374151;
}

.dark .prose-content table th {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #e5e7eb;
}

/* Enhanced Images */
.prose-content img {
  height: auto;
  max-width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin: 2rem 0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.prose-content img:hover {
  transform: scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced Links */
.prose-content a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.prose-content a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.prose-content a:hover::after {
  width: 100%;
}

.prose-content a:hover {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .prose-content a {
  color: #60a5fa;
}

.dark .prose-content a:hover {
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced Strong and Emphasis */
.prose-content strong {
  font-weight: 700;
  background: linear-gradient(135deg, #1f2937, #4b5563);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.prose-content em {
  font-style: italic;
  color: #6b7280;
}

.dark .prose-content strong {
  background: linear-gradient(135deg, #f9fafb, #d1d5db);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .prose-content em {
  color: #9ca3af;
}

/* Video Embeds */
.prose-content iframe {
  width: 100%;
  border-radius: 12px;
  margin: 2rem 0;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Callout Boxes */
.prose-content .callout {
  padding: 1.5rem;
  margin: 2rem 0;
  border-radius: 12px;
  border-left: 4px solid;
  font-weight: 500;
}

.prose-content .callout-info {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-left-color: #3b82f6;
  color: #1e40af;
}

.prose-content .callout-warning {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-left-color: #f59e0b;
  color: #92400e;
}

.prose-content .callout-success {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-left-color: #10b981;
  color: #065f46;
}

.prose-content .callout-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  border-left-color: #ef4444;
  color: #991b1b;
}

.dark .prose-content .callout-info {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: #bfdbfe;
}

.dark .prose-content .callout-warning {
  background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
  color: #fde68a;
}

.dark .prose-content .callout-success {
  background: linear-gradient(135deg, #065f46 0%, #047857 100%);
  color: #a7f3d0;
}

.dark .prose-content .callout-error {
  background: linear-gradient(135deg, #991b1b 0%, #b91c1c 100%);
  color: #fca5a5;
}

/* Custom Scrollbar for TOC */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: #374151;
}

/* Enhanced Copy Code Button */
.copy-code-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(71, 85, 105, 0.8);
  color: #e2e8f0;
  border: 1px solid #475569;
  border-radius: 6px;
  padding: 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.prose-content pre:hover .copy-code-btn {
  opacity: 1;
}

.copy-code-btn:hover {
  background: rgba(59, 130, 246, 0.8);
  border-color: #3b82f6;
  transform: scale(1.05);
}

.dark .copy-code-btn:hover {
  background: rgba(96, 165, 250, 0.8);
  border-color: #60a5fa;
}

/* Line clamp utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Border utilities */
.border-l-3 {
  border-left-width: 3px;
}

/* Colorful code line highlighting */
.highlight-line-gradient {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2));
  position: relative;
  padding-left: 1rem;
  margin-left: -0.5rem;
}

.highlight-line-gradient::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #a855f7, #ec4899);
}

/* Override react-medium-image-zoom overlay background for better image preview */
.rmiz__overlay,
.medium-zoom-overlay {
  background: rgba(15, 23, 42, 0.85) !important;
  backdrop-filter: blur(2px);
}

/* Fix for react-medium-image-zoom overlay using data attribute */
div[data-rmiz-modal-overlay] {
  background: rgba(15, 23, 42, 0.85) !important;
  backdrop-filter: blur(2px) !important;
}

.shimmer-border {
  position: relative;
  z-index: 0;
}
.shimmer-border::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  border-radius: 1.25rem;
  background: linear-gradient(120deg, #f472b6, #a78bfa, #38bdf8, #34d399, #fbbf24, #f472b6 90%);
  background-size: 300% 300%;
  animation: shimmerMove 4s linear infinite;
  opacity: 0.7;
}

@keyframes shimmerMove {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

.copy-float-btn {
  box-shadow: 0 4px 24px 0 rgba(59,130,246,0.10), 0 1.5px 6px 0 rgba(168,85,247,0.10);
  transition: box-shadow 0.2s, transform 0.2s;
}

@keyframes pingOnce {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.3); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-ping-once {
  animation: pingOnce 0.7s cubic-bezier(0.4,0,0.2,1) 1;
}

/* Consistent spacing utilities */
@layer components {
  .page-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  .hero-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }
}
