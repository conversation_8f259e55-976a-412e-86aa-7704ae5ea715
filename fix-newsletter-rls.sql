-- =======================================================
-- QUICK FIX FOR NEWSLETTER SUBSCRIBERS RLS POLICIES
-- =======================================================
-- Run this in your Supabase SQL editor to fix the RLS policy error

-- Remove all existing policies
DROP POLICY IF EXISTS "newsletter_subscribers_insert_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_confirm_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_unsubscribe_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_admin_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_select_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_update_confirm_policy" ON public.newsletter_subscribers;

-- Create new policies that allow public access
CREATE POLICY "newsletter_subscribers_insert_policy" ON public.newsletter_subscribers
    FOR INSERT TO anon, authenticated
    WITH CHECK (true);

CREATE POLICY "newsletter_subscribers_update_confirm_policy" ON public.newsletter_subscribers
    FOR UPDATE TO anon, authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "newsletter_subscribers_select_policy" ON public.newsletter_subscribers
    FOR SELECT TO anon, authenticated
    USING (true);

CREATE POLICY "newsletter_subscribers_admin_policy" ON public.newsletter_subscribers
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Verify policies are applied
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'newsletter_subscribers';
