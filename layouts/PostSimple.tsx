import { ReactNode } from 'react'
import { formatDate } from 'pliny/utils/formatDate'
import { CoreContent } from 'pliny/utils/contentlayer'
import type { Blog } from 'contentlayer/generated'
import Comments from '@/components/Comments'
import Link from '@/components/Link'
import PageTitle from '@/components/PageTitle'
import SectionContainer from '@/components/SectionContainer'
import siteMetadata from '@/data/siteMetadata'
import ScrollTopAndComment from '@/components/ScrollTopAndComment'

import PostNavigationFloat from '@/components/PostNavigationFloat'
import { Calendar, ArrowLeft } from 'lucide-react'

interface LayoutProps {
  content: CoreContent<Blog>
  children: ReactNode
  next?: { path: string; title: string }
  prev?: { path: string; title: string }
}

export default function PostLayout({ content, next, prev, children }: LayoutProps) {
  const { slug, date, title } = content

  return (
    <SectionContainer>

      <PostNavigationFloat prev={prev} next={next} postTitle={title} />
      <ScrollTopAndComment />
      
      <article className="relative max-w-4xl mx-auto">
        {/* Simple Header */}
        <header className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-900 dark:via-gray-800/50 dark:to-primary-900/20 border border-white/20 dark:border-gray-700/50 shadow-lg mb-12">
          <div className="absolute inset-0 opacity-20">
            <svg
              width="60"
              height="60"
              viewBox="0 0 60 60"
              xmlns="http://www.w3.org/2000/svg"
              className="absolute inset-0 h-full w-full"
            >
              <defs>
                <pattern
                  id="post-simple-pattern"
                  x="0"
                  y="0"
                  width="60"
                  height="60"
                  patternUnits="userSpaceOnUse"
                >
                  <circle cx="30" cy="30" r="1.5" fill="#9C92AC" fillOpacity="0.08" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#post-simple-pattern)" />
            </svg>
          </div>
          
          <div className="relative px-8 py-16 text-center">
            <div className="mx-auto max-w-3xl">
              {/* Date Badge */}
              <div className="mb-6 flex justify-center">
                <div className="inline-flex items-center rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-6 py-2 shadow-md border border-gray-200/50 dark:border-gray-700/50">
                  <Calendar className="mr-2 h-4 w-4 text-primary-600 dark:text-primary-400" />
                  <time 
                    dateTime={date}
                    className="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    {formatDate(date, siteMetadata.locale)}
                  </time>
                </div>
              </div>

              {/* Title */}
              <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 dark:text-gray-100 sm:text-5xl md:text-6xl leading-tight">
                <span className="block bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent">
                  {title}
                </span>
              </h1>
              </div>
            </div>
          </header>

        {/* Content Section */}
        <main className="rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg border border-gray-200/50 dark:border-gray-700/50 overflow-hidden mb-12">
          <div className="prose prose-lg dark:prose-invert max-w-none p-8 lg:p-12 prose-content">
            {children}
          </div>
        </main>

        {/* Comments */}
        {siteMetadata.comments && (
          <div className="rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-8 mb-12" id="comment">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Comments
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Share your thoughts and join the discussion
              </p>
            </div>
                <Comments slug={slug} />
              </div>
            )}

        {/* Navigation Footer */}
        <footer className="space-y-6">
          {/* Previous/Next Navigation */}
          {(prev || next) && (
            <div className="grid gap-4 sm:grid-cols-2">
                {prev && prev.path && (
                    <Link
                      href={`/${prev.path}`}
                  className="group block rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl transition-all duration-300"
                      aria-label={`Previous post: ${prev.title}`}
                    >
                  <div className="flex items-start gap-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 group-hover:from-primary-500 group-hover:to-primary-600 transition-all duration-300">
                      <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover:text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
                        Previous Article
                      </div>
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 line-clamp-2 transition-colors">
                        {prev.title}
                      </div>
                    </div>
                  </div>
                </Link>
                )}
                {next && next.path && (
                    <Link
                      href={`/${next.path}`}
                  className="group block rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 shadow-lg border border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl transition-all duration-300"
                      aria-label={`Next post: ${next.title}`}
                    >
                  <div className="flex items-start gap-4">
                    <div className="flex-1 min-w-0 text-right">
                      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-1">
                        Next Article
                      </div>
                      <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 line-clamp-2 transition-colors">
                        {next.title}
                      </div>
                    </div>
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700 group-hover:from-primary-500 group-hover:to-primary-600 transition-all duration-300">
                      <ArrowLeft className="h-5 w-5 text-gray-600 dark:text-gray-300 group-hover:text-white transform rotate-180" />
                    </div>
                  </div>
                </Link>
                )}
              </div>
          )}

          {/* Back to Blog */}
          <div className="text-center">
            <Link
              href="/blog"
              className="inline-flex items-center rounded-full bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-3 font-medium text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              aria-label="Back to the blog"
            >
              ← Back to the blog
            </Link>
          </div>
        </footer>
      </article>
    </SectionContainer>
  )
}
