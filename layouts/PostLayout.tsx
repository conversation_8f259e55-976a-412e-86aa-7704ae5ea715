import { ReactNode, useEffect } from 'react'
import { CoreContent } from 'pliny/utils/contentlayer'
import type { Blog, Authors } from 'contentlayer/generated'
import Comments from '@/components/Comments'
import PostInteraction from '@/components/PostInteraction'
import Link from '@/components/Link'
import Tag from '@/components/Tag'
import siteMetadata from '@/data/siteMetadata'
import ScrollTopAndComment from '@/components/ScrollTopAndComment'
import PostTableOfContents from '@/components/PostTableOfContents'
import PostNavigationFloat from '@/components/PostNavigationFloat'
import { Calendar, Clock, ArrowLeft, ArrowRight } from 'lucide-react'

const editUrl = (path) => `${siteMetadata.siteRepo}/blob/main/data/${path}`
const discussUrl = (path) =>
  `https://mobile.twitter.com/search?q=${encodeURIComponent(`${siteMetadata.siteUrl}/${path}`)}`

const postDateTemplate: Intl.DateTimeFormatOptions = {
  weekday: 'long',
  year: 'numeric',
  month: 'long',
  day: 'numeric',
}

interface LayoutProps {
  content: CoreContent<Blog>
  authorDetails: CoreContent<Authors>[]
  next?: { path: string; title: string }
  prev?: { path: string; title: string }
  children: ReactNode
}

export default function PostLayout({ content, authorDetails, next, prev, children }: LayoutProps) {
  const { slug, date, title, tags, readingTime } = content

  // Reading progress functionality
  useEffect(() => {
    const updateReadingProgress = () => {
      const article = document.querySelector('.reading-content')
      const progressBar = document.querySelector('.reading-progress') as HTMLElement
      
      if (!article || !progressBar) return
      
      const articleRect = article.getBoundingClientRect()
      const articleHeight = article.scrollHeight
      const windowHeight = window.innerHeight
      const scrollTop = window.scrollY
      
      const articleTop = articleRect.top + scrollTop
      const readProgress = Math.max(0, Math.min(100, 
        ((scrollTop + windowHeight - articleTop) / (articleHeight + windowHeight)) * 100
      ))
      
      progressBar.style.width = `${readProgress}%`
    }

    window.addEventListener('scroll', updateReadingProgress)
    window.addEventListener('resize', updateReadingProgress)
    updateReadingProgress()

    return () => {
      window.removeEventListener('scroll', updateReadingProgress)
      window.removeEventListener('resize', updateReadingProgress)
    }
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-slate-900">
      <PostTableOfContents />
      <PostNavigationFloat prev={prev} next={next} postTitle={title} />
      <ScrollTopAndComment />
      
      {/* Reading Progress */}
      <div className="reading-progress-container fixed top-0 left-0 right-0 z-50 h-1 bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600">
        <div className="reading-progress h-full bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500 transition-all duration-500 ease-out shadow-sm"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-12">
        <article className="relative max-w-4xl mx-auto">
          {/* Hero Header */}
          <header className="mb-16 text-center">
            <div className="relative">
              {/* Background decoration */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-emerald-600/5 rounded-3xl transform -rotate-1"></div>
              <div className="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-2xl border border-white/20 dark:border-gray-700/30">
                
                {/* Meta info */}
                <div className="flex items-center justify-center gap-6 mb-8 text-sm">
                  <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-full">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <time dateTime={date} className="font-medium text-gray-700 dark:text-gray-300">
                      {new Date(date).toLocaleDateString(siteMetadata.locale, postDateTemplate)}
                    </time>
                  </div>
                  {readingTime && typeof readingTime === 'object' && 'minutes' in readingTime && (
                    <div className="flex items-center gap-2 px-4 py-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-full">
                      <Clock className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                      <span className="font-medium text-emerald-700 dark:text-emerald-300">
                        {Math.ceil((readingTime as any).minutes)} min read
                      </span>
                    </div>
                  )}
                </div>

                {/* Title */}
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent leading-tight mb-8">
                  {title}
                </h1>

                {/* Tags */}
                {tags && tags.length > 0 && (
                  <div className="flex flex-wrap justify-center gap-3">
                    {tags.map((tag) => (
                      <Tag key={tag} text={tag} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="mb-20">
            <div className="relative">
              {/* Content container with elegant styling */}
              <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 dark:border-gray-700/50 overflow-hidden">
                
                {/* Content body */}
                <div className="reading-content p-8 lg:p-16">
                  <div className="prose prose-xl dark:prose-invert max-w-none prose-headings:font-bold prose-headings:tracking-tight prose-h1:text-4xl prose-h2:text-3xl prose-h3:text-2xl prose-h4:text-xl prose-p:text-lg prose-p:leading-relaxed prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-li:text-lg prose-li:leading-relaxed prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:bg-blue-50 dark:prose-blockquote:bg-blue-900/20 prose-blockquote:p-6 prose-blockquote:rounded-r-lg prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-emerald-600 dark:prose-code:text-emerald-400 prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 dark:prose-strong:text-gray-100">
                    
                    {/* Reading comfort styling */}
                    <div className="[&>*:first-child]:mt-0 [&>*:last-child]:mb-0 [&>h2]:mt-16 [&>h2]:mb-8 [&>h3]:mt-12 [&>h3]:mb-6 [&>h4]:mt-10 [&>h4]:mb-4 [&>p]:mb-6 [&>ul]:mb-6 [&>ol]:mb-6 [&>blockquote]:my-8 [&>pre]:my-8 [&>hr]:my-12">
                      {children}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </main>

          {/* Post Interaction */}
          <section className="mb-16">
            <div className="bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-600/50">
              <PostInteraction postSlug={slug} />
            </div>
          </section>

          {/* Comments Section */}
          {siteMetadata.comments && (
            <section className="mb-16" id="comment">
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8 text-center">
                  Join the Discussion
                </h2>
                <Comments slug={slug} />
              </div>
            </section>
          )}

          {/* Post Navigation */}
          {(prev || next) && (
            <nav className="mb-16">
              <div className="grid gap-6 md:grid-cols-2">
                {prev && prev.path && (
                  <Link
                    href={`/${prev.path}`}
                    className="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-200/50 dark:border-blue-700/50 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] hover:border-blue-300 dark:hover:border-blue-600"
                  >
                    <div className="absolute top-4 left-4 w-12 h-12 bg-blue-500/10 dark:bg-blue-400/10 rounded-full flex items-center justify-center group-hover:bg-blue-500/20 dark:group-hover:bg-blue-400/20 transition-colors">
                      <ArrowLeft className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="ml-16">
                      <div className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider mb-2">
                        Previous Article
                      </div>
                      <div className="text-lg font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-700 dark:group-hover:text-blue-300 line-clamp-2 transition-colors">
                        {prev.title}
                      </div>
                    </div>
                  </Link>
                )}
                {next && next.path && (
                  <Link
                    href={`/${next.path}`}
                    className="group relative overflow-hidden bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-2xl p-6 border border-emerald-200/50 dark:border-emerald-700/50 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] hover:border-emerald-300 dark:hover:border-emerald-600"
                  >
                    <div className="absolute top-4 right-4 w-12 h-12 bg-emerald-500/10 dark:bg-emerald-400/10 rounded-full flex items-center justify-center group-hover:bg-emerald-500/20 dark:group-hover:bg-emerald-400/20 transition-colors">
                      <ArrowRight className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <div className="mr-16 text-right">
                      <div className="text-xs font-semibold text-emerald-600 dark:text-emerald-400 uppercase tracking-wider mb-2">
                        Next Article
                      </div>
                      <div className="text-lg font-bold text-gray-900 dark:text-gray-100 group-hover:text-emerald-700 dark:group-hover:text-emerald-300 line-clamp-2 transition-colors">
                        {next.title}
                      </div>
                    </div>
                  </Link>
                )}
              </div>
            </nav>
          )}

          {/* Back to Blog CTA */}
          <div className="text-center">
            <Link
              href="/blog"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl font-semibold text-lg transition-all duration-300 shadow-2xl hover:shadow-purple-500/25 hover:scale-105 transform"
            >
              <ArrowLeft className="h-5 w-5" />
              Back to All Articles
            </Link>
          </div>
        </article>
      </div>
    </div>
  )
}
