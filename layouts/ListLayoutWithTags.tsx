'use client'

import { usePathname } from 'next/navigation'
import { useState, useMemo } from 'react'
import { slug } from 'github-slugger'
import { formatDate } from 'pliny/utils/formatDate'
import { CoreContent } from 'pliny/utils/contentlayer'
import type { Blog } from 'contentlayer/generated'
import Link from '@/components/Link'
import Tag from '@/components/Tag'
import siteMetadata from '@/data/siteMetadata'
import { 
  Calendar, 
  Clock, 
  ArrowRight, 
  BookOpen, 
  Search, 
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface PaginationProps {
  totalPages: number
  currentPage: number
}

interface TagInfo {
  name: string
  slug: string
  count: number
}

interface ListLayoutProps {
  posts: any[]
  title: string
  initialDisplayPosts?: any[]
  pagination?: PaginationProps
  tagInfo?: TagInfo
}

function Pagination({ totalPages, currentPage }: PaginationProps) {
  const pathname = usePathname()
  const basePath = pathname?.split('/')[1] || 'blog'
  
  const prevPage = currentPage - 1 > 0
  const nextPage = currentPage + 1 <= totalPages

  return (
    <div className="flex items-center justify-center gap-4">
      {/* Previous Button */}
      {prevPage ? (
        <Link
          href={currentPage - 1 === 1 ? `/${basePath}/` : `/${basePath}/page/${currentPage - 1}`}
          rel="prev"
          className="flex items-center gap-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors font-medium"
        >
          <ChevronLeft className="w-4 h-4" />
          <span>Previous</span>
        </Link>
      ) : (
        <div className="flex items-center gap-2 px-6 py-3 text-gray-400 dark:text-gray-600 font-medium">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous</span>
        </div>
      )}

      {/* Page Numbers */}
      <div className="flex items-center gap-2">
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          let pageNum: number
          if (totalPages <= 5) {
            pageNum = i + 1
          } else if (currentPage <= 3) {
            pageNum = i + 1
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i
          } else {
            pageNum = currentPage - 2 + i
          }

          const isActive = pageNum === currentPage

          return (
            <Link
              key={pageNum}
              href={pageNum === 1 ? `/${basePath}/` : `/${basePath}/page/${pageNum}`}
              className={`w-12 h-12 flex items-center justify-center rounded-lg font-bold transition-colors ${
                isActive
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
              }`}
            >
              {pageNum}
            </Link>
          )
        })}
      </div>

      {/* Next Button */}
      {nextPage ? (
        <Link
          href={`/${basePath}/page/${currentPage + 1}`}
          rel="next"
          className="flex items-center gap-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors font-medium"
        >
          <span>Next</span>
          <ChevronRight className="w-4 h-4" />
        </Link>
      ) : (
        <div className="flex items-center gap-2 px-6 py-3 text-gray-400 dark:text-gray-600 font-medium">
          <span>Next</span>
          <ChevronRight className="w-4 h-4" />
        </div>
      )}
    </div>
  )
}

export default function ListLayoutWithTags({
  posts,
  title,
  initialDisplayPosts = [],
  pagination,
  tagInfo,
}: ListLayoutProps) {
  const pathname = usePathname()
  const [searchValue, setSearchValue] = useState('')
  
  // Generate tags from posts
  const tagCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    posts.forEach((post) => {
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach((tag: string) => {
          counts[tag] = (counts[tag] || 0) + 1
        })
      }
    })
    return counts
  }, [posts])
  
  const tagKeys = Object.keys(tagCounts)
  const sortedTags = tagKeys.sort((a, b) => tagCounts[b] - tagCounts[a])

  const displayPosts = initialDisplayPosts.length > 0 ? initialDisplayPosts : posts

  const filteredPosts = useMemo(() => {
    if (!searchValue) return displayPosts
    return displayPosts.filter((post) =>
      post.title.toLowerCase().includes(searchValue.toLowerCase()) ||
      post.summary?.toLowerCase().includes(searchValue.toLowerCase()) ||
      post.tags?.some((tag: string) => tag.toLowerCase().includes(searchValue.toLowerCase()))
    )
  }, [displayPosts, searchValue])

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Wired-style Hero Section */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 shadow-sm">
        <div className="max-w-6xl mx-auto px-4 py-12">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-display font-black text-gray-900 dark:text-white mb-6 tracking-tight">
              {pathname?.startsWith('/tags/') ? tagInfo?.name?.toUpperCase() || 'TAGGED POSTS' : 'ALL POSTS'}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
              {pathname?.startsWith('/tags/')
                ? `${tagCounts[tagInfo?.name || ''] || 0} posts about ${tagInfo?.name || 'this topic'}`
                : 'Complete archive of technology tips, tricks, and insights'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg mx-4 mb-8">
        <div className="max-w-6xl mx-auto px-6 pt-8">
          {/* Search Bar */}
          <div className="mb-8">
            <div className="max-w-md mx-auto">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search posts..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <Search className="absolute left-3 top-3.5 w-4 h-4 text-gray-400" />
              </div>
            </div>
          </div>

          {/* Popular Tags */}
          <div className="mb-8">
            <h3 className="text-lg font-display font-bold text-gray-900 dark:text-white mb-4 text-center">
              Popular Topics
            </h3>
            <div className="flex flex-wrap justify-center gap-2 max-w-4xl mx-auto">
              {sortedTags.slice(0, 12).map((tag) => (
                <Link
                  key={tag}
                  href={`/tags/${slug(tag)}`}
                  className="inline-flex items-center px-3 py-1 text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 uppercase tracking-wider transition-colors duration-200"
                >
                  {tag}
                </Link>
              ))}
            </div>
          </div>
          {/* Posts Grid */}
          {filteredPosts.length === 0 ? (
            <div className="text-center py-20">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {searchValue ? 'No articles found' : 'No articles found'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                {searchValue
                  ? 'Try adjusting your search terms or browse all articles.'
                  : 'Check back soon for new content!'
                }
              </p>
              <Link
                href="/blog"
                className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                Browse All Articles
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 pb-12">
              {filteredPosts.map((post) => {
                const { slug: postSlug, date, title: postTitle, summary, tags } = post
                return (
                  <article key={postSlug} className="group">
                    <div className="bg-gray-200 dark:bg-gray-700 aspect-[16/10] rounded-lg mb-4 flex items-center justify-center border border-gray-300 dark:border-gray-600">
                      <span className="text-gray-500 dark:text-gray-400 text-sm">Image</span>
                    </div>

                    <div className="mb-2">
                      {tags && tags.length > 0 && (
                        <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider">
                          {tags[0]}
                        </span>
                      )}
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                      <Link href={`/blog/${postSlug}`}>
                        {postTitle}
                      </Link>
                    </h3>

                    {summary && (
                      <p className="text-gray-600 dark:text-gray-300 mb-3 line-clamp-3 text-sm leading-relaxed">
                        {summary}
                      </p>
                    )}

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      <span className="font-medium">{siteMetadata.author}</span>
                      <span className="mx-2">•</span>
                      <time>{formatDate(date, siteMetadata.locale)}</time>
                    </div>
                  </article>
                )
              })}
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && !searchValue && (
            <div className="mt-12">
              <Pagination currentPage={pagination.currentPage} totalPages={pagination.totalPages} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
