'use client'

import { usePathname } from 'next/navigation'
import { useState, useMemo } from 'react'
import { slug } from 'github-slugger'
import { formatDate } from 'pliny/utils/formatDate'
import { CoreContent } from 'pliny/utils/contentlayer'
import type { Blog } from 'contentlayer/generated'
import Link from '@/components/Link'
import Tag from '@/components/Tag'
import siteMetadata from '@/data/siteMetadata'
import { 
  Calendar, 
  Clock, 
  ArrowRight, 
  BookOpen, 
  Search, 
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface PaginationProps {
  totalPages: number
  currentPage: number
}

interface TagInfo {
  name: string
  slug: string
  count: number
}

interface ListLayoutProps {
  posts: any[]
  title: string
  initialDisplayPosts?: any[]
  pagination?: PaginationProps
  tagInfo?: TagInfo
}

function Pagination({ totalPages, currentPage }: PaginationProps) {
  const pathname = usePathname()
  const basePath = pathname?.split('/')[1] || 'blog'
  
  const prevPage = currentPage - 1 > 0
  const nextPage = currentPage + 1 <= totalPages

  return (
    <div className="flex items-center justify-center gap-4 mt-12">
      {/* Previous Button */}
      {prevPage ? (
        <Link 
          href={currentPage - 1 === 1 ? `/${basePath}/` : `/${basePath}/page/${currentPage - 1}`} 
          rel="prev"
          className="flex items-center gap-2 px-6 py-3 text-slate-700 dark:text-slate-300 hover:text-white hover:bg-indigo-600 font-semibold rounded-lg transition-colors group"
        >
          <ChevronLeft className="w-5 h-5 transition-transform group-hover:-translate-x-1" />
          <span>Previous</span>
        </Link>
      ) : (
        <div className="flex items-center gap-2 px-6 py-3 text-slate-400 dark:text-slate-600 font-semibold">
          <ChevronLeft className="w-5 h-5" />
          <span>Previous</span>
        </div>
      )}

      {/* Page Numbers */}
      <div className="flex items-center gap-2">
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          let pageNum: number
          if (totalPages <= 5) {
            pageNum = i + 1
          } else if (currentPage <= 3) {
            pageNum = i + 1
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i
          } else {
            pageNum = currentPage - 2 + i
          }

          const isActive = pageNum === currentPage
          
          return (
            <Link
              key={pageNum}
              href={pageNum === 1 ? `/${basePath}/` : `/${basePath}/page/${pageNum}`}
              className={`w-12 h-12 flex items-center justify-center rounded-lg font-bold transition-colors ${
                isActive
                  ? 'bg-indigo-600 text-white'
                  : 'text-slate-600 dark:text-slate-400 hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20'
              }`}
            >
              {pageNum}
            </Link>
          )
        })}
      </div>

      {/* Next Button */}
      {nextPage ? (
        <Link 
          href={`/${basePath}/page/${currentPage + 1}`} 
          rel="next"
          className="flex items-center gap-2 px-6 py-3 text-slate-700 dark:text-slate-300 hover:text-white hover:bg-indigo-600 font-semibold rounded-lg transition-colors group"
        >
          <span>Next</span>
          <ChevronRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
        </Link>
      ) : (
        <div className="flex items-center gap-2 px-6 py-3 text-slate-400 dark:text-slate-600 font-semibold">
          <span>Next</span>
          <ChevronRight className="w-5 h-5" />
        </div>
      )}
    </div>
  )
}

export default function ListLayoutWithTags({
  posts,
  title,
  initialDisplayPosts = [],
  pagination,
  tagInfo,
}: ListLayoutProps) {
  const pathname = usePathname()
  const [searchValue, setSearchValue] = useState('')
  
  // Generate tags from posts
  const tagCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    posts.forEach((post) => {
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach((tag: string) => {
          counts[tag] = (counts[tag] || 0) + 1
        })
      }
    })
    return counts
  }, [posts])
  
  const tagKeys = Object.keys(tagCounts)
  const sortedTags = tagKeys.sort((a, b) => tagCounts[b] - tagCounts[a])

  const displayPosts = initialDisplayPosts.length > 0 ? initialDisplayPosts : posts

  const filteredPosts = useMemo(() => {
    if (!searchValue) return displayPosts
    return displayPosts.filter((post) =>
      post.title.toLowerCase().includes(searchValue.toLowerCase()) ||
      post.summary?.toLowerCase().includes(searchValue.toLowerCase()) ||
      post.tags?.some((tag: string) => tag.toLowerCase().includes(searchValue.toLowerCase()))
    )
  }, [displayPosts, searchValue])

  return (
    <>
      {/* Hero Section - Only show on main blog page */}
      {pathname === '/blog' && (
        <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center space-y-4">
              <h1 className="text-4xl sm:text-5xl font-bold text-slate-900 dark:text-white">
                Latest Articles
              </h1>
              <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
                Technology insights, tutorials, and reviews to help you stay ahead in the digital world.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Clean Header for tag and category pages */}
      {(pathname?.startsWith('/tags/') || pathname?.startsWith('/categories/')) && (
        <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center space-y-4">
              <h1 className="text-4xl sm:text-5xl font-bold text-slate-900 dark:text-white">
                {title}
              </h1>
              <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
                {tagInfo ? `${tagInfo.count} articles in this ${pathname?.startsWith('/categories/') ? 'category' : 'topic'}` : 'Articles in this category'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Blog Listing */}
      <div className="bg-slate-50 dark:bg-slate-900 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {/* Search */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Search className="w-5 h-5 text-slate-400" />
                  <h3 className="font-semibold text-slate-900 dark:text-white">Search</h3>
                </div>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search posts..."
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-900 text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-2.5 w-4 h-4 text-slate-400" />
                </div>
              </div>

              {/* Tags */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6">
                <h3 className="font-semibold text-slate-900 dark:text-white mb-4">Popular Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {sortedTags.slice(0, 10).map((tag) => (
                    <Tag key={tag} text={tag} />
                  ))}
                </div>
                <div className="flex items-center justify-between mt-4">
                  <Link
                    href="/tags"
                    className="inline-flex items-center gap-1 text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300"
                  >
                    View all tags
                    <ArrowRight className="w-3 h-3" />
                  </Link>
                  <Link
                    href="/categories"
                    className="inline-flex items-center gap-1 text-sm text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300"
                  >
                    Browse categories
                    <ArrowRight className="w-3 h-3" />
                  </Link>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {filteredPosts.length === 0 ? (
                <div className="text-center py-20">
                  <div className="bg-white dark:bg-slate-800 rounded-xl p-12 shadow-sm border border-slate-200 dark:border-slate-700">
                    <BookOpen className="w-16 h-16 text-slate-400 mx-auto mb-6" />
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                      {searchValue ? 'No articles found' : tagInfo ? `No articles found for "${tagInfo.name}"` : 'No articles found'}
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400 mb-8">
                      {searchValue 
                        ? 'Try adjusting your search terms or browse all articles.'
                        : 'Check back soon for new content!'
                      }
                    </p>
                    <Link
                      href="/blog"
                      className="inline-flex items-center gap-2 px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors"
                    >
                      Browse All Articles
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="space-y-8">
                  {/* Posts */}
                  {filteredPosts.map((post) => {
                    const { slug: postSlug, date, title: postTitle, summary, tags } = post
                    return (
                      <article key={postSlug} className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-6 hover:shadow-md transition-shadow">
                        <div className="space-y-4">
                          {/* Date */}
                          <div className="flex items-center gap-2 text-sm text-slate-500 dark:text-slate-400">
                            <Calendar className="w-4 h-4" />
                            <time dateTime={date}>{formatDate(date, siteMetadata.locale)}</time>
                          </div>

                          {/* Title */}
                          <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                            <Link
                              href={`/blog/${postSlug}`}
                              className="hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
                            >
                              {postTitle}
                            </Link>
                          </h2>

                          {/* Summary */}
                          {summary && (
                            <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                              {summary}
                            </p>
                          )}

                          {/* Tags */}
                          {tags && tags.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                              {tags.map((tag: string) => (
                                <Tag key={tag} text={tag} />
                              ))}
                            </div>
                          )}

                          {/* Read More */}
                          <Link
                            href={`/blog/${postSlug}`}
                            className="inline-flex items-center gap-2 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium"
                          >
                            Read more
                            <ArrowRight className="w-4 h-4" />
                          </Link>
                        </div>
                      </article>
                    )
                  })}

                  {/* Pagination */}
                  {pagination && pagination.totalPages > 1 && !searchValue && (
                    <Pagination currentPage={pagination.currentPage} totalPages={pagination.totalPages} />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
