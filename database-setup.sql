-- =======================================================
-- PERSONAL BLOG DATABASE SETUP SCRIPT
-- =======================================================
-- Complete database setup for the personal blog including:
-- - Blog posts table
-- - Projects table  
-- - Comments and likes system
-- - Migrations tracking table
-- Updated to match current live server status
-- =======================================================

-- =======================================================
-- 1. BLOG POSTS TABLE
-- =======================================================

CREATE TABLE IF NOT EXISTS public.blog_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    slug TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    date DATE NOT NULL,
    author TEXT NOT NULL,
    description TEXT NOT NULL,
    tags TEXT NOT NULL,
    read_time TEXT NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    status TEXT DEFAULT 'published' CHECK (status IN ('published', 'draft', 'disabled')),
    featured BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    show_affiliate_disclosure BOOLEAN DEFAULT false
);

-- =======================================================
-- 2. PROJECTS TABLE
-- =======================================================

CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    href TEXT,
    img_src TEXT,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'draft')),
    featured BOOLEAN NOT NULL DEFAULT false,
    technologies JSONB DEFAULT '[]'::jsonb,
    order_index INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- =======================================================
-- 3. COMMENTS TABLE
-- =======================================================

CREATE TABLE IF NOT EXISTS public.comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_slug TEXT NOT NULL,
    author_name TEXT NOT NULL,
    author_email TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- =======================================================
-- 4. LIKES TABLE
-- =======================================================

CREATE TABLE IF NOT EXISTS public.likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_slug TEXT NOT NULL,
    user_ip TEXT NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- =======================================================
-- 5. MIGRATIONS TABLE
-- =======================================================

CREATE TABLE IF NOT EXISTS public.migrations (
    id VARCHAR PRIMARY KEY,
    name VARCHAR NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- =======================================================
-- 6. INDEXES FOR PERFORMANCE
-- =======================================================

-- Add comment for affiliate disclosure column
COMMENT ON COLUMN public.blog_posts.show_affiliate_disclosure 
IS 'Flag to control whether affiliate disclosure is shown at the end of the post';

-- Blog posts indexes
CREATE INDEX IF NOT EXISTS blog_posts_slug_idx ON public.blog_posts(slug);
CREATE INDEX IF NOT EXISTS blog_posts_status_idx ON public.blog_posts(status);
CREATE INDEX IF NOT EXISTS blog_posts_featured_idx ON public.blog_posts(featured);
CREATE INDEX IF NOT EXISTS blog_posts_date_idx ON public.blog_posts(date);
CREATE INDEX IF NOT EXISTS blog_posts_view_count_idx ON public.blog_posts(view_count);

-- Projects indexes
CREATE INDEX IF NOT EXISTS projects_status_idx ON public.projects(status);
CREATE INDEX IF NOT EXISTS projects_featured_idx ON public.projects(featured);
CREATE INDEX IF NOT EXISTS projects_order_idx ON public.projects(order_index);
CREATE INDEX IF NOT EXISTS projects_created_at_idx ON public.projects(created_at);

-- Comments indexes
CREATE INDEX IF NOT EXISTS comments_post_slug_idx ON public.comments(post_slug);
CREATE INDEX IF NOT EXISTS comments_status_idx ON public.comments(status);
CREATE INDEX IF NOT EXISTS comments_created_at_idx ON public.comments(created_at);

-- Likes indexes and constraints
CREATE INDEX IF NOT EXISTS likes_post_slug_idx ON public.likes(post_slug);
CREATE INDEX IF NOT EXISTS likes_user_ip_idx ON public.likes(user_ip);
-- Prevent duplicate likes from same IP for same post
CREATE UNIQUE INDEX IF NOT EXISTS likes_post_slug_user_ip_key ON public.likes(post_slug, user_ip);

-- =======================================================
-- 7. ROW LEVEL SECURITY (RLS) POLICIES
-- =======================================================

-- Enable RLS on all tables (except migrations)
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;

-- Blog Posts Policies
DROP POLICY IF EXISTS "blog_posts_select_policy" ON public.blog_posts;
CREATE POLICY "blog_posts_select_policy" ON public.blog_posts
    FOR SELECT USING (status = 'published');

DROP POLICY IF EXISTS "blog_posts_admin_policy" ON public.blog_posts;
CREATE POLICY "blog_posts_admin_policy" ON public.blog_posts
    FOR ALL USING (auth.role() = 'service_role');

-- Projects Policies
DROP POLICY IF EXISTS "projects_select_policy" ON public.projects;
CREATE POLICY "projects_select_policy" ON public.projects
    FOR SELECT USING (status IN ('active', 'archived'));

DROP POLICY IF EXISTS "projects_admin_policy" ON public.projects;
CREATE POLICY "projects_admin_policy" ON public.projects
    FOR ALL USING (auth.role() = 'service_role');

-- Comments Policies
DROP POLICY IF EXISTS "comments_select_policy" ON public.comments;
CREATE POLICY "comments_select_policy" ON public.comments
    FOR SELECT USING (status = 'approved');

DROP POLICY IF EXISTS "comments_insert_policy" ON public.comments;
CREATE POLICY "comments_insert_policy" ON public.comments
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "comments_admin_policy" ON public.comments;
CREATE POLICY "comments_admin_policy" ON public.comments
    FOR UPDATE USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "comments_delete_policy" ON public.comments;
CREATE POLICY "comments_delete_policy" ON public.comments
    FOR DELETE USING (auth.role() = 'service_role');

-- Likes Policies
DROP POLICY IF EXISTS "likes_select_policy" ON public.likes;
CREATE POLICY "likes_select_policy" ON public.likes
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "likes_insert_policy" ON public.likes;
CREATE POLICY "likes_insert_policy" ON public.likes
    FOR INSERT WITH CHECK (true);

DROP POLICY IF EXISTS "likes_delete_policy" ON public.likes;
CREATE POLICY "likes_delete_policy" ON public.likes
    FOR DELETE USING (true);

-- =======================================================
-- 8. TRIGGERS AND FUNCTIONS
-- =======================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER set_blog_posts_updated_at
    BEFORE UPDATE ON public.blog_posts
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER set_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- =======================================================
-- 9. SAMPLE DATA (Optional - Remove in production)
-- =======================================================

-- Insert sample blog post if none exists
-- INSERT INTO public.blog_posts (slug, title, date, author, description, tags, read_time, content, status, featured)
-- SELECT 'welcome-to-my-blog', 'Welcome to My Blog', CURRENT_DATE, 'Your Name', 'This is a sample blog post to get you started', '{"welcome","blog","first-post"}', '2 min read', 'Welcome to my personal blog! This is where I share my thoughts and experiences.', 'published', true
-- WHERE NOT EXISTS (SELECT 1 FROM public.blog_posts);

-- Insert sample project if none exists  
-- INSERT INTO public.projects (title, description, href, status, featured, technologies, order_index)
-- SELECT 'Sample Project', 'This is a sample project to demonstrate the portfolio functionality', 'https://github.com/yourusername/sample-project', 'active', true, '["Next.js", "TypeScript", "Supabase"]'::jsonb, 1
-- WHERE NOT EXISTS (SELECT 1 FROM public.projects); 