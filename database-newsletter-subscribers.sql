-- =======================================================
-- NEWSLETTER SUBSCRIBERS TABLE WITH EMAIL CONFIRMATION
-- =======================================================

CREATE TABLE IF NOT EXISTS public.newsletter_subscribers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'unsubscribed')),
    confirmation_token TEXT UNIQUE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    ip_address TEXT,
    user_agent TEXT,
    source TEXT DEFAULT 'website',
    tags J<PERSON>NB DEFAULT '["newsletter"]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- =======================================================
-- INDEXES FOR PERFORMANCE
-- =======================================================

CREATE INDEX IF NOT EXISTS newsletter_subscribers_email_idx ON public.newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS newsletter_subscribers_status_idx ON public.newsletter_subscribers(status);
CREATE INDEX IF NOT EXISTS newsletter_subscribers_confirmation_token_idx ON public.newsletter_subscribers(confirmation_token);
CREATE INDEX IF NOT EXISTS newsletter_subscribers_created_at_idx ON public.newsletter_subscribers(created_at);
CREATE INDEX IF NOT EXISTS newsletter_subscribers_source_idx ON public.newsletter_subscribers(source);

-- =======================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =======================================================

ALTER TABLE public.newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Remove all existing policies first
DROP POLICY IF EXISTS "newsletter_subscribers_insert_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_confirm_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_unsubscribe_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_admin_policy" ON public.newsletter_subscribers;
DROP POLICY IF EXISTS "newsletter_subscribers_select_policy" ON public.newsletter_subscribers;

-- Allow public (anonymous) users to insert new subscriptions
CREATE POLICY "newsletter_subscribers_insert_policy" ON public.newsletter_subscribers
    FOR INSERT TO anon, authenticated
    WITH CHECK (true);

-- Allow public to update confirmation status (for email confirmation)
CREATE POLICY "newsletter_subscribers_update_confirm_policy" ON public.newsletter_subscribers
    FOR UPDATE TO anon, authenticated
    USING (true)
    WITH CHECK (true);

-- Allow public to read for unsubscribe functionality
CREATE POLICY "newsletter_subscribers_select_policy" ON public.newsletter_subscribers
    FOR SELECT TO anon, authenticated
    USING (true);

-- Admin/service role access to all operations
CREATE POLICY "newsletter_subscribers_admin_policy" ON public.newsletter_subscribers
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- =======================================================
-- TRIGGERS
-- =======================================================

-- Update updated_at timestamp
CREATE TRIGGER set_newsletter_subscribers_updated_at
    BEFORE UPDATE ON public.newsletter_subscribers
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- =======================================================
-- FUNCTIONS
-- =======================================================

-- Function to generate confirmation token
CREATE OR REPLACE FUNCTION public.generate_confirmation_token()
RETURNS TEXT AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'hex');
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup expired tokens (tokens older than 24 hours)
CREATE OR REPLACE FUNCTION public.cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.newsletter_subscribers 
    WHERE status = 'pending' 
    AND created_at < (NOW() - INTERVAL '24 hours');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =======================================================
-- COMMENTS
-- =======================================================

COMMENT ON TABLE public.newsletter_subscribers IS 'Newsletter subscription management with email confirmation';
COMMENT ON COLUMN public.newsletter_subscribers.status IS 'Subscription status: pending (awaiting confirmation), active (confirmed), unsubscribed';
COMMENT ON COLUMN public.newsletter_subscribers.confirmation_token IS 'Unique token for email confirmation, null after confirmation';
COMMENT ON COLUMN public.newsletter_subscribers.confirmed_at IS 'Timestamp when email was confirmed';
COMMENT ON COLUMN public.newsletter_subscribers.source IS 'Where the subscription came from (homepage, footer, etc.)';
COMMENT ON COLUMN public.newsletter_subscribers.tags IS 'JSON array of subscription tags for segmentation';
