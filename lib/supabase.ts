import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Helper function to safely parse JSON with fallback
function safeJSONParse<T>(jsonString: string | null | undefined, fallback: T = [] as T): T {
  try {
    return JSON.parse(jsonString || JSON.stringify(fallback))
  } catch (e) {
    console.warn('Failed to parse JSON:', jsonString, e)
    return fallback
  }
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Public client for read operations
export const supabase =
  supabaseUrl && supabaseAnonKey
    ? createClient(supabaseUrl, supabaseAnonKey, {
        global: {
          fetch: (url, options = {}) => {
            return fetch(url, {
              ...options,
              signal: AbortSignal.timeout(10000), // 10 second timeout
            })
          },
        },
      })
    : null

// Admin client for write operations (server-side only)
export const supabaseAdmin =
  supabaseUrl && supabaseServiceKey
    ? createClient(supabaseUrl, supabaseServiceKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
        global: {
          headers: { 'x-my-custom-header': 'blog-admin' },
          fetch: (url, options = {}) => {
            return fetch(url, {
              ...options,
              signal: AbortSignal.timeout(10000), // 10 second timeout
            })
          },
        },
        realtime: {
          params: {
            eventsPerSecond: 10,
          },
        },
      })
    : null

export interface BlogPost {
  id?: string
  slug: string
  title: string
  date: string
  author: string
  description: string
  tags: string[]
  readTime: string
  content: string
  status?: 'published' | 'draft' | 'disabled'
  featured?: boolean
  view_count?: number
  showAffiliateDisclosure?: boolean
  contentType?: 'markdown' | 'rich'
  created_at?: string
  updated_at?: string
}

export async function createBlogPost(post: Omit<BlogPost, 'id' | 'created_at' | 'updated_at'>) {
  const client = supabaseAdmin || supabase
  if (!client) throw new Error('Supabase is not configured')

  const { data, error } = await client
    .from('blog_posts')
    .insert([
      {
        slug: post.slug,
        title: post.title,
        date: post.date,
        author: post.author,
        description: post.description,
        tags: JSON.stringify(post.tags),
        read_time: post.readTime, // Map camelCase to snake_case
        content: post.content,
        status: post.status || 'published',
        featured: post.featured || false,
        view_count: post.view_count || 0,
        show_affiliate_disclosure: post.showAffiliateDisclosure || false,
      },
    ])
    .select()
    .single()

  if (error) throw error
  return data
}

export async function updateBlogPost(id: string, updates: Partial<BlogPost>): Promise<BlogPost> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const updateData: Record<string, unknown> = { updated_at: new Date().toISOString() }
  if (updates.slug) updateData.slug = updates.slug
  if (updates.title) updateData.title = updates.title
  if (updates.content) updateData.content = updates.content
  if (updates.description) updateData.description = updates.description
  if (updates.author) updateData.author = updates.author
  if (updates.date) updateData.date = updates.date
  if (updates.tags) updateData.tags = JSON.stringify(updates.tags)
  if (updates.readTime) updateData.read_time = updates.readTime
  if (updates.status) updateData.status = updates.status
  if (updates.featured !== undefined) updateData.featured = updates.featured
  if (updates.showAffiliateDisclosure !== undefined)
    updateData.show_affiliate_disclosure = updates.showAffiliateDisclosure

  const { data, error } = await client
    .from('blog_posts')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating blog post:', error)
    throw error
  }

  return {
    id: data.id,
    slug: data.slug,
    title: data.title,
    content: data.content,
    description: data.description,
    author: data.author,
    date: data.date,
    tags: safeJSONParse(data.tags, []),
    readTime: data.read_time,
    status: data.status,
    featured: data.featured,
    view_count: data.view_count,
    showAffiliateDisclosure: data.show_affiliate_disclosure || false,
    created_at: data.created_at,
    updated_at: data.updated_at,
  }
}

export async function deleteBlogPost(id: string): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { error } = await client.from('blog_posts').delete().eq('id', id)

  if (error) {
    console.error('Error deleting blog post:', error)
    throw error
  }
}

export async function getBlogPosts() {
  if (!supabase) {
    console.log('Supabase is not configured')
    return []
  }

  const { data, error } = await supabase
    .from('blog_posts')
    .select('*')
    .order('date', { ascending: false })

  if (error) throw error

  return (
    data?.map((post) => ({
      id: post.id,
      slug: post.slug,
      title: post.title,
      date: post.date,
      author: post.author,
      description: post.description,
      tags: safeJSONParse(post.tags, []),
      readTime: post.read_time, // Map snake_case to camelCase
      content: post.content,
      status: post.status || 'published', // Default to published if field doesn't exist
      featured: post.featured || false, // Default to false if field doesn't exist
      view_count: post.view_count || 0, // Default to 0 if field doesn't exist
      showAffiliateDisclosure: post.show_affiliate_disclosure || false, // Default to false if field doesn't exist
      created_at: post.created_at,
      updated_at: post.updated_at,
    })) || []
  )
}

// Function to get only published blog posts for frontend display
export async function getBlogPostsPublished() {
  if (!supabase) {
    console.log('Supabase is not configured')
    return []
  }

  const { data, error } = await supabase
    .from('blog_posts')
    .select('*')
    .eq('status', 'published') // Only get published posts
    .order('date', { ascending: false })

  if (error) throw error

  return (
    data?.map((post) => ({
      id: post.id,
      slug: post.slug,
      title: post.title,
      date: post.date,
      author: post.author,
      description: post.description,
      tags: safeJSONParse(post.tags, []),
      readTime: post.read_time, // Map snake_case to camelCase
      content: post.content,
      status: post.status || 'published', // Default to published if field doesn't exist
      featured: post.featured || false, // Default to false if field doesn't exist
      view_count: post.view_count || 0, // Default to 0 if field doesn't exist
      showAffiliateDisclosure: post.show_affiliate_disclosure || false, // Default to false if field doesn't exist
      created_at: post.created_at,
      updated_at: post.updated_at,
    })) || []
  )
}

export async function getBlogPostBySlug(slug: string) {
  if (!supabase) throw new Error('Supabase is not configured')

  const { data, error } = await supabase.from('blog_posts').select('*').eq('slug', slug).single()

  if (error) throw error

  return data
    ? {
        id: data.id,
        slug: data.slug,
        title: data.title,
        date: data.date,
        author: data.author,
        description: data.description,
        tags: JSON.parse(data.tags || '[]'),
        readTime: data.read_time, // Map snake_case to camelCase
        content: data.content,
        status: data.status || 'published', // Default to published if field doesn't exist
        featured: data.featured || false, // Default to false if field doesn't exist
        view_count: data.view_count || 0, // Default to 0 if field doesn't exist
        showAffiliateDisclosure: data.show_affiliate_disclosure || false, // Default to false if field doesn't exist
        created_at: data.created_at,
        updated_at: data.updated_at,
      }
    : null
}

export async function getBlogPostById(id: string) {
  if (!supabase) throw new Error('Supabase is not configured')

  const { data, error } = await supabase.from('blog_posts').select('*').eq('id', id).single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // Post not found
    }
    throw error
  }

  return data
    ? {
        id: data.id,
        slug: data.slug,
        title: data.title,
        date: data.date,
        author: data.author,
        description: data.description,
        tags: JSON.parse(data.tags || '[]'),
        readTime: data.read_time, // Map snake_case to camelCase
        content: data.content,
        status: data.status || 'published', // Default to published if field doesn't exist
        featured: data.featured || false, // Default to false if field doesn't exist
        view_count: data.view_count || 0, // Default to 0 if field doesn't exist
        showAffiliateDisclosure: data.show_affiliate_disclosure || false, // Default to false if field doesn't exist
        created_at: data.created_at,
        updated_at: data.updated_at,
      }
    : null
}

export async function incrementViewCount(slug: string) {
  const client = supabaseAdmin || supabase
  if (!client) throw new Error('Supabase is not configured')

  try {
    // First, get the current view count
    const { data: currentPost, error: fetchError } = await client
      .from('blog_posts')
      .select('view_count')
      .eq('slug', slug)
      .single()

    if (fetchError) {
      console.error('Error fetching current view count:', fetchError)
      return
    }

    const currentViewCount = currentPost?.view_count || 0

    // Then update with incremented count
    const { error: updateError } = await client
      .from('blog_posts')
      .update({ view_count: currentViewCount + 1 })
      .eq('slug', slug)

    if (updateError) {
      console.error('Error updating view count:', updateError)
    }
  } catch (error) {
    console.error('Error in incrementViewCount:', error)
  }
}

// Projects interface and functions
export interface Project {
  id?: string
  title: string
  description: string
  href?: string
  imgSrc?: string
  status: 'active' | 'archived' | 'draft'
  featured: boolean
  technologies?: string[]
  created_at?: string
  updated_at?: string
  order_index: number
}

export async function getProjects(): Promise<Project[]> {
  if (!supabase) {
    console.log('Supabase is not configured')
    return []
  }

  const { data, error } = await supabase
    .from('projects')
    .select('*')
    .order('order_index', { ascending: true })

  if (error) {
    console.error('Error fetching projects:', error)
    throw error
  }

  return (
    data?.map((project) => ({
      id: project.id,
      title: project.title,
      description: project.description,
      href: project.href,
      imgSrc: project.img_src,
      status: project.status,
      featured: project.featured,
      technologies: safeJSONParse(project.technologies, []),
      created_at: project.created_at,
      updated_at: project.updated_at,
      order_index: project.order_index,
    })) || []
  )
}

export async function getProjectById(id: string): Promise<Project | null> {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await supabase.from('projects').select('*').eq('id', id).single()

  if (error) {
    if (error.code === 'PGRST116') {
      return null // Project not found
    }
    console.error('Error fetching project:', error)
    return null
  }

  return data
    ? {
        id: data.id,
        title: data.title,
        description: data.description,
        href: data.href,
        imgSrc: data.img_src,
        status: data.status,
        featured: data.featured,
        technologies: safeJSONParse(data.technologies, []),
        created_at: data.created_at,
        updated_at: data.updated_at,
        order_index: data.order_index,
      }
    : null
}

export async function createProject(
  project: Omit<Project, 'id' | 'created_at' | 'updated_at'>
): Promise<Project> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await client
    .from('projects')
    .insert({
      title: project.title,
      description: project.description,
      href: project.href,
      img_src: project.imgSrc,
      status: project.status,
      featured: project.featured,
      technologies: JSON.stringify(project.technologies || []),
      order_index: project.order_index,
    })
    .select()
    .single()

  if (error) {
    console.error('Error creating project:', error)
    throw error
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description,
    href: data.href,
    imgSrc: data.img_src,
    status: data.status,
    featured: data.featured,
    technologies: safeJSONParse(data.technologies, []),
    created_at: data.created_at,
    updated_at: data.updated_at,
    order_index: data.order_index,
  }
}

export async function updateProject(id: string, updates: Partial<Project>): Promise<Project> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const updateData: Record<string, unknown> = { updated_at: new Date().toISOString() }
  if (updates.title) updateData.title = updates.title
  if (updates.description) updateData.description = updates.description
  if (updates.href !== undefined) updateData.href = updates.href
  if (updates.imgSrc !== undefined) updateData.img_src = updates.imgSrc
  if (updates.status) updateData.status = updates.status
  if (updates.featured !== undefined) updateData.featured = updates.featured
  if (updates.technologies) updateData.technologies = JSON.stringify(updates.technologies)
  if (updates.order_index !== undefined) updateData.order_index = updates.order_index

  const { data, error } = await client
    .from('projects')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating project:', error)
    throw error
  }

  return {
    id: data.id,
    title: data.title,
    description: data.description,
    href: data.href,
    imgSrc: data.img_src,
    status: data.status,
    featured: data.featured,
    technologies: JSON.parse(data.technologies || '[]'),
    created_at: data.created_at,
    updated_at: data.updated_at,
    order_index: data.order_index,
  }
}

export async function deleteProject(id: string): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { error } = await client.from('projects').delete().eq('id', id)

  if (error) {
    console.error('Error deleting project:', error)
    throw error
  }
}

// Comments and Likes interfaces and functions
export interface Comment {
  id?: string
  post_slug: string
  author_name: string
  author_email: string
  content: string
  status: 'pending' | 'approved' | 'rejected'
  created_at?: string
  updated_at?: string
}

export interface Like {
  id?: string
  post_slug: string
  user_ip: string
  user_agent?: string
  created_at?: string
}

export interface PostInteraction {
  comment_count: number
  like_count: number
  user_has_liked: boolean
}

// Comment functions
export async function getCommentsBySlug(slug: string): Promise<Comment[]> {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await supabase
    .from('comments')
    .select('*')
    .eq('post_slug', slug)
    .eq('status', 'approved')
    .order('created_at', { ascending: true })

  if (error) {
    console.error('Error fetching comments:', error)
    throw error
  }

  return data || []
}

export async function createComment(
  comment: Omit<Comment, 'id' | 'created_at' | 'updated_at' | 'status'>
): Promise<Comment> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await client
    .from('comments')
    .insert({
      post_slug: comment.post_slug,
      author_name: comment.author_name,
      author_email: comment.author_email,
      content: comment.content,
      status: 'pending',
    })
    .select()
    .single()

  if (error) {
    console.error('Error creating comment:', error)
    throw error
  }

  return data
}

export async function getAllComments(): Promise<Comment[]> {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await supabase
    .from('comments')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching all comments:', error)
    throw error
  }

  return data || []
}

export async function updateCommentStatus(
  id: string,
  status: 'approved' | 'rejected'
): Promise<Comment> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { data, error } = await client
    .from('comments')
    .update({ status, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating comment status:', error)
    throw error
  }

  return data
}

export async function deleteComment(id: string): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  const { error } = await client.from('comments').delete().eq('id', id)

  if (error) {
    console.error('Error deleting comment:', error)
    throw error
  }
}

// Like functions
export async function toggleLike(
  slug: string,
  userIp: string,
  userAgent?: string
): Promise<{ liked: boolean; count: number }> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  // Check if user already liked this post
  const { data: existingLike, error: checkError } = await client
    .from('likes')
    .select('id')
    .eq('post_slug', slug)
    .eq('user_ip', userIp)
    .single()

  if (checkError && checkError.code !== 'PGRST116') {
    console.error('Error checking existing like:', checkError)
    throw checkError
  }

  if (existingLike) {
    // Unlike: remove the like
    const { error: deleteError } = await client.from('likes').delete().eq('id', existingLike.id)

    if (deleteError) {
      console.error('Error removing like:', deleteError)
      throw deleteError
    }
  } else {
    // Like: add new like
    const { error: insertError } = await client.from('likes').insert({
      post_slug: slug,
      user_ip: userIp,
      user_agent: userAgent,
    })

    if (insertError) {
      console.error('Error adding like:', insertError)
      throw insertError
    }
  }

  // Get updated like count
  const { data: likeCount, error: countError } = await client
    .from('likes')
    .select('id')
    .eq('post_slug', slug)

  if (countError) {
    console.error('Error getting like count:', countError)
    throw countError
  }

  return {
    liked: !existingLike,
    count: likeCount?.length || 0,
  }
}

export async function getPostInteraction(slug: string, userIp?: string): Promise<PostInteraction> {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  // Get comment count
  const { data: comments, error: commentError } = await supabase
    .from('comments')
    .select('id')
    .eq('post_slug', slug)
    .eq('status', 'approved')

  if (commentError) {
    console.error('Error getting comment count:', commentError)
    throw commentError
  }

  // Get like count
  const { data: likes, error: likeError } = await supabase
    .from('likes')
    .select('id')
    .eq('post_slug', slug)

  if (likeError) {
    console.error('Error getting like count:', likeError)
    throw likeError
  }

  // Check if user has liked (if userIp provided and not 'unknown')
  let userHasLiked = false
  if (userIp && userIp !== 'unknown') {
    const { data: userLike, error: userLikeError } = await supabase
      .from('likes')
      .select('id')
      .eq('post_slug', slug)
      .eq('user_ip', userIp)
      .single()

    if (userLikeError && userLikeError.code !== 'PGRST116') {
      console.error('Error checking user like:', userLikeError)
    } else if (userLike) {
      userHasLiked = true
    }
  }

  return {
    comment_count: comments?.length || 0,
    like_count: likes?.length || 0,
    user_has_liked: userHasLiked,
  }
}

// =======================================================
// IMAGE UPLOAD FUNCTIONS
// =======================================================

export interface ImageUploadResult {
  url: string
  path: string
  error?: string
}

export async function uploadImage(
  file: File,
  folder: string = 'blog-images'
): Promise<ImageUploadResult> {
  // MUST use admin client for uploads
  if (!supabaseAdmin) {
    const errorMsg =
      'Admin client not available. Missing SUPABASE_SERVICE_ROLE_KEY environment variable.'
    console.error(errorMsg)
    return { url: '', path: '', error: errorMsg }
  }

  // Use environment variable or default to 'blog-assets'
  const bucketName = process.env.SUPABASE_STORAGE_BUCKET || 'blog-assets'

  try {
    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${folder}/${fileName}`

    // Upload file to Supabase Storage
    const { data, error } = await supabaseAdmin.storage.from(bucketName).upload(filePath, file, {
      cacheControl: '3600',
      upsert: false,
    })

    if (error) {
      console.error('Error uploading image:', error)
      console.error('Upload details:', {
        bucketName,
        filePath,
        fileSize: file.size,
        fileType: file.type,
      })
      return { url: '', path: '', error: `Upload failed: ${error.message}` }
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabaseAdmin.storage.from(bucketName).getPublicUrl(filePath)

    return {
      url: publicUrl,
      path: filePath,
    }
  } catch (error) {
    console.error('Error in uploadImage:', error)
    return { url: '', path: '', error: 'Upload failed' }
  }
}

export async function deleteImage(path: string): Promise<boolean> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  // Use environment variable or default to 'blog-assets'
  const bucketName = process.env.SUPABASE_STORAGE_BUCKET || 'blog-assets'

  try {
    const { error } = await client.storage.from(bucketName).remove([path])

    if (error) {
      console.error('Error deleting image:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in deleteImage:', error)
    return false
  }
}

export async function getImageUrl(path: string): Promise<string> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured. Please check your environment variables.')
  }

  // Use environment variable or default to 'blog-assets'
  const bucketName = process.env.SUPABASE_STORAGE_BUCKET || 'blog-assets'

  const {
    data: { publicUrl },
  } = client.storage.from(bucketName).getPublicUrl(path)

  return publicUrl
}

// =======================================================
// CONTACT MESSAGE FUNCTIONS
// =======================================================

export interface ContactMessage {
  id: string
  name: string
  email: string
  subject: string
  message: string
  status: 'new' | 'read' | 'replied'
  ip_address?: string
  user_agent?: string
  created_at: string
}

export async function createContactMessage(
  data: Omit<ContactMessage, 'id' | 'created_at' | 'status'>
): Promise<ContactMessage> {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  const { data: message, error } = await supabase
    .from('contact_messages')
    .insert([
      {
        ...data,
        status: 'new',
      },
    ])
    .select()
    .single()

  if (error) {
    console.error('Error creating contact message:', error)
    throw new Error('Failed to save contact message')
  }

  return message
}

export async function getContactMessages(): Promise<ContactMessage[]> {
  const client = supabaseAdmin || supabase
  if (!client) {
    console.error('Supabase is not configured')
    return []
  }

  try {
    const { data, error } = await client
      .from('contact_messages')
      .select('id, name, email, subject, message, status, created_at')
      .order('created_at', { ascending: false })
      .limit(200) // Limit to prevent large queries

    if (error) {
      console.error('Error fetching contact messages:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Network error fetching contact messages:', error)
    return []
  }
}

export async function updateContactMessageStatus(
  id: string,
  status: ContactMessage['status']
): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured')
  }

  const { error } = await client.from('contact_messages').update({ status }).eq('id', id)

  if (error) {
    console.error('Error updating contact message status:', error)
    throw new Error('Failed to update contact message status')
  }
}

export async function deleteContactMessage(id: string): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured')
  }

  const { error } = await client.from('contact_messages').delete().eq('id', id)

  if (error) {
    console.error('Error deleting contact message:', error)
    throw new Error('Failed to delete contact message')
  }
}

// =======================================================
// NEWSLETTER SUBSCRIPTION FUNCTIONS
// =======================================================

export interface NewsletterSubscriber {
  id: string
  email: string
  status: 'pending' | 'active' | 'unsubscribed'
  confirmation_token?: string
  confirmed_at?: string
  subscribed_at: string
  unsubscribed_at?: string
  ip_address?: string
  user_agent?: string
  source?: string
  tags?: string[]
}

export async function createNewsletterSubscription(
  data: Omit<
    NewsletterSubscriber,
    'id' | 'subscribed_at' | 'status' | 'confirmation_token' | 'confirmed_at'
  >
): Promise<NewsletterSubscriber> {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  // Generate confirmation token
  const confirmationToken = generateRandomToken()

  // Check if email already exists
  const { data: existing } = await supabase
    .from('newsletter_subscribers')
    .select('*')
    .eq('email', data.email.toLowerCase())
    .single()

  if (existing) {
    if (existing.status === 'unsubscribed') {
      // Reactivate subscription with new confirmation token
      const { data: updated, error } = await supabase
        .from('newsletter_subscribers')
        .update({
          status: 'pending',
          confirmation_token: confirmationToken,
          confirmed_at: null,
          subscribed_at: new Date().toISOString(),
          unsubscribed_at: null,
          ip_address: data.ip_address,
          user_agent: data.user_agent,
          source: data.source,
          tags: data.tags,
        })
        .eq('id', existing.id)
        .select()
        .single()

      if (error) {
        console.error('Error reactivating subscription:', error)
        throw new Error('Failed to reactivate subscription')
      }

      return updated
    } else if (existing.status === 'pending') {
      // Update existing pending subscription with new token
      const { data: updated, error } = await supabase
        .from('newsletter_subscribers')
        .update({
          confirmation_token: confirmationToken,
          ip_address: data.ip_address,
          user_agent: data.user_agent,
          source: data.source,
          tags: data.tags,
        })
        .eq('id', existing.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating pending subscription:', error)
        throw new Error('Failed to update subscription')
      }

      return updated
    } else {
      throw new Error('Email is already subscribed')
    }
  }

  const { data: subscription, error } = await supabase
    .from('newsletter_subscribers')
    .insert([
      {
        ...data,
        email: data.email.toLowerCase(),
        status: 'pending',
        confirmation_token: confirmationToken,
      },
    ])
    .select()
    .single()

  if (error) {
    console.error('Error creating newsletter subscription:', error)
    throw new Error('Failed to subscribe to newsletter')
  }

  return subscription
}

// Generate random confirmation token
function generateRandomToken(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('')
}

export async function confirmNewsletterSubscription(
  token: string
): Promise<NewsletterSubscriber | null> {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  // Find subscription by confirmation token
  const { data: subscription, error: findError } = await supabase
    .from('newsletter_subscribers')
    .select('*')
    .eq('confirmation_token', token)
    .eq('status', 'pending')
    .single()

  if (findError || !subscription) {
    console.error('Error finding subscription or invalid token:', findError)
    return null
  }

  // Check if token is expired (24 hours)
  const createdAt = new Date(subscription.subscribed_at)
  const now = new Date()
  const hoursElapsed = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60)

  if (hoursElapsed > 24) {
    // Delete expired subscription
    await supabase.from('newsletter_subscribers').delete().eq('id', subscription.id)

    return null
  }

  // Confirm subscription
  const { data: confirmed, error: confirmError } = await supabase
    .from('newsletter_subscribers')
    .update({
      status: 'active',
      confirmation_token: null,
      confirmed_at: new Date().toISOString(),
    })
    .eq('id', subscription.id)
    .select()
    .single()

  if (confirmError) {
    console.error('Error confirming subscription:', confirmError)
    throw new Error('Failed to confirm subscription')
  }

  return confirmed
}

export async function getNewsletterSubscribers(): Promise<NewsletterSubscriber[]> {
  const client = supabaseAdmin || supabase
  if (!client) {
    console.error('Supabase is not configured')
    return []
  }

  try {
    const { data, error } = await client
      .from('newsletter_subscribers')
      .select('id, email, status, subscribed_at, confirmed_at, source, unsubscribed_at')
      .order('subscribed_at', { ascending: false })
      .limit(500) // Limit to prevent large queries

    if (error) {
      console.error('Error fetching newsletter subscribers:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Network error fetching newsletter subscribers:', error)
    return []
  }
}

export async function updateNewsletterSubscriberStatus(
  id: string,
  status: NewsletterSubscriber['status']
): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured')
  }

  const updateData: any = { status }
  if (status === 'unsubscribed') {
    updateData.unsubscribed_at = new Date().toISOString()
  }

  const { error } = await client.from('newsletter_subscribers').update(updateData).eq('id', id)

  if (error) {
    console.error('Error updating newsletter subscriber status:', error)
    throw new Error('Failed to update subscription status')
  }
}

export async function unsubscribeFromNewsletter(email: string): Promise<void> {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  const { error } = await supabase
    .from('newsletter_subscribers')
    .update({
      status: 'unsubscribed',
      unsubscribed_at: new Date().toISOString(),
    })
    .eq('email', email.toLowerCase())

  if (error) {
    console.error('Error unsubscribing from newsletter:', error)
    throw new Error('Failed to unsubscribe from newsletter')
  }
}

export async function deleteNewsletterSubscriber(id: string): Promise<void> {
  const client = supabaseAdmin || supabase
  if (!client) {
    throw new Error('Supabase is not configured')
  }

  const { error } = await client.from('newsletter_subscribers').delete().eq('id', id)

  if (error) {
    console.error('Error deleting newsletter subscriber:', error)
    throw new Error('Failed to delete subscriber')
  }
}
