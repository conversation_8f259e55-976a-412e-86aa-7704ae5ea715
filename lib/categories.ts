// lib/categories.ts
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export interface Category {
  id: string
  name: string
  slug: string
  description: string
  icon: string
  created_at: string
  updated_at: string
}

export interface CategoryTag {
  id: string
  category_id: string
  tag: string
  created_at: string
}

// Get all categories
export async function getCategories(): Promise<Category[]> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name')

  if (error) {
    console.error('Error fetching categories:', error)
    return []
  }

  return data || []
}

// Get category by slug
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('slug', slug)
    .single()

  if (error) {
    console.error('Error fetching category:', error)
    return null
  }

  return data
}

// Get tags for a category
export async function getCategoryTags(categoryId: string): Promise<string[]> {
  const { data, error } = await supabase
    .from('category_tags')
    .select('tag')
    .eq('category_id', categoryId)

  if (error) {
    console.error('Error fetching category tags:', error)
    return []
  }

  return data?.map(item => item.tag) || []
}

// Create category
export async function createCategory(category: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category | null> {
  const { data, error } = await supabase
    .from('categories')
    .insert(category)
    .select()
    .single()

  if (error) {
    console.error('Error creating category:', error)
    return null
  }

  return data
}

// Update category
export async function updateCategory(id: string, updates: Partial<Category>): Promise<Category | null> {
  const { data, error } = await supabase
    .from('categories')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating category:', error)
    return null
  }

  return data
}

// Delete category
export async function deleteCategory(id: string): Promise<boolean> {
  const { error } = await supabase
    .from('categories')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting category:', error)
    return false
  }

  return true
}

// Add tag to category
export async function addCategoryTag(categoryId: string, tag: string): Promise<boolean> {
  const { error } = await supabase
    .from('category_tags')
    .insert({ category_id: categoryId, tag })

  if (error) {
    console.error('Error adding category tag:', error)
    return false
  }

  return true
}

// Remove tag from category
export async function removeCategoryTag(categoryId: string, tag: string): Promise<boolean> {
  const { error } = await supabase
    .from('category_tags')
    .delete()
    .eq('category_id', categoryId)
    .eq('tag', tag)

  if (error) {
    console.error('Error removing category tag:', error)
    return false
  }

  return true
}
