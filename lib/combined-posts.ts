import { getBlogPostsPublished, getBlogPosts } from './supabase'

export interface CombinedPost {
  slug: string
  title: string
  date: string
  summary?: string
  description?: string
  tags: string[]
  author?: string
  readTime?: string
  source: 'supabase' | 'mdx'
  path: string // Path for routing, required by template
  // For MDX posts
  body?: unknown
  // For Supabase posts
  content?: string
  id?: string
  status?: string
  view_count?: number
  showAffiliateDisclosure?: boolean
  contentType?: 'markdown' | 'rich'
}

export async function getCombinedPosts(): Promise<CombinedPost[]> {
  try {
    // Get only published Supabase posts
    const supabasePosts = await getBlogPostsPublished()

    // Convert Supabase posts to combined format (already filtered by status)
    const combinedPosts: CombinedPost[] = supabasePosts
      .map((post) => ({
        slug: post.slug,
        title: post.title,
        date: post.date,
        description: post.description,
        summary: post.description, // Use description as summary for consistency
        tags: post.tags,
        author: post.author,
        readTime: post.readTime,
        source: 'supabase' as const,
        content: post.content,
        id: post.id,
        status: post.status,
        view_count: post.view_count,
        showAffiliateDisclosure: post.showAffiliateDisclosure,
        path: `blog/${post.slug}`, // Add path for compatibility with template
      }))

    // Sort all posts by date (newest first)
    combinedPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    return combinedPosts
  } catch (error) {
    console.error('Error getting combined posts:', error)
    return []
  }
}

export async function getCombinedPostBySlug(slug: string): Promise<CombinedPost | null> {
  // Only skip database calls during BUILD TIME (when building the app)
  // Check for build phase specifically, not just production mode
  if (process.env.NODE_ENV === 'production' && process.env.BUILD_PHASE === 'true') {
    console.log('Skipping database call during build phase')
    return null
  }
  
  try {
    // Get only published Supabase posts
    const supabasePosts = await getBlogPostsPublished()
    const supabasePost = supabasePosts.find((post) => post.slug === slug)

    if (supabasePost) {
      return {
        slug: supabasePost.slug,
        title: supabasePost.title,
        date: supabasePost.date,
        description: supabasePost.description,
        summary: supabasePost.description,
        tags: supabasePost.tags,
        author: supabasePost.author,
        readTime: supabasePost.readTime,
        source: 'supabase' as const,
        content: supabasePost.content,
        id: supabasePost.id,
        status: supabasePost.status,
        view_count: supabasePost.view_count,
        showAffiliateDisclosure: supabasePost.showAffiliateDisclosure,
        path: `blog/${supabasePost.slug}`,
      }
    }

    return null
  } catch (error) {
    console.error('Error getting post by slug:', error)
    return null
  }
}

// Admin function to get any post regardless of status (for previews)
export async function getCombinedPostBySlugAdmin(slug: string): Promise<CombinedPost | null> {
  // Only skip database calls during BUILD TIME (when building the app)
  if (process.env.NODE_ENV === 'production' && process.env.BUILD_PHASE === 'true') {
    console.log('Skipping database call during build phase')
    return null
  }
  
  try {
    // Get all Supabase posts (any status for admin)
    const supabasePosts = await getBlogPosts()
    const supabasePost = supabasePosts.find((post) => post.slug === slug) // No status filter for admin

    if (supabasePost) {
      return {
        slug: supabasePost.slug,
        title: supabasePost.title,
        date: supabasePost.date,
        description: supabasePost.description,
        summary: supabasePost.description,
        tags: supabasePost.tags,
        author: supabasePost.author,
        readTime: supabasePost.readTime,
        source: 'supabase' as const,
        content: supabasePost.content,
        id: supabasePost.id,
        status: supabasePost.status,
        view_count: supabasePost.view_count,
        showAffiliateDisclosure: supabasePost.showAffiliateDisclosure,
        path: `blog/${supabasePost.slug}`,
      }
    }

    return null
  } catch (error) {
    console.error('Error getting admin post by slug:', error)
    return null
  }
}
