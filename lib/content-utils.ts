/**
 * Utility functions for handling different content types (HTML vs Markdown)
 */

/**
 * Detects if content is HTML or Markdown based on common HTML patterns
 * @param content - The content string to analyze
 * @returns true if content appears to be HTML, false if it appears to be Markdown
 */
export function isHtmlContent(content: string): boolean {
  if (!content || typeof content !== 'string') {
    return false
  }

  // Check for common HTML tags that wouldn't appear in plain markdown
  const htmlTags =
    /<(p|div|h[1-6]|span|strong|em|ul|ol|li|table|tr|td|th|img|iframe|youtube|blockquote|figure|figcaption|pre|code|a)\s*[^>]*>/i

  // Check for HTML entities
  const htmlEntities = /&(amp|lt|gt|quot|#\d+|#x[0-9a-f]+);/i

  // Check for self-closing tags
  const selfClosingTags = /<(br|hr|img|input|meta|link)\s*[^>]*\/>/i

  // Check for HTML structure patterns
  const htmlStructure = /<\/?(html|head|body|title|meta|link|script|style)/i

  return (
    htmlTags.test(content) ||
    htmlEntities.test(content) ||
    selfClosingTags.test(content) ||
    htmlStructure.test(content)
  )
}

/**
 * Detects the content type of a blog post
 * @param content - The content string to analyze
 * @returns 'rich' for HTML content, 'markdown' for Markdown content
 */
export function detectContentType(content: string): 'rich' | 'markdown' {
  return isHtmlContent(content) ? 'rich' : 'markdown'
}

/**
 * Sanitizes HTML content to prevent XSS attacks while preserving formatting
 * Note: In a production environment, you might want to use a library like DOMPurify
 * @param html - The HTML string to sanitize
 * @returns Sanitized HTML string
 */
export function sanitizeHtml(html: string): string {
  // Basic sanitization - in production, use DOMPurify or similar
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+="[^"]*"/gi, '') // Remove event handlers
    .replace(/on\w+='[^']*'/gi, '') // Remove event handlers (single quotes)
}

/**
 * Converts Rich Text Editor HTML to a plain text summary
 * @param html - HTML content from Rich Text Editor
 * @param maxLength - Maximum length of the summary (default: 160)
 * @returns Plain text summary
 */
export function htmlToTextSummary(html: string, maxLength: number = 160): string {
  // Remove HTML tags and get plain text
  const text = html
    .replace(/<[^>]*>/g, ' ') // Remove HTML tags
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()

  // Truncate to maxLength and add ellipsis if needed
  if (text.length <= maxLength) {
    return text
  }

  const truncated = text.substring(0, maxLength)
  const lastSpace = truncated.lastIndexOf(' ')

  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + '...'
  }

  return truncated + '...'
}

/**
 * Estimates reading time for content
 * @param content - The content (HTML or Markdown)
 * @param wordsPerMinute - Average reading speed (default: 200 WPM)
 * @returns Reading time estimate string
 */
export function estimateReadingTime(content: string, wordsPerMinute: number = 200): string {
  // Remove HTML tags and get plain text
  const text = content.replace(/<[^>]*>/g, ' ')

  // Count words (split by whitespace and filter empty strings)
  const wordCount = text.split(/\s+/).filter((word) => word.length > 0).length

  // Calculate reading time in minutes
  const minutes = Math.ceil(wordCount / wordsPerMinute)

  if (minutes === 1) {
    return '1 min read'
  }

  return `${minutes} min read`
}
