interface SEOAnalysisResult {
  score: number
  issues: string[]
  suggestions: string[]
  keywords: string[]
  readability: {
    score: number
    level: string
  }
}

interface ContentMetrics {
  wordCount: number
  readingTime: number
  headingStructure: {
    h1: number
    h2: number
    h3: number
    h4: number
    h5: number
    h6: number
  }
  images: number
  links: {
    internal: number
    external: number
  }
}

export class SEOAnalyzer {
  static analyzeContent(content: string, title: string, description: string): SEOAnalysisResult {
    const issues: string[] = []
    const suggestions: string[] = []
    let score = 100

    // Title analysis
    if (title.length < 30) {
      issues.push('Title is too short (less than 30 characters)')
      suggestions.push('Consider expanding your title to 50-60 characters for better SEO')
      score -= 10
    }
    if (title.length > 60) {
      issues.push('Title is too long (over 60 characters)')
      suggestions.push('Shorten your title to under 60 characters to prevent truncation in search results')
      score -= 10
    }

    // Description analysis
    if (description.length < 120) {
      issues.push('Meta description is too short')
      suggestions.push('Expand your meta description to 150-160 characters')
      score -= 15
    }
    if (description.length > 160) {
      issues.push('Meta description is too long')
      suggestions.push('Shorten your meta description to under 160 characters')
      score -= 10
    }

    // Content length analysis
    const wordCount = this.getWordCount(content)
    if (wordCount < 300) {
      issues.push('Content is too short for good SEO ranking')
      suggestions.push('Add more valuable content. Aim for at least 300-500 words')
      score -= 20
    }

    // Heading structure analysis
    const headingStructure = this.analyzeHeadingStructure(content)
    if (headingStructure.h1 === 0) {
      issues.push('No H1 heading found')
      suggestions.push('Add a clear H1 heading to your content')
      score -= 15
    }
    if (headingStructure.h1 > 1) {
      issues.push('Multiple H1 headings found')
      suggestions.push('Use only one H1 heading per page')
      score -= 10
    }

    // Keyword analysis
    const keywords = this.extractKeywords(content, title)

    // Readability analysis
    const readability = this.analyzeReadability(content)

    return {
      score: Math.max(0, score),
      issues,
      suggestions,
      keywords,
      readability,
    }
  }

  static getContentMetrics(content: string): ContentMetrics {
    return {
      wordCount: this.getWordCount(content),
      readingTime: Math.ceil(this.getWordCount(content) / 200),
      headingStructure: this.analyzeHeadingStructure(content),
      images: (content.match(/!\[.*?\]\(.*?\)/g) || []).length,
      links: {
        internal: (content.match(/\[.*?\]\(\/.*?\)/g) || []).length,
        external: (content.match(/\[.*?\]\(https?:\/\/.*?\)/g) || []).length,
      },
    }
  }

  private static getWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  private static analyzeHeadingStructure(content: string) {
    return {
      h1: (content.match(/^# /gm) || []).length,
      h2: (content.match(/^## /gm) || []).length,
      h3: (content.match(/^### /gm) || []).length,
      h4: (content.match(/^#### /gm) || []).length,
      h5: (content.match(/^##### /gm) || []).length,
      h6: (content.match(/^###### /gm) || []).length,
    }
  }

  private static extractKeywords(content: string, title: string): string[] {
    const text = (content + ' ' + title).toLowerCase()
    const words = text.match(/\b\w{4,}\b/g) || []
    const wordFreq: { [key: string]: number } = {}

    words.forEach(word => {
      if (!this.isStopWord(word)) {
        wordFreq[word] = (wordFreq[word] || 0) + 1
      }
    })

    return Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word)
  }

  private static isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
      'might', 'must', 'can', 'shall', 'from', 'up', 'out', 'down', 'off', 'over', 'under',
      'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how',
      'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no',
      'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'just'
    ])
    return stopWords.has(word)
  }

  private static analyzeReadability(content: string): { score: number; level: string } {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = content.trim().split(/\s+/)
    const syllables = words.reduce((total, word) => total + this.countSyllables(word), 0)

    if (sentences.length === 0 || words.length === 0) {
      return { score: 0, level: 'Unknown' }
    }

    // Flesch Reading Ease Score
    const avgWordsPerSentence = words.length / sentences.length
    const avgSyllablesPerWord = syllables / words.length
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)

    let level: string
    if (score >= 90) level = 'Very Easy'
    else if (score >= 80) level = 'Easy'
    else if (score >= 70) level = 'Fairly Easy'
    else if (score >= 60) level = 'Standard'
    else if (score >= 50) level = 'Fairly Difficult'
    else if (score >= 30) level = 'Difficult'
    else level = 'Very Difficult'

    return { score: Math.max(0, Math.min(100, score)), level }
  }

  private static countSyllables(word: string): number {
    word = word.toLowerCase()
    if (word.length <= 3) return 1
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '')
    word = word.replace(/^y/, '')
    const matches = word.match(/[aeiouy]{1,2}/g)
    return matches ? matches.length : 1
  }

  static generateSEOTips(): string[] {
    return [
      'Use your target keyword in the first 100 words of your content',
      'Include your keyword in at least one H2 or H3 heading',
      'Add internal links to related content on your site',
      'Use descriptive alt text for all images',
      'Keep paragraphs short (2-3 sentences) for better readability',
      'Use bullet points and numbered lists to break up content',
      'Include a call-to-action (CTA) in your content',
      'Add social sharing buttons to increase engagement',
      'Use schema markup for better search engine understanding',
      'Optimize your images for faster loading times',
      'Create a compelling meta description that includes your keyword',
      'Use long-tail keywords throughout your content naturally',
      'Add a table of contents for longer articles',
      'Include recent statistics and data to keep content fresh',
      'Use questions as headings to match voice search queries'
    ]
  }
} 