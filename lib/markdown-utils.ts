// utils/markdownUtils.ts

/**
 * Detects if a text block is likely a pipe-style markdown table
 */
export function isMarkdownTable(text: string): boolean {
  // Simple detection: has pipe characters, multiple lines, and header separator row
  const lines = text.trim().split('\n');
  if (lines.length < 3) return false;
  
  // Check for pipe characters
  if (!lines[0].includes('|') || !lines[1].includes('|')) return false;
  
  // Check for header separator row (contains only |, -, and spaces)
  const separatorLine = lines[1].trim();
  return /^[\|\-\s]+$/.test(separatorLine);
}

/**
 * Converts a pipe-style markdown table to HTML table
 */
export function markdownTableToHtml(markdown: string): string {
  const lines = markdown.trim().split('\n');
  if (lines.length < 3) return markdown;
  
  // Start building HTML table
  let html = '<table class="markdown-table">\n<thead>\n<tr>';
  
  // Parse headers
  const headers = lines[0].split('|')
    .filter(cell => cell.trim() !== '')
    .map(header => header.trim());
  
  for (const header of headers) {
    html += `\n  <th>${header}</th>`;
  }
  html += '\n</tr>\n</thead>\n<tbody>';
  
  // Parse rows (skip header and separator rows)
  for (let i = 2; i < lines.length; i++) {
    if (!lines[i].trim()) continue;
    
    html += '\n<tr>';
    const cells = lines[i].split('|')
      .filter(cell => cell.trim() !== '')
      .map(cell => cell.trim());
    
    for (const cell of cells) {
      html += `\n  <td>${cell}</td>`;
    }
    html += '\n</tr>';
  }
  
  html += '\n</tbody>\n</table>';
  return html;
}
