import { getProjects, Project as SupabaseProject } from './supabase'

export interface CombinedProject {
  id?: string
  title: string
  description: string
  href?: string
  imgSrc?: string
  status?: 'active' | 'archived' | 'draft'
  featured?: boolean
  technologies?: string[]
  source: 'static' | 'supabase'
  order_index?: number
}

export async function getCombinedProjects(): Promise<CombinedProject[]> {
  // Get Supabase projects only
  try {
    const supabaseProjects = await getProjects()
    const activeProjects = supabaseProjects
      .filter((project) => project.status === 'active')
      .map((project) => ({
        ...project,
        source: 'supabase' as const,
      }))

    // Sort by order_index, then by featured, then by title
    return activeProjects.sort((a, b) => {
      // Primary sort: by order_index (lower numbers first)
      const orderA = a.order_index || 0
      const orderB = b.order_index || 0

      if (orderA !== orderB) {
        return orderA - orderB
      }

      // Secondary sort: featured projects first
      if (a.featured && !b.featured) return -1
      if (!a.featured && b.featured) return 1

      // Tertiary sort: by title alphabetically
      return a.title.localeCompare(b.title)
    })
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.log('Supabase projects not available:', error)
    }
    return []
  }
}

export async function getPublicProjects(): Promise<CombinedProject[]> {
  const projects = await getCombinedProjects()
  // Only return active projects for public consumption
  return projects.filter((project) => project.status === 'active')
}

export async function getAllProjects() {
  try {
    return await getProjects()
  } catch (error) {
    console.error('Error fetching all projects:', error)
    return []
  }
}
