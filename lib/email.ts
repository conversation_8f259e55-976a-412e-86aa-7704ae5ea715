// =======================================================
// EMAIL SERVICE FOR MAILTRAP
// =======================================================

interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
}

interface MailtrapConfig {
  apiKey: string
  fromEmail: string
  fromName: string
}

// Get Mailtrap configuration from environment variables
const getMailtrapConfig = (): MailtrapConfig => {
  const apiKey = process.env.MAILTRAP_API_KEY
  const fromEmail = process.env.MAILTRAP_FROM_EMAIL || '<EMAIL>'
  const fromName = process.env.MAILTRAP_FROM_NAME || 'Your Blog'

  if (!apiKey) {
    throw new Error('MAILTRAP_API_KEY is not configured')
  }

  return { apiKey, fromEmail, fromName }
}

// Send email via Mailtrap API
export async function sendEmail(data: EmailData): Promise<boolean> {
  try {
    const config = getMailtrapConfig()

    console.log('📧 Attempting to send email via Mailtrap...')
    console.log('  To:', data.to)
    console.log('  Subject:', data.subject)
    console.log('  From:', config.fromEmail)

    const payload = {
      from: {
        email: config.fromEmail,
        name: config.fromName,
      },
      to: [
        {
          email: data.to,
        },
      ],
      subject: data.subject,
      html: data.html,
      text: data.text,
    }

    const response = await fetch('https://send.api.mailtrap.io/api/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify(payload),
    })

    console.log('📤 Mailtrap API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Mailtrap API error:', response.status, errorText)
      console.error('   Request payload:', JSON.stringify(payload, null, 2))
      return false
    }

    const result = await response.json()
    console.log('✅ Email sent successfully via Mailtrap!')
    console.log('   Message ID:', result.message_id || 'N/A')
    console.log('   Response:', result)
    return true
  } catch (error) {
    console.error('❌ Error sending email via Mailtrap:', error)
    console.error('   Error details:', error.message)
    return false
  }
}

// Generate confirmation email HTML
export function generateConfirmationEmail(
  email: string,
  confirmationToken: string,
  baseUrl: string
): { html: string; text: string } {
  const confirmationUrl = `${baseUrl}/api/newsletter/confirm?token=${confirmationToken}`

  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your Newsletter Subscription</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1a202c;
            margin: 0;
            font-size: 24px;
        }
        .content {
            text-align: center;
        }
        .button {
            display: inline-block;
            background: #3182ce;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .button:hover {
            background: #2c5aa0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .link {
            color: #3182ce;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>&#128231; Confirm Your Subscription</h1>
        </div>
        
        <div class="content">
            <p>Hi there!</p>
            <p>Thank you for subscribing to our newsletter. To complete your subscription, please click the button below to confirm your email address:</p>
            
            <a href="${confirmationUrl}" class="button">Confirm Subscription</a>
            
            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p><a href="${confirmationUrl}" class="link">${confirmationUrl}</a></p>
            
            <p>This confirmation link will expire in 24 hours for security reasons.</p>
        </div>
        
        <div class="footer">
            <p>If you didn't subscribe to this newsletter, you can safely ignore this email.</p>
            <p>This email was sent to: ${email}</p>
        </div>
    </div>
</body>
</html>
  `

  const text = `
Confirm Your Newsletter Subscription

Hi there!

Thank you for subscribing to our newsletter. To complete your subscription, please visit this link to confirm your email address:

${confirmationUrl}

This confirmation link will expire in 24 hours for security reasons.

If you didn't subscribe to this newsletter, you can safely ignore this email.

This email was sent to: ${email}
  `

  return { html, text }
}

// Generate welcome email after confirmation
export function generateWelcomeEmail(email: string): { html: string; text: string } {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Our Newsletter!</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1a202c;
            margin: 0;
            font-size: 24px;
        }
        .content {
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>&#127881; Welcome!</h1>
        </div>
        
        <div class="content">
            <p>Welcome to our newsletter!</p>
            <p>Your email has been successfully confirmed and you're now subscribed to receive our latest updates, articles, and insights.</p>
            <p>We're excited to have you on board! &#128640;</p>
        </div>
        
        <div class="footer">
            <p>You can unsubscribe at any time by clicking the unsubscribe link in any of our emails.</p>
            <p>This email was sent to: ${email}</p>
        </div>
    </div>
</body>
</html>
  `

  const text = `
Welcome to our newsletter!

Your email has been successfully confirmed and you're now subscribed to receive our latest updates, articles, and insights.

We're excited to have you on board!

You can unsubscribe at any time by clicking the unsubscribe link in any of our emails.

This email was sent to: ${email}
  `

  return { html, text }
}

// Generate contact notification email for admin
export function generateContactNotificationEmail(
  name: string,
  email: string,
  subject: string,
  message: string,
  ipAddress?: string
): { html: string; text: string } {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Contact Form Submission</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3182ce;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #1a202c;
            margin: 0;
            font-size: 24px;
        }
        .field {
            margin-bottom: 20px;
        }
        .field-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }
        .field-content {
            background: #f9fafb;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #3182ce;
        }
        .message-content {
            background: #f0f9ff;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #bae6fd;
            white-space: pre-wrap;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .metadata {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
            color: #6b7280;
        }
        .reply-button {
            display: inline-block;
            background: #3182ce;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>&#128231; New Contact Message</h1>
        </div>
        
        <div class="field">
            <div class="field-label">From:</div>
            <div class="field-content">
                <strong>${name}</strong><br>
                <a href="mailto:${email}">${email}</a>
            </div>
        </div>
        
        <div class="field">
            <div class="field-label">Subject:</div>
            <div class="field-content">${subject}</div>
        </div>
        
        <div class="field">
            <div class="field-label">Message:</div>
            <div class="message-content">${message}</div>
        </div>
        
        <div style="text-align: center;">
            <a href="mailto:${email}?subject=Re: ${encodeURIComponent(subject)}" class="reply-button">
                Reply to ${name}
            </a>
        </div>
        
        ${
          ipAddress
            ? `
        <div class="metadata">
            <strong>Additional Information:</strong><br>
            Submitted: ${new Date().toLocaleString()}<br>
            IP Address: ${ipAddress}
        </div>
        `
            : ''
        }
    </div>
</body>
</html>
  `

  const text = `
New Contact Form Submission

From: ${name} <${email}>
Subject: ${subject}

Message:
${message}

${
  ipAddress
    ? `
---
Submitted: ${new Date().toLocaleString()}
IP Address: ${ipAddress}
`
    : ''
}

Reply to this message: mailto:${email}?subject=Re: ${encodeURIComponent(subject)}
  `

  return { html, text }
}
