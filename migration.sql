-- ===============================================
-- BLOG DATABASE MIGRATION STATUS
-- ===============================================
-- This file tracks the migration history for your blog database.
-- All migrations listed below have been successfully applied.

-- ✅ COMPLETED MIGRATIONS:
-- 001_add_affiliate_disclosure - Added affiliate disclosure flag to blog posts

-- ===============================================
-- MIGRATION: 001_add_affiliate_disclosure
-- ===============================================
-- Status: ✅ COMPLETED (Applied: 2025-06-22)
-- 
-- This migration added the affiliate disclosure functionality:
-- 1. Created migrations tracking table
-- 2. Added show_affiliate_disclosure boolean column to blog_posts
-- 3. Set default values for existing posts
-- 4. Added column documentation

-- If you need to manually apply this migration, use:
/*
CREATE TABLE IF NOT EXISTS migrations (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

ALTER TABLE blog_posts 
ADD COLUMN IF NOT EXISTS show_affiliate_disclosure BOOLEAN DEFAULT false;

UPDATE blog_posts 
SET show_affiliate_disclosure = false 
WHERE show_affiliate_disclosure IS NULL;

COMMENT ON COLUMN blog_posts.show_affiliate_disclosure 
IS 'Flag to control whether affiliate disclosure is shown at the end of the post';

INSERT INTO migrations (id, name) VALUES ('001_add_affiliate_disclosure', 'Add affiliate disclosure flag to blog posts');
*/

-- ===============================================
-- DATABASE STATUS: ✅ UP TO DATE
-- ===============================================
-- Your database schema is current and matches your application code.
-- All tables, indexes, and security policies are properly configured.