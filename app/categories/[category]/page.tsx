import { getCombinedPosts } from '../../../lib/combined-posts'
import { getCategoryBySlug, getCategoryTags } from '../../../lib/categories'
import ListLayoutWithTags from '@/layouts/ListLayoutWithTags'
import { genPageMetadata } from 'app/seo'
import { notFound } from 'next/navigation'

type Props = {
  params: Promise<{
    category: string
  }>
}

export async function generateStaticParams() {
  // For now return empty array to use dynamic rendering
  // Can be enhanced with database categories later
  return []
}

export async function generateMetadata({ params }: Props) {
  const { category } = await params
  const categoryData = await getCategoryBySlug(category)
  
  if (!categoryData) {
    return {}
  }

  return genPageMetadata({
    title: categoryData.name,
    description: `${categoryData.description} - Browse all ${categoryData.name.toLowerCase()} articles`,
  })
}

export default async function CategoryPage({ params }: Props) {
  try {
    const { category } = await params
    const categoryData = await getCategoryBySlug(category)
    
    if (!categoryData) {
      notFound()
    }

    const [allPosts, categoryTags] = await Promise.all([
      getCombinedPosts(),
      getCategoryTags(categoryData.id)
    ])

    const filteredPosts = allPosts.filter(post => 
      post.tags?.some((tag: string) => 
        categoryTags.includes(tag.toLowerCase())
      )
    )

    return (
      <ListLayoutWithTags
        posts={allPosts}
        initialDisplayPosts={filteredPosts}
        title={categoryData.name}
        tagInfo={{
          name: categoryData.name,
          slug: category,
          count: filteredPosts.length
        }}
      />
    )
  } catch (error) {
    console.error('Category page error:', error)
    notFound()
  }
}
