import Link from '@/components/Link'
import PageHeader from '@/components/PageHeader'
import CategoriesFallback from '@/components/CategoriesFallback'
import { getCombinedPosts } from '../../lib/combined-posts'
import { getCategories, getCategoryTags } from '../../lib/categories'
import { genPageMetadata } from 'app/seo'
import { 
  Layers, 
  ArrowRight,
  Code,
  Server,
  Cloud,
  Wrench,
  Folder
} from 'lucide-react'

// Force dynamic rendering
export const dynamic = 'force-dynamic'

export const metadata = genPageMetadata({
  title: 'Categories',
  description: 'Browse articles by category and topic',
})

// Icon mapping for database icons
const ICON_MAP: Record<string, any> = {
  Code,
  Server,
  Cloud,
  Wrench,
  Folder
}

// Get category counts from posts
async function getCategoryCounts() {
  try {
    const [posts, categories] = await Promise.all([
      getCombinedPosts(),
      getCategories()
    ])
    
    const categoryCounts: Record<string, number> = {}
    
    for (const category of categories) {
      const tags = await getCategoryTags(category.id)
      const count = posts.filter(post => 
        post.tags?.some((tag: string) => 
          tags.includes(tag.toLowerCase())
        )
      ).length
      
      categoryCounts[category.slug] = count
    }
    
    return { categoryCounts, categories, posts }
  } catch (error) {
    console.error('Error counting categories:', error)
    return { categoryCounts: {}, categories: [], posts: [] }
  }
}

export default async function CategoriesPage() {
  try {
    const { categoryCounts, categories, posts } = await getCategoryCounts()
    
    // If no categories available (database not set up), show fallback
    if (categories.length === 0) {
      return <CategoriesFallback />
    }

    const totalPosts = posts.length

    return (
      <>
        <PageHeader
          title="Categories"
          description={`Explore ${categories.length} main categories covering all aspects of modern web development`}
          icon={<Layers className="w-4 h-4" />}
          badge="Categories"
        />

        <div className="bg-slate-50 dark:bg-slate-900 min-h-screen">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="space-y-8">
              {/* Stats - Minimal without colors */}
              <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-slate-900 dark:text-white">{categories.length}</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Categories</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-slate-900 dark:text-white">{totalPosts}</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Articles</div>
                  </div>
                </div>
              </div>

              {/* Categories Grid - Minimal design */}
              <div className="grid gap-6">
                {categories.map((category) => {
                  const Icon = ICON_MAP[category.icon] || Folder
                  const count = categoryCounts[category.slug] || 0

                  return (
                    <Link
                      key={category.id}
                      href={`/categories/${category.slug}`}
                      className="group block"
                    >
                      <div className="p-6 rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 transition-all duration-200 group-hover:shadow-md group-hover:border-slate-300 dark:group-hover:border-slate-600">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4">
                            <div className="p-3 rounded-lg bg-slate-100 dark:bg-slate-700">
                              <Icon className="w-6 h-6 text-slate-600 dark:text-slate-400" />
                            </div>
                            <div className="flex-1">
                              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{category.name}</h3>
                              <p className="text-sm text-slate-600 dark:text-slate-400">{category.description}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-slate-900 dark:text-white">{count}</div>
                            <div className="text-xs text-slate-500 dark:text-slate-400">articles</div>
                            <ArrowRight className="w-4 h-4 mt-2 text-slate-400 transition-transform group-hover:translate-x-1" />
                          </div>
                        </div>
                      </div>
                    </Link>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Categories page error:', error)
    return <CategoriesFallback />
  }
}
