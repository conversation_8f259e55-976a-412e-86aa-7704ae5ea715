import { MetadataRoute } from 'next'
import siteMetadata from '@/data/siteMetadata'
import { getCombinedPosts } from '../lib/combined-posts'
import { getPublicProjects } from '../lib/combined-projects'

// Force dynamic generation to include latest posts
export const dynamic = 'force-dynamic'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const siteUrl = siteMetadata.siteUrl

  try {
    // Get published blog posts
    const posts = await getCombinedPosts()
    const publishedPosts = posts.filter((post) => post.status === 'published')

    const blogRoutes = publishedPosts.map((post) => ({
      url: `${siteUrl}/blog/${post.slug}`,
      lastModified: new Date(post.date).toISOString(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))

    // Get active projects
    const projects = await getPublicProjects()
    const projectRoutes = projects.map((project, index) => ({
      url: `${siteUrl}/projects#${project.title.toLowerCase().replace(/\s+/g, '-')}`,
      lastModified: new Date().toISOString(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }))

    // Get all tags from posts
    const allTags = new Set<string>()
    publishedPosts.forEach((post) => {
      post.tags?.forEach((tag) => allTags.add(tag))
    })

    const tagRoutes = Array.from(allTags).map((tag) => ({
      url: `${siteUrl}/tags/${tag}`,
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly' as const,
      priority: 0.5,
    }))

    // Main site routes
    const routes = [
      {
        url: siteUrl,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily' as const,
        priority: 1.0,
      },
      {
        url: `${siteUrl}/blog`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${siteUrl}/projects`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      },
      {
        url: `${siteUrl}/tags`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      },
      {
        url: `${siteUrl}/about`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'monthly' as const,
        priority: 0.6,
      },
    ]

    return [...routes, ...blogRoutes, ...projectRoutes, ...tagRoutes]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    // Fallback to basic routes if database is unavailable
    const routes = ['', 'blog', 'projects', 'tags', 'about'].map((route) => ({
      url: `${siteUrl}/${route}`,
      lastModified: new Date().toISOString(),
      changeFrequency: 'weekly' as const,
      priority: route === '' ? 1.0 : 0.8,
    }))
    return routes
  }
}
