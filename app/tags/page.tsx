import Link from '@/components/Link'
import PageHeader from '@/components/PageHeader'
import { slug } from 'github-slugger'
import { genPageMetadata } from 'app/seo'
import { getCombinedPosts } from '../../lib/combined-posts'
import { 
  Target,
  ArrowRight
} from 'lucide-react'

// Force dynamic rendering to ensure latest posts are always fetched
export const dynamic = 'force-dynamic'

export const metadata = genPageMetadata({
  title: 'Tags',
  description: 'Explore topics and technologies I blog about',
})

// Get tag counts from posts
async function getTags(): Promise<{ [key: string]: number }> {
  try {
    const posts = await getCombinedPosts()
    const tagCounts: { [key: string]: number } = {}
    
    posts.forEach((post) => {
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach((tag: string) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      }
    })
    
    return tagCounts
  } catch (error) {
    console.error('Error fetching tags:', error)
    return {}
  }
}

export default async function Page() {
  const tagCounts = await getTags()
  const tagKeys = Object.keys(tagCounts)
  const sortedTags = tagKeys.sort((a, b) => tagCounts[b] - tagCounts[a])
  const totalTags = tagKeys.length
  const totalPosts = Object.values(tagCounts).reduce((sum: number, count: number) => sum + count, 0)

  return (
    <>
      {/* Clean Header */}
      <PageHeader
        title="Topics & Tags"
        description={`Explore ${totalTags} topics across ${totalPosts} articles`}
        icon={<Target className="w-4 h-4" />}
        badge="Tags"
      />

      {/* Main Content */}
      <div className="bg-slate-50 dark:bg-slate-900 min-h-screen">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {totalTags === 0 ? (
            /* No Tags */
            <div className="text-center py-20">
              <div className="bg-white dark:bg-slate-800 rounded-xl p-12 shadow-sm border border-slate-200 dark:border-slate-700">
                <div className="text-6xl mb-6">🏷️</div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                  No Tags Yet
                </h3>
                <p className="text-slate-600 dark:text-slate-400 mb-8">
                  Tags will appear here as I publish more content.
                </p>
                <Link
                  href="/blog"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-slate-600 hover:bg-slate-700 text-white font-medium rounded-lg transition-colors"
                >
                  Browse Articles
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          ) : (
            /* Tags Grid */
            <div className="space-y-8">
              {/* Simple Stats - No colors */}
              <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-slate-900 dark:text-white">{totalTags}</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Topics</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-slate-900 dark:text-white">{totalPosts}</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Articles</div>
                  </div>
                </div>
              </div>

              {/* All Tags - Simple Grid */}
              <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-sm border border-slate-200 dark:border-slate-700">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">All Topics</h2>
                <div className="flex flex-wrap gap-3">
                  {sortedTags.map((tag) => (
                    <Link
                      key={tag}
                      href={`/tags/${slug(tag)}`}
                      className="group"
                    >
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white rounded-lg transition-colors">
                        <span className="font-medium">{tag}</span>
                        <span className="text-xs bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded-full">
                          {tagCounts[tag]}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
