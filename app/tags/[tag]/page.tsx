import { slug } from 'github-slugger'
import { allCoreContent, sortPosts } from 'pliny/utils/contentlayer'
import siteMetadata from '@/data/siteMetadata'
import ListLayout from '@/layouts/ListLayoutWithTags'
import { allBlogs } from 'contentlayer/generated'
import { genPageMetadata } from 'app/seo'
import { Metadata } from 'next'
import { getCombinedPosts } from '../../../lib/combined-posts'

const POSTS_PER_PAGE = 5

// Force dynamic rendering to ensure latest posts are always fetched
export const dynamic = 'force-dynamic'

export async function generateMetadata(props: {
  params: Promise<{ tag: string }>
}): Promise<Metadata> {
  const params = await props.params
  const tag = decodeURI(params.tag)
  return genPageMetadata({
    title: tag,
    description: `${siteMetadata.title} ${tag} tagged content`,
    alternates: {
      canonical: './',
      types: {
        'application/rss+xml': `${siteMetadata.siteUrl}/tags/${tag}/feed.xml`,
      },
    },
  })
}

export default async function TagPage(props: { params: Promise<{ tag: string }> }) {
  const params = await props.params
  const tag = decodeURI(params.tag)

  // Get combined posts and filter by tag
  const combinedPosts = await getCombinedPosts()
  const filteredCombinedPosts = combinedPosts.filter(
    (post) => post.tags && post.tags.map((t) => slug(t)).includes(tag)
  )

  // Find the original tag name (with proper casing) from the posts
  let originalTagName = tag
  for (const post of filteredCombinedPosts) {
    if (post.tags) {
      const foundTag = post.tags.find((t) => slug(t) === tag)
      if (foundTag) {
        originalTagName = foundTag
        break
      }
    }
  }

  // Convert to format expected by ListLayout (using template format)
  const filteredPosts = filteredCombinedPosts.map((post) => ({
    slug: post.slug,
    title: post.title,
    date: post.date,
    tags: post.tags,
    summary: post.summary || post.description,
    path: post.path,
    authors: post.author ? [post.author] : ['default'],
    draft: post.status !== 'published',
  }))

  const totalPages = Math.ceil(filteredPosts.length / POSTS_PER_PAGE)
  const initialDisplayPosts = filteredPosts.slice(0, POSTS_PER_PAGE)
  const pagination = {
    currentPage: 1,
    totalPages: totalPages,
  }

  // Enhanced title with context
  const title = originalTagName
  const tagInfo = {
    name: originalTagName,
    slug: tag,
    count: filteredPosts.length,
  }

  return (
    <ListLayout
      posts={filteredPosts}
      initialDisplayPosts={initialDisplayPosts}
      pagination={pagination}
      title={title}
      tagInfo={tagInfo}
    />
  )
}
