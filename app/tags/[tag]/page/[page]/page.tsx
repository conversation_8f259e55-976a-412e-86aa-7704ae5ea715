import { slug } from 'github-slugger'
import { allCoreContent, sortPosts } from 'pliny/utils/contentlayer'
import siteMetadata from '@/data/siteMetadata'
import ListLayout from '@/layouts/ListLayoutWithTags'
import { allBlogs } from 'contentlayer/generated'
import { notFound } from 'next/navigation'
import { getCombinedPosts } from '../../../../../lib/combined-posts'

const POSTS_PER_PAGE = 5

// Force dynamic rendering to ensure latest posts are always fetched
export const dynamic = 'force-dynamic'

export default async function Page(props: { params: Promise<{ tag: string; page: string }> }) {
  const params = await props.params
  const tag = decodeURI(params.tag)
  const pageNumber = parseInt(params.page)

  // Get combined posts and filter by tag
  const combinedPosts = await getCombinedPosts()
  const filteredCombinedPosts = combinedPosts.filter(
    (post) => post.tags && post.tags.map((t) => slug(t)).includes(tag)
  )

  // Find the original tag name (with proper casing) from the posts
  let originalTagName = tag
  for (const post of filteredCombinedPosts) {
    if (post.tags) {
      const foundTag = post.tags.find((t) => slug(t) === tag)
      if (foundTag) {
        originalTagName = foundTag
        break
      }
    }
  }

  // Convert to format expected by ListLayout (using template format)
  const filteredPosts = filteredCombinedPosts.map((post) => ({
    slug: post.slug,
    title: post.title,
    date: post.date,
    tags: post.tags,
    summary: post.summary || post.description,
    path: post.path,
    authors: post.author ? [post.author] : ['default'],
    draft: post.status !== 'published',
  }))

  const totalPages = Math.ceil(filteredPosts.length / POSTS_PER_PAGE)

  // Return 404 for invalid page numbers or empty pages
  if (pageNumber <= 0 || pageNumber > totalPages || isNaN(pageNumber)) {
    return notFound()
  }
  const initialDisplayPosts = filteredPosts.slice(
    POSTS_PER_PAGE * (pageNumber - 1),
    POSTS_PER_PAGE * pageNumber
  )
  const pagination = {
    currentPage: pageNumber,
    totalPages: totalPages,
  }

  // Enhanced title with context
  const title = originalTagName
  const tagInfo = {
    name: originalTagName,
    slug: tag,
    count: filteredPosts.length,
  }

  return (
    <ListLayout
      posts={filteredPosts}
      initialDisplayPosts={initialDisplayPosts}
      pagination={pagination}
      title={title}
      tagInfo={tagInfo}
    />
  )
}
