'use client'

import ContactForm from '@/components/ContactForm'
import PageHeader from '@/components/PageHeader'
import { Mail, MessageSquare, Clock, MapPin, Phone, Send } from 'lucide-react'

export default function ContactPage() {
  return (
    <>
      {/* Clean Hero Section */}
      <PageHeader
        title="Get in Touch"
        description="Have a question, feedback, or just want to connect? I'd love to hear from you."
        icon={<Mail className="w-4 h-4" />}
        badge="Contact"
      />

      {/* Contact Section */}
      <div className="bg-slate-50 dark:bg-slate-800/50 py-16 lg:py-24">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Info */}
            <div className="space-y-8">
              <div>
                <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">
                  Let's Start a Conversation
                </h2>
                <p className="text-lg text-slate-600 dark:text-slate-400 leading-relaxed">
                  Whether you have a question about technology, want to discuss a project, 
                  or just want to say hello, I'm always excited to connect with fellow tech enthusiasts.
                </p>
              </div>

              {/* Contact Methods */}
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-1">Email</h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      Drop me a line and I'll get back to you within 24 hours.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-1">Response Time</h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      I typically respond to messages within 24 hours on weekdays.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center flex-shrink-0">
                    <MessageSquare className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-1">Topics</h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      Tech reviews, tutorials, collaborations, or just general tech chat.
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Contact */}
              <div className="bg-white dark:bg-slate-900 rounded-2xl p-6 shadow-sm border border-slate-200 dark:border-slate-800">
                <h3 className="font-semibold text-slate-900 dark:text-white mb-4">
                  Prefer Social Media?
                </h3>
                <p className="text-slate-600 dark:text-slate-400 mb-4">
                  You can also reach out to me on social platforms for quick questions or discussions.
                </p>
                <div className="flex gap-3">
                  <a href="#" className="flex-1 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300 py-2 px-4 rounded-lg text-center transition-colors">
                    Twitter
                  </a>
                  <a href="#" className="flex-1 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300 py-2 px-4 rounded-lg text-center transition-colors">
                    LinkedIn
                  </a>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 shadow-sm border border-slate-200 dark:border-slate-800">
              <div className="mb-8">
                <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium mb-4">
                  <Send className="w-4 h-4" />
                  Send Message
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">
                  Send Me a Message
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  Fill out the form below and I'll get back to you as soon as possible.
                </p>
              </div>

              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
