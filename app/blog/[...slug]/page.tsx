import 'css/prism.css'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import siteMetadata from '@/data/siteMetadata'
import { getCombinedPostBySlug } from '../../../lib/combined-posts'
import SupabasePostRenderer from '@/components/SupabasePostRenderer'

// Force dynamic rendering to ensure latest posts are always fetched
export const dynamic = 'force-dynamic'

export async function generateMetadata(props: {
  params: Promise<{ slug: string[] }>
}): Promise<Metadata | undefined> {
  const params = await props.params
  const slug = decodeURI(params.slug.join('/'))

  const post = await getCombinedPostBySlug(slug)
  if (!post) {
    return
  }

  return {
    title: post.title,
    description: post.summary || post.description,
    openGraph: {
      title: post.title,
      description: post.summary || post.description,
      siteName: siteMetadata.title,
      locale: 'en_US',
      type: 'article',
      publishedTime: new Date(post.date).toISOString(),
      url: './',
      authors: [post.author || siteMetadata.author],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.summary || post.description,
    },
  }
}

export default async function Page(props: { params: Promise<{ slug: string[] }> }) {
  const params = await props.params
  const slug = decodeURI(params.slug.join('/'))

  const post = await getCombinedPostBySlug(slug)

  if (!post) {
    return notFound()
  }

  return <SupabasePostRenderer post={post} />
}
