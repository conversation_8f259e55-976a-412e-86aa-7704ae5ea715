import 'css/prism.css'
import { notFound } from 'next/navigation'
import { getCombinedPostBySlugAdmin } from '@/lib/combined-posts'
import SupabasePostRenderer from '@/components/SupabasePostRenderer'

// Preview Banner Component
function PreviewBanner() {
  return (
    <div className="sticky top-0 z-50 bg-gradient-to-r from-amber-500 to-orange-500 px-4 py-2 text-center text-sm font-medium text-white shadow-lg">
      <div className="flex items-center justify-center space-x-2">
        <div className="flex h-2 w-2 animate-pulse rounded-full bg-white"></div>
        <span>PREVIEW MODE - This post is not published yet</span>
        <div className="flex h-2 w-2 animate-pulse rounded-full bg-white"></div>
      </div>
    </div>
  )
}

export default async function PreviewPage({ 
  params 
}: { 
  params: Promise<{ slug: string }> 
}) {
  const { slug } = await params
  
  // TODO: Add proper admin session check here
  // For now, we'll allow preview access but show a warning banner
  // if (!isAdmin()) return notFound()

  try {
    const post = await getCombinedPostBySlugAdmin(slug)
    
    if (!post) {
      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <PreviewBanner />
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Post Not Found
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                The post you're trying to preview doesn't exist or has been removed.
              </p>
              <a
                href="/admin"
                className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
              >
                Back to Admin
              </a>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PreviewBanner />
        <SupabasePostRenderer post={post} />
      </div>
    )
  } catch (error) {
    console.error('Error loading preview:', error)
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <PreviewBanner />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Preview Error
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              There was an error loading the preview. Please try again.
            </p>
            <a
              href="/admin"
              className="inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
            >
              Back to Admin
            </a>
          </div>
        </div>
      </div>
    )
  }
} 