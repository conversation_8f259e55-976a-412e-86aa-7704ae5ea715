import { NextResponse } from 'next/server'
import { getBlogPosts } from '../../../lib/supabase'

export async function GET() {
  try {
    const tagCounts: { [key: string]: number } = {}

    // Add tags from Supabase posts (only published ones)
    try {
      const supabasePosts = await getBlogPosts()
      const publishedPosts = supabasePosts.filter((post) => post.status === 'published')

      publishedPosts.forEach((post) => {
        if (post.tags && post.tags.length > 0) {
          post.tags.forEach((tag: string) => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1
          })
        }
      })
    } catch (supabaseError) {
      console.error('Error fetching Supabase posts:', supabaseError)
      return NextResponse.json({}, { status: 500 })
    }

    const response = NextResponse.json(tagCounts)

    // Add caching headers for better performance (tags don't change frequently)
    response.headers.set('Cache-Control', 's-maxage=300, stale-while-revalidate=600')

    return response
  } catch (error) {
    console.error('Error fetching tags:', error)
    return NextResponse.json({}, { status: 500 })
  }
}
