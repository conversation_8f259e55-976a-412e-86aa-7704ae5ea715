import { NextRequest, NextResponse } from 'next/server'
import { getProjects, createProject, updateProject } from '../../../lib/supabase'

export async function GET() {
  try {
    const projects = await getProjects()

    const response = NextResponse.json(projects)

    // Reduce cache time for faster updates when admin changes status
    response.headers.set('Cache-Control', 's-maxage=10, stale-while-revalidate=30')

    return response
  } catch (error) {
    console.error('Error fetching projects:', error)
    // Return empty array if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json([])
    }
    return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, href, imgSrc, status, featured, technologies, order_index } = body

    if (!title || !description) {
      return NextResponse.json({ error: 'Title and description are required' }, { status: 400 })
    }

    const project = await createProject({
      title,
      description,
      href: href || null,
      imgSrc: imgSrc || null,
      status: status || 'active',
      featured: featured || false,
      technologies: technologies || [],
      order_index: order_index || 0,
    })

    return NextResponse.json(project, { status: 201 })
  } catch (error) {
    console.error('Error creating project:', error)
    return NextResponse.json({ error: 'Failed to create project' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updates } = body

    if (!id) {
      return NextResponse.json({ error: 'Project ID is required' }, { status: 400 })
    }

    const project = await updateProject(id, updates)
    return NextResponse.json(project)
  } catch (error) {
    console.error('Error updating project:', error)
    return NextResponse.json({ error: 'Failed to update project' }, { status: 500 })
  }
}
