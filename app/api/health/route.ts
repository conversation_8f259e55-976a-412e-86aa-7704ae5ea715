import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Simple health check - you can add more checks here if needed
    // For example: database connectivity, external services, etc.
    
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'Tip & Trick Guru Blog',
      version: process.env.npm_package_version || '1.0.0'
    }

    return NextResponse.json(healthCheck, { status: 200 })
  } catch (error) {
    console.error('Health check failed:', error)
    
    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Service health check failed'
    }

    return NextResponse.json(errorResponse, { status: 503 })
  }
} 