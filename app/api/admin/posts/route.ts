import { NextResponse } from 'next/server'
import { getBlogPosts } from '../../../../lib/supabase'

export async function GET() {
  try {
    // Admin API should return ALL posts regardless of status
    const posts = await getBlogPosts()

    const response = NextResponse.json(posts)

    // Shorter cache for admin to see changes immediately
    response.headers.set('Cache-Control', 's-maxage=5, stale-while-revalidate=10')

    return response
  } catch (error) {
    console.error('Error fetching admin posts:', error)
    // Return empty array if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json([])
    }
    return NextResponse.json({ error: 'Failed to fetch admin posts' }, { status: 500 })
  }
} 