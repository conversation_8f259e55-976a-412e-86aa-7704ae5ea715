import { NextResponse } from 'next/server'
import { getProjects } from '../../../../lib/supabase'

export async function GET() {
  try {
    // Admin API should return ALL projects regardless of status
    const projects = await getProjects()

    const response = NextResponse.json(projects)

    // Shorter cache for admin to see changes immediately
    response.headers.set('Cache-Control', 's-maxage=5, stale-while-revalidate=10')

    return response
  } catch (error) {
    console.error('Error fetching admin projects:', error)
    // Return empty array if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json([])
    }
    return NextResponse.json({ error: 'Failed to fetch admin projects' }, { status: 500 })
  }
} 