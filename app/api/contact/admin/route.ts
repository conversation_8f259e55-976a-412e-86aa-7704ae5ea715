import { NextRequest, NextResponse } from 'next/server'
import {
  getContactMessages,
  updateContactMessageStatus,
  deleteContactMessage,
} from '../../../../lib/supabase'

// GET /api/contact/admin - Fetch all contact messages
export async function GET() {
  try {
    const startTime = Date.now()
    const messages = await getContactMessages()
    const duration = Date.now() - startTime

    console.log(`Contact messages fetched in ${duration}ms`)
    return NextResponse.json({
      success: true,
      data: messages,
      meta: { count: messages.length, duration },
    })
  } catch (error) {
    console.error('Error fetching contact messages:', error)

    // More specific error handling
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const isTimeout = errorMessage.includes('timeout') || errorMessage.includes('AbortError')

    return NextResponse.json(
      {
        success: false,
        error: isTimeout
          ? 'Database connection timeout. Please try again.'
          : 'Failed to fetch contact messages',
        details: errorMessage,
      },
      { status: isTimeout ? 408 : 500 }
    )
  }
}

// PATCH /api/contact/admin - Update message status
export async function PATCH(request: NextRequest) {
  try {
    const { id, status } = await request.json()

    if (!id || !status) {
      return NextResponse.json(
        { success: false, error: 'Message ID and status are required' },
        { status: 400 }
      )
    }

    if (!['new', 'read', 'replied'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status. Must be new, read, or replied' },
        { status: 400 }
      )
    }

    await updateContactMessageStatus(id, status)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating contact message status:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update contact message status' },
      { status: 500 }
    )
  }
}

// DELETE /api/contact/admin - Delete a message
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ success: false, error: 'Message ID is required' }, { status: 400 })
    }

    await deleteContactMessage(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting contact message:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete contact message' },
      { status: 500 }
    )
  }
}
