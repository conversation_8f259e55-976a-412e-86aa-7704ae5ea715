import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { sendEmail, generateContactNotificationEmail } from '../../../lib/email'

// Rate limiting map (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, subject, message } = body

    // Basic validation
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      )
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }

    // Length validation
    if (name.length > 100 || subject.length > 200 || message.length > 2000) {
      return NextResponse.json(
        { success: false, error: 'Message content is too long' },
        { status: 400 }
      )
    }

    // Get client IP for rate limiting
    const clientIp =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'

    // Rate limiting (5 messages per hour per IP)
    const now = Date.now()
    const hourInMs = 60 * 60 * 1000
    const rateLimitKey = `contact:${clientIp}`
    const rateLimit = rateLimitMap.get(rateLimitKey)

    if (rateLimit) {
      if (now < rateLimit.resetTime) {
        if (rateLimit.count >= 5) {
          return NextResponse.json(
            { success: false, error: 'Rate limit exceeded. Please try again later.' },
            { status: 429 }
          )
        }
        rateLimit.count++
      } else {
        rateLimitMap.set(rateLimitKey, { count: 1, resetTime: now + hourInMs })
      }
    } else {
      rateLimitMap.set(rateLimitKey, { count: 1, resetTime: now + hourInMs })
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey =
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase configuration')
      // Fallback to logging if Supabase is not configured
      console.log('New contact message (no DB):', {
        name,
        email,
        subject,
        message,
        timestamp: new Date().toISOString(),
        ip: clientIp,
      })

      return NextResponse.json({
        success: true,
        message: "Message sent successfully! I'll get back to you soon.",
      })
    }

    const supabase = createClient(supabaseUrl, supabaseKey)

    // Save message to database
    const { data, error } = await supabase
      .from('contact_messages')
      .insert({
        name: name.trim(),
        email: email.trim().toLowerCase(),
        subject: subject.trim(),
        message: message.trim(),
        ip_address: clientIp,
        user_agent: request.headers.get('user-agent') || null,
      })
      .select()

    if (error) {
      console.error('Error saving contact message:', error)
      // Fallback to logging if database insert fails
      console.log('New contact message (DB error):', {
        name,
        email,
        subject,
        message,
        timestamp: new Date().toISOString(),
        ip: clientIp,
        error: error.message,
      })
    } else {
      console.log('Contact message saved to database:', data?.[0]?.id)
    }

    // Send email notification to admin
    const adminEmail = process.env.ADMIN_CONTACT_EMAIL
    console.log('🔍 Admin email configuration:', adminEmail ? `${adminEmail}` : 'NOT SET')

    if (adminEmail) {
      try {
        console.log('📧 Generating contact notification email...')
        const { html: notificationHtml, text: notificationText } = generateContactNotificationEmail(
          name.trim(),
          email.trim(),
          subject.trim(),
          message.trim(),
          clientIp
        )
        console.log('✅ Email template generated successfully')

        // Fire and forget - don't wait for email to send
        console.log('📤 Sending email notification...')
        sendEmail({
          to: adminEmail,
          subject: `New Contact Message: ${subject.trim()}`,
          html: notificationHtml,
          text: notificationText,
        })
          .then((success) => {
            if (success) {
              console.log('✅ Contact notification email sent successfully to:', adminEmail)
            } else {
              console.log('❌ Failed to send contact notification email to:', adminEmail)
            }
          })
          .catch((error) => {
            console.error('❌ Failed to send contact notification email:', error)
          })

        console.log('📬 Contact notification email queued for:', adminEmail)
      } catch (emailError) {
        console.error('❌ Error preparing contact notification email:', emailError)
        // Don't fail the request if email fails
      }
    } else {
      console.log('⚠️  ADMIN_CONTACT_EMAIL not configured - skipping email notification')
    }

    // Return success response regardless of database status
    return NextResponse.json({
      success: true,
      message: "Message sent successfully! I'll get back to you soon.",
    })
  } catch (error) {
    console.error('Error processing contact form:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send message. Please try again later.' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 })
}
