import { NextRequest, NextResponse } from 'next/server'
import { createBlogPost, getBlogPosts, getBlogPostsPublished, updateBlogPost } from '../../../lib/supabase'

export async function GET() {
  try {
    // Use getBlogPostsPublished to only return published posts for frontend
    const posts = await getBlogPostsPublished()

    const response = NextResponse.json(posts)

    // Reduce cache time for faster updates when admin changes status
    response.headers.set('Cache-Control', 's-maxage=10, stale-while-revalidate=30')

    return response
  } catch (error) {
    console.error('Error fetching posts:', error)
    // Return empty array if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json([])
    }
    return NextResponse.json({ error: 'Failed to fetch posts' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const {
      title,
      slug: providedSlug,
      content,
      description,
      author,
      tags,
      readTime,
      status,
      featured,
      showAffiliateDisclosure,
    } = body

    // Debug logging
    console.log('API POST - showAffiliateDisclosure received:', showAffiliateDisclosure)

    if (!title || !content || !description || !author) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Generate base slug from provided slug or title
    const baseSlug =
      providedSlug ||
      title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '')

    // Check for existing slugs and make unique
    let slug = baseSlug
    let counter = 1
    const existingPosts = await getBlogPosts()

    while (existingPosts.some((post) => post.slug === slug)) {
      slug = `${baseSlug}-${counter}`
      counter++
    }

    const currentDate = new Date().toISOString().split('T')[0]

    const postData = {
      slug,
      title,
      content,
      description,
      author,
      date: currentDate,
      tags: Array.isArray(tags)
        ? tags
        : tags
            ?.split(',')
            .map((t: string) => t.trim())
            .filter(Boolean) || [],
      readTime: readTime || '5 min read',
      status: status || 'draft',
      featured: featured || false,
      showAffiliateDisclosure: showAffiliateDisclosure || false,
    }

    const newPost = await createBlogPost(postData)
    return NextResponse.json(newPost)
  } catch (error) {
    console.error('Error creating post:', error)
    // Return helpful message if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json(
        {
          error:
            'Supabase not configured. Please add your Supabase credentials to .env.local to enable post creation.',
          details:
            'Add NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY to your .env.local file',
        },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Failed to create post' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    const {
      id,
      title,
      slug,
      content,
      description,
      author,
      tags,
      readTime,
      status,
      featured,
      showAffiliateDisclosure,
    } = body

    if (!id) {
      return NextResponse.json({ error: 'Post ID is required for updates' }, { status: 400 })
    }

    if (!title || !content || !description || !author) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // For updates, if slug is changed, check for conflicts (excluding current post)
    if (slug) {
      const existingPosts = await getBlogPosts()
      const conflictingPost = existingPosts.find((post) => post.slug === slug && post.id !== id)

      if (conflictingPost) {
        return NextResponse.json({ error: 'A post with this slug already exists' }, { status: 400 })
      }
    }

    const postData = {
      slug,
      title,
      content,
      description,
      author,
      tags: Array.isArray(tags)
        ? tags
        : tags
            ?.split(',')
            .map((t: string) => t.trim())
            .filter(Boolean) || [],
      readTime: readTime || '5 min read',
      status: status || 'draft',
      featured: featured || false,
      showAffiliateDisclosure: showAffiliateDisclosure || false,
    }

    const updatedPost = await updateBlogPost(id, postData)
    return NextResponse.json(updatedPost)
  } catch (error) {
    console.error('Error updating post:', error)
    // Return helpful message if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json(
        {
          error:
            'Supabase not configured. Please add your Supabase credentials to .env.local to enable post updates.',
          details:
            'Add NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY to your .env.local file',
        },
        { status: 400 }
      )
    }
    return NextResponse.json({ error: 'Failed to update post' }, { status: 500 })
  }
}
