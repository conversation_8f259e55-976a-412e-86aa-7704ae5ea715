import { NextRequest, NextResponse } from 'next/server'
import { sendEmail, generateContactNotificationEmail } from '../../../lib/email'

export async function POST(request: NextRequest) {
  try {
    const adminEmail = process.env.ADMIN_CONTACT_EMAIL

    if (!adminEmail) {
      return NextResponse.json(
        {
          success: false,
          error: 'ADMIN_CONTACT_EMAIL not configured',
        },
        { status: 400 }
      )
    }

    console.log('🧪 Testing email functionality...')
    console.log('📧 Admin email:', adminEmail)
    console.log('📤 Sending test email...')

    // Generate test contact notification
    const { html, text } = generateContactNotificationEmail(
      'Test User',
      '<EMAIL>',
      'Email Test - Debug',
      'This is a test email to debug the contact form notification system. If you receive this email, the system is working correctly.',
      '127.0.0.1'
    )

    // Send test email
    const success = await sendEmail({
      to: adminEmail,
      subject: '🧪 Test: Contact Form Email Notification',
      html,
      text,
    })

    if (success) {
      return NextResponse.json({
        success: true,
        message: `Test email sent successfully to ${adminEmail}`,
        timestamp: new Date().toISOString(),
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send test email - check server logs for details',
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('❌ Test email error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Unknown error occurred',
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    info: 'POST to this endpoint to send a test email',
    adminEmail: process.env.ADMIN_CONTACT_EMAIL || 'NOT SET',
    mailtrapConfigured: !!(process.env.MAILTRAP_API_KEY && process.env.MAILTRAP_FROM_EMAIL),
  })
}
