import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { platform, productName, href, timestamp } = await request.json()

    // Here you could save to database or external analytics service
    // For now, we'll just log it (in production, you'd want proper storage)
    console.log('Affiliate click tracked:', {
      platform,
      productName,
      href,
      timestamp,
      userAgent: request.headers.get('user-agent'),
      referer: request.headers.get('referer'),
    })

    // You could also send to external analytics services like:
    // - Google Analytics 4
    // - Mixpanel
    // - PostHog
    // - Custom database table

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error tracking affiliate click:', error)
    return NextResponse.json({ error: 'Failed to track click' }, { status: 500 })
  }
}
