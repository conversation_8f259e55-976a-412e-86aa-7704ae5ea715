import { NextRequest, NextResponse } from 'next/server'
import {
  getBlogPosts,
  getAllComments,
  getContactMessages,
  Comment,
  ContactMessage,
} from '../../../lib/supabase'

export async function GET() {
  try {
    const posts = await getBlogPosts()
    let comments: Comment[] = []
    let contactMessages: ContactMessage[] = []

    try {
      comments = await getAllComments()
    } catch (error) {
      // Comments not available, continue with empty array
      if (process.env.NODE_ENV === 'development') {
        console.log('Comments not available:', error)
      }
    }

    try {
      contactMessages = await getContactMessages()
    } catch (error) {
      // Contact messages not available, continue with empty array
      if (process.env.NODE_ENV === 'development') {
        console.log('Contact messages not available:', error)
      }
    }

    // Calculate basic statistics
    const totalPosts = posts.length
    const publishedPosts = posts.filter((post) => post.status === 'published')
    const draftPosts = posts.filter((post) => post.status === 'draft')
    const disabledPosts = posts.filter((post) => post.status === 'disabled')

    // Calculate view statistics
    const totalViews = posts.reduce((sum, post) => sum + (post.view_count || 0), 0)
    const avgViews = totalPosts > 0 ? Math.round(totalViews / totalPosts) : 0
    const avgViewsPublished =
      publishedPosts.length > 0
        ? Math.round(
            publishedPosts.reduce((sum, post) => sum + (post.view_count || 0), 0) /
              publishedPosts.length
          )
        : 0

    // Top performing posts
    const topPosts = posts
      .filter((post) => post.status === 'published')
      .sort((a, b) => (b.view_count || 0) - (a.view_count || 0))
      .slice(0, 10)
      .map((post) => ({
        id: post.id,
        title: post.title,
        slug: post.slug,
        views: post.view_count || 0,
        date: post.date,
        tags: post.tags,
      }))

    // Posts by status
    const postsByStatus = {
      published: publishedPosts.length,
      draft: draftPosts.length,
      disabled: disabledPosts.length,
    }

    // Recent activity (posts created in last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentPosts = posts.filter((post) => {
      const postDate = new Date(post.created_at || post.date)
      return postDate >= thirtyDaysAgo
    })

    // Views by month (last 6 months of published posts)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)

    const viewsByMonth: { [key: string]: number } = {}
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ]

    // Initialize last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`
      viewsByMonth[key] = 0
    }

    // Aggregate views by month for published posts
    publishedPosts.forEach((post) => {
      const postDate = new Date(post.date)
      if (postDate >= sixMonthsAgo) {
        const key = `${monthNames[postDate.getMonth()]} ${postDate.getFullYear()}`
        if (viewsByMonth[key] !== undefined) {
          viewsByMonth[key] += post.view_count || 0
        }
      }
    })

    // Tag popularity
    const tagCounts: { [key: string]: number } = {}
    posts.forEach((post) => {
      if (post.tags && post.tags.length > 0) {
        post.tags.forEach((tag: string) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1
        })
      }
    })

    const popularTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count: count as number }))

    // Comment analytics (handle case where comments might be empty array)
    const totalComments = comments?.length || 0
    const approvedComments = comments?.filter((comment) => comment.status === 'approved') || []
    const pendingComments = comments?.filter((comment) => comment.status === 'pending') || []
    const rejectedComments = comments?.filter((comment) => comment.status === 'rejected') || []

    const commentsByStatus = {
      approved: approvedComments.length,
      pending: pendingComments.length,
      rejected: rejectedComments.length,
    }

    // Average comments per post
    const avgCommentsPerPost =
      publishedPosts.length > 0
        ? Math.round((approvedComments.length / publishedPosts.length) * 100) / 100
        : 0

    // Comment approval rate
    const commentApprovalRate =
      totalComments > 0 ? ((approvedComments.length / totalComments) * 100).toFixed(1) : '0'

    // Most commented posts
    const postCommentCounts: { [slug: string]: { count: number; title: string; id: string } } = {}

    approvedComments.forEach((comment) => {
      const post = posts.find((p) => p.slug === comment.post_slug)
      if (post && post.status === 'published') {
        if (!postCommentCounts[comment.post_slug]) {
          postCommentCounts[comment.post_slug] = { count: 0, title: post.title, id: post.id! }
        }
        postCommentCounts[comment.post_slug].count++
      }
    })

    const mostCommentedPosts = Object.entries(postCommentCounts)
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([slug, data]) => ({
        id: data.id,
        title: data.title,
        slug,
        comments: data.count,
      }))

    // Comments by month (last 6 months)
    const commentsByMonth: { [key: string]: number } = {}

    // Initialize last 6 months for comments
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`
      commentsByMonth[key] = 0
    }

    // Aggregate comments by month
    comments?.forEach((comment) => {
      const commentDate = new Date(comment.created_at || new Date())
      if (commentDate >= sixMonthsAgo) {
        const key = `${monthNames[commentDate.getMonth()]} ${commentDate.getFullYear()}`
        if (commentsByMonth[key] !== undefined) {
          commentsByMonth[key]++
        }
      }
    })

    // Top commenters (by approved comments)
    const commenterCounts: { [name: string]: { count: number; email: string } } = {}

    approvedComments.forEach((comment) => {
      if (!commenterCounts[comment.author_name]) {
        commenterCounts[comment.author_name] = { count: 0, email: comment.author_email }
      }
      commenterCounts[comment.author_name].count++
    })

    const topCommenters = Object.entries(commenterCounts)
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([name, data]) => ({
        name,
        email: data.email,
        comments: data.count,
      }))

    // Recent comments (last 30 days)
    const recentComments =
      comments?.filter((comment) => {
        const commentDate = new Date(comment.created_at || new Date())
        return commentDate >= thirtyDaysAgo
      })?.length || 0

    // Contact message analytics
    const totalContactMessages = contactMessages?.length || 0
    const newContactMessages = contactMessages?.filter((msg) => msg.status === 'new')?.length || 0
    const readContactMessages = contactMessages?.filter((msg) => msg.status === 'read')?.length || 0
    const repliedContactMessages =
      contactMessages?.filter((msg) => msg.status === 'replied')?.length || 0

    const recentContactMessages =
      contactMessages?.filter((msg) => {
        const msgDate = new Date(msg.created_at || new Date())
        return msgDate >= thirtyDaysAgo
      })?.length || 0

    // Contact messages by month (last 6 months)
    const contactMessagesByMonth: { [key: string]: number } = {}

    // Initialize last 6 months for contact messages
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const key = `${monthNames[date.getMonth()]} ${date.getFullYear()}`
      contactMessagesByMonth[key] = 0
    }

    // Aggregate contact messages by month
    contactMessages?.forEach((msg) => {
      const msgDate = new Date(msg.created_at || new Date())
      if (msgDate >= sixMonthsAgo) {
        const key = `${monthNames[msgDate.getMonth()]} ${msgDate.getFullYear()}`
        if (contactMessagesByMonth[key] !== undefined) {
          contactMessagesByMonth[key]++
        }
      }
    })

    // Performance metrics
    const performanceMetrics = {
      postsWithViews: posts.filter((post) => (post.view_count || 0) > 0).length,
      postsWithoutViews: posts.filter((post) => (post.view_count || 0) === 0).length,
      highPerformingPosts: posts.filter((post) => (post.view_count || 0) > avgViews).length,
      engagementRate:
        totalPosts > 0
          ? (
              (posts.filter((post) => (post.view_count || 0) > 0).length / totalPosts) *
              100
            ).toFixed(1)
          : 0,
    }

    return NextResponse.json({
      overview: {
        totalPosts,
        totalViews,
        avgViews,
        avgViewsPublished,
        publishedPosts: publishedPosts.length,
        draftPosts: draftPosts.length,
        recentPosts: recentPosts.length,
        totalComments,
        recentComments,
        avgCommentsPerPost,
        commentApprovalRate,
        totalContactMessages,
        newContactMessages,
        recentContactMessages,
      },
      postsByStatus,
      topPosts,
      viewsByMonth: Object.entries(viewsByMonth).map(([month, views]) => ({
        month,
        views,
      })),
      popularTags,
      performanceMetrics,
      recentActivity: recentPosts.slice(0, 5).map((post) => ({
        id: post.id,
        title: post.title,
        status: post.status,
        date: post.created_at || post.date,
        views: post.view_count || 0,
      })),
      commentAnalytics: {
        commentsByStatus,
        mostCommentedPosts,
        commentsByMonth: Object.entries(commentsByMonth).map(([month, comments]) => ({
          month,
          comments,
        })),
        topCommenters,
      },
      contactAnalytics: {
        totalContactMessages,
        newContactMessages,
        readContactMessages,
        repliedContactMessages,
        recentContactMessages,
        contactMessagesByMonth: Object.entries(contactMessagesByMonth).map(([month, contacts]) => ({
          month,
          contacts,
        })),
      },
    })
  } catch (error) {
    console.error('Error fetching analytics:', error)
    // Return empty analytics if Supabase is not configured
    if (error instanceof Error && error.message.includes('Supabase is not configured')) {
      return NextResponse.json({
        overview: {
          totalPosts: 0,
          totalViews: 0,
          avgViews: 0,
          avgViewsPublished: 0,
          publishedPosts: 0,
          draftPosts: 0,
          recentPosts: 0,
          totalComments: 0,
          recentComments: 0,
          avgCommentsPerPost: 0,
          commentApprovalRate: '0',
          totalContactMessages: 0,
          newContactMessages: 0,
          recentContactMessages: 0,
        },
        postsByStatus: { published: 0, draft: 0, disabled: 0 },
        topPosts: [],
        viewsByMonth: [],
        popularTags: [],
        performanceMetrics: {
          postsWithViews: 0,
          postsWithoutViews: 0,
          highPerformingPosts: 0,
          engagementRate: 0,
        },
        recentActivity: [],
        commentAnalytics: {
          commentsByStatus: { approved: 0, pending: 0, rejected: 0 },
          mostCommentedPosts: [],
          commentsByMonth: [],
          topCommenters: [],
        },
        contactAnalytics: {
          totalContactMessages: 0,
          newContactMessages: 0,
          readContactMessages: 0,
          repliedContactMessages: 0,
          recentContactMessages: 0,
          contactMessagesByMonth: [],
        },
      })
    }
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 })
  }
}
