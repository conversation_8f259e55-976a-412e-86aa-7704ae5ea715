import { NextRequest, NextResponse } from 'next/server'
import { confirmNewsletterSubscription } from '@/lib/supabase'
import { sendEmail, generateWelcomeEmail } from '@/lib/email'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token = searchParams.get('token')

  if (!token) {
    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invalid Confirmation Link</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; text-align: center; padding: 40px; background: #f8fafc; }
          .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          h1 { color: #e53e3e; margin-bottom: 20px; }
          p { color: #4a5568; line-height: 1.6; }
          .button { display: inline-block; background: #3182ce; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>&#10060; Invalid Confirmation Link</h1>
          <p>The confirmation link is invalid or missing. Please check your email for the correct link or try subscribing again.</p>
          <a href="/" class="button">Return to Homepage</a>
        </div>
      </body>
      </html>
    `,
      {
        status: 400,
        headers: { 'Content-Type': 'text/html' },
      }
    )
  }

  try {
    const confirmedSubscription = await confirmNewsletterSubscription(token)

    if (!confirmedSubscription) {
      return new NextResponse(
        `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Confirmation Link Expired</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; text-align: center; padding: 40px; background: #f8fafc; }
            .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #4a5568; line-height: 1.6; }
            .button { display: inline-block; background: #3182ce; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>&#8986; Confirmation Link Expired</h1>
            <p>This confirmation link has expired or is invalid. Confirmation links are valid for 24 hours after subscription.</p>
            <p>Please subscribe again to receive a new confirmation email.</p>
            <a href="/" class="button">Return to Homepage</a>
          </div>
        </body>
        </html>
      `,
        {
          status: 410,
          headers: { 'Content-Type': 'text/html' },
        }
      )
    }

    // Send welcome email
    const { html: welcomeHtml, text: welcomeText } = generateWelcomeEmail(
      confirmedSubscription.email
    )

    // Don't wait for welcome email to send - fire and forget
    sendEmail({
      to: confirmedSubscription.email,
      subject: 'Welcome to our newsletter!',
      html: welcomeHtml,
      text: welcomeText,
    }).catch((error) => {
      console.error('Failed to send welcome email:', error)
    })

    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Subscription Confirmed!</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; text-align: center; padding: 40px; background: #f8fafc; }
          .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          h1 { color: #38a169; margin-bottom: 20px; }
          p { color: #4a5568; line-height: 1.6; }
          .button { display: inline-block; background: #3182ce; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 20px; }
          .email { background: #edf2f7; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>&#127881; Subscription Confirmed!</h1>
          <p>Thank you for confirming your email address!</p>
          <div class="email">${confirmedSubscription.email}</div>
          <p>You're now subscribed to our newsletter and will receive our latest updates, articles, and insights.</p>
          <p>We've sent you a welcome email with more information.</p>
          <a href="/" class="button">Return to Homepage</a>
        </div>
      </body>
      </html>
    `,
      {
        headers: { 'Content-Type': 'text/html' },
      }
    )
  } catch (error) {
    console.error('Newsletter confirmation error:', error)

    return new NextResponse(
      `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Confirmation Error</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; text-align: center; padding: 40px; background: #f8fafc; }
          .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          h1 { color: #e53e3e; margin-bottom: 20px; }
          p { color: #4a5568; line-height: 1.6; }
          .button { display: inline-block; background: #3182ce; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>&#10060; Confirmation Error</h1>
          <p>There was an error confirming your subscription. This might be a temporary issue.</p>
          <p>Please try again or contact support if the problem persists.</p>
          <a href="/" class="button">Return to Homepage</a>
        </div>
      </body>
      </html>
    `,
      {
        status: 500,
        headers: { 'Content-Type': 'text/html' },
      }
    )
  }
}
