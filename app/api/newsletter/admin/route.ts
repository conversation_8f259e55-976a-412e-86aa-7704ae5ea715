import { NextRequest, NextResponse } from 'next/server'
import {
  getNewsletterSubscribers,
  updateNewsletterSubscriberStatus,
  deleteNewsletterSubscriber,
} from '../../../../lib/supabase'

export async function GET() {
  try {
    const startTime = Date.now()
    const subscribers = await getNewsletterSubscribers()
    const duration = Date.now() - startTime

    console.log(`Newsletter subscribers fetched in ${duration}ms`)
    return NextResponse.json({
      success: true,
      data: subscribers,
      meta: { count: subscribers.length, duration },
    })
  } catch (error) {
    console.error('Error fetching newsletter subscribers:', error)

    // More specific error handling
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const isTimeout = errorMessage.includes('timeout') || errorMessage.includes('AbortError')

    return NextResponse.json(
      {
        success: false,
        error: isTimeout
          ? 'Database connection timeout. Please try again.'
          : 'Failed to fetch newsletter subscribers',
        details: errorMessage,
      },
      { status: isTimeout ? 408 : 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, status, bulk, ids, action } = body

    if (bulk && ids && Array.isArray(ids)) {
      // Handle bulk operations
      try {
        if (action === 'delete') {
          // Bulk delete
          await Promise.all(ids.map((id) => deleteNewsletterSubscriber(id)))
        } else if (status) {
          // Bulk status update
          await Promise.all(ids.map((id) => updateNewsletterSubscriberStatus(id, status)))
        }

        return NextResponse.json({ success: true, message: `Bulk ${action || 'update'} completed` })
      } catch (error) {
        console.error('Error in bulk operation:', error)
        return NextResponse.json(
          { success: false, error: `Failed to perform bulk ${action || 'update'}` },
          { status: 500 }
        )
      }
    } else if (id && status) {
      // Handle single status update
      try {
        await updateNewsletterSubscriberStatus(id, status)
        return NextResponse.json({ success: true, message: 'Subscriber status updated' })
      } catch (error) {
        console.error('Error updating subscriber status:', error)
        return NextResponse.json(
          { success: false, error: 'Failed to update subscriber status' },
          { status: 500 }
        )
      }
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid request parameters' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error processing PATCH request:', error)
    return NextResponse.json({ success: false, error: 'Invalid request' }, { status: 400 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { id } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Subscriber ID is required' },
        { status: 400 }
      )
    }

    await deleteNewsletterSubscriber(id)
    return NextResponse.json({ success: true, message: 'Subscriber deleted' })
  } catch (error) {
    console.error('Error deleting subscriber:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete subscriber' },
      { status: 500 }
    )
  }
}
