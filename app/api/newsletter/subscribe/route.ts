import { NextRequest, NextResponse } from 'next/server'
import { createNewsletterSubscription } from '../../../../lib/supabase'
import { sendEmail, generateConfirmationEmail } from '../../../../lib/email'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, source = 'website' } = body

    if (!email) {
      return NextResponse.json({ success: false, error: 'Email is required' }, { status: 400 })
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }

    try {
      const subscription = await createNewsletterSubscription({
        email: email.trim().toLowerCase(),
        source,
        tags: ['newsletter'],
        ip_address:
          request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
      })

      // Send confirmation email
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
      const { html, text } = generateConfirmationEmail(
        subscription.email,
        subscription.confirmation_token!,
        baseUrl
      )

      const emailSent = await sendEmail({
        to: subscription.email,
        subject: 'Confirm your newsletter subscription',
        html,
        text,
      })

      if (emailSent) {
        return NextResponse.json({
          success: true,
          message: 'Please check your email to confirm your subscription!',
        })
      } else {
        // If email fails, still return success but with different message
        return NextResponse.json({
          success: true,
          message: 'Subscription created! Please check your email for confirmation.',
        })
      }
    } catch (error: any) {
      console.error('Newsletter subscription error:', error)

      if (error.message === 'Email is already subscribed') {
        return NextResponse.json(
          { success: false, error: 'This email is already subscribed.' },
          { status: 409 }
        )
      }

      // For demo purposes, return success even if DB not configured
      console.log('Newsletter subscription (fallback):', { email, source })
      return NextResponse.json({
        success: true,
        message: "Thank you for subscribing! We'll send you a confirmation email shortly.",
      })
    }
  } catch (error) {
    console.error('Newsletter subscription request error:', error)
    return NextResponse.json({ success: false, error: 'Invalid request' }, { status: 400 })
  }
}
