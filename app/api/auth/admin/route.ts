import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import crypto from 'crypto'

// In production, use a proper JWT library or session management system
const ADMIN_PASSWORD =
  process.env.ADMIN_PASSWORD || (process.env.NODE_ENV === 'development' ? 'admin123' : undefined)
const SESSION_SECRET =
  process.env.SESSION_SECRET ||
  (process.env.NODE_ENV === 'development' ? 'dev-secret-key-change-in-production' : undefined)

// Ensure we have default values for Docker environments
if (!ADMIN_PASSWORD) {
  console.warn('⚠️ ADMIN_PASSWORD not provided in environment. Using default for development only.')
}
if (!SESSION_SECRET) {
  console.warn('⚠️ SESSION_SECRET not provided in environment. Using default for development only.')
}

// Development warning
if (process.env.NODE_ENV === 'development' && !process.env.ADMIN_PASSWORD) {
  console.warn(
    '⚠️  Using default admin password for development. Set ADMIN_PASSWORD in .env.local for security.'
  )
  console.warn('🔑 Default admin password: admin123')
}

// Session configuration
const SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours
const REFRESH_THRESHOLD = 2 * 60 * 60 * 1000 // Refresh when 2 hours or less remaining
const MIN_REFRESH_INTERVAL = 5 * 60 * 1000 // Minimum 5 minutes between auto-refreshes

interface SessionData {
  isAdmin: boolean
  createdAt: number
  expiresAt: number
  lastRefreshed?: number
}

function generateSessionToken(sessionData: SessionData): string {
  if (!SESSION_SECRET) {
    console.warn('SESSION_SECRET not provided, using fallback')
    // Use a fallback secret for development or Docker environments
    const fallbackSecret = 'docker-dev-fallback-secret-key-not-for-production-use'
    const payload = JSON.stringify(sessionData)
    return crypto
      .createHmac('sha256', fallbackSecret)
      .update(payload)
      .digest('hex') + '.' + Buffer.from(payload).toString('base64')
  }

  // Standard approach with proper secret
  const payload = JSON.stringify(sessionData)
  const signature = crypto
    .createHmac('sha256', SESSION_SECRET)
    .update(payload)
    .digest('hex')
  
  // Simple concatenation with a dot separator for reliable parsing
  return signature + '.' + Buffer.from(payload).toString('base64')
}

function verifySessionToken(token: string): SessionData | null {
  // Safety check for malformed tokens
  if (!token || !token.includes('.')) {
    console.error('Invalid token format (no separator)')
    return null
  }

  try {
    // Split the token into signature and payload
    const [signature, encodedPayload] = token.split('.')
    
    if (!signature || !encodedPayload) {
      console.error('Invalid token format (missing parts)')
      return null
    }
    
    // Decode the payload
    let payloadStr: string
    try {
      payloadStr = Buffer.from(encodedPayload, 'base64').toString()
    } catch (e) {
      console.error('Base64 decode error:', e)
      return null
    }
    
    // Verify signature
    const secret = SESSION_SECRET || 'docker-dev-fallback-secret-key-not-for-production-use'
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payloadStr)
      .digest('hex')
    
    if (signature !== expectedSignature) {
      console.error('Invalid signature')
      return null
    }
    
    // Parse the payload
    let sessionData: SessionData
    try {
      sessionData = JSON.parse(payloadStr)
    } catch (e) {
      console.error('JSON parse error:', e)
      return null
    }

    // Check if session is expired
    if (Date.now() > sessionData.expiresAt) {
      console.error('Session expired')
      return null
    }

    return sessionData
  } catch (err) {
    console.error('Token verification error:', err)
    return null
  }
}

function shouldRefreshSession(sessionData: SessionData): boolean {
  const now = Date.now()
  const timeUntilExpiry = sessionData.expiresAt - now
  const timeSinceLastRefresh = sessionData.lastRefreshed
    ? now - sessionData.lastRefreshed
    : Infinity

  // Refresh if:
  // 1. Less than REFRESH_THRESHOLD time remaining AND
  // 2. At least MIN_REFRESH_INTERVAL has passed since last refresh
  return timeUntilExpiry <= REFRESH_THRESHOLD && timeSinceLastRefresh >= MIN_REFRESH_INTERVAL
}

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json()

    if (!ADMIN_PASSWORD) {
      return NextResponse.json(
        {
          error: 'Admin authentication not configured',
          details: 'Set ADMIN_PASSWORD in your environment variables',
        },
        { status: 500 }
      )
    }

    if (!password) {
      return NextResponse.json({ error: 'Password is required' }, { status: 400 })
    }

    if (password !== ADMIN_PASSWORD) {
      return NextResponse.json({ error: 'Invalid password' }, { status: 401 })
    }

    // Create session
    const now = Date.now()
    const sessionData: SessionData = {
      isAdmin: true,
      createdAt: now,
      expiresAt: now + SESSION_DURATION,
      lastRefreshed: now,
    }

    const sessionToken = generateSessionToken(sessionData)

    // Set HTTP-only cookie
    const cookieStore = await cookies()
    cookieStore.set('admin-session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: SESSION_DURATION / 1000, // Convert to seconds
      path: '/',
    })

    return NextResponse.json({
      success: true,
      expiresAt: sessionData.expiresAt,
      sessionDuration: SESSION_DURATION,
      refreshThreshold: REFRESH_THRESHOLD,
    })
  } catch (error) {
    console.error('Admin auth error:', error)
    return NextResponse.json({ error: 'Authentication failed' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('admin-session')?.value

    if (!sessionToken) {
      return NextResponse.json({ authenticated: false })
    }

    const sessionData = verifySessionToken(sessionToken)

    if (!sessionData || !sessionData.isAdmin) {
      // Clear invalid cookie
      cookieStore.delete('admin-session')
      return NextResponse.json({ authenticated: false })
    }

    // Check if session should be refreshed
    if (shouldRefreshSession(sessionData)) {
      const now = Date.now()
      const newSessionData: SessionData = {
        ...sessionData,
        expiresAt: now + SESSION_DURATION,
        lastRefreshed: now,
      }

      const newSessionToken = generateSessionToken(newSessionData)
      cookieStore.set('admin-session', newSessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: SESSION_DURATION / 1000,
        path: '/',
      })

      return NextResponse.json({
        authenticated: true,
        expiresAt: newSessionData.expiresAt,
        refreshed: true,
        sessionDuration: SESSION_DURATION,
        refreshThreshold: REFRESH_THRESHOLD,
      })
    }

    return NextResponse.json({
      authenticated: true,
      expiresAt: sessionData.expiresAt,
      refreshed: false,
      sessionDuration: SESSION_DURATION,
      refreshThreshold: REFRESH_THRESHOLD,
    })
  } catch (error) {
    console.error('Session verification error:', error)
    return NextResponse.json({ authenticated: false })
  }
}

// New endpoint for manual session refresh
export async function PUT(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('admin-session')?.value

    if (!sessionToken) {
      return NextResponse.json(
        {
          error: 'No active session',
        },
        { status: 401 }
      )
    }

    const sessionData = verifySessionToken(sessionToken)

    if (!sessionData || !sessionData.isAdmin) {
      cookieStore.delete('admin-session')
      return NextResponse.json(
        {
          error: 'Invalid session',
        },
        { status: 401 }
      )
    }

    // Force refresh the session
    const now = Date.now()
    const newSessionData: SessionData = {
      ...sessionData,
      expiresAt: now + SESSION_DURATION,
      lastRefreshed: now,
    }

    const newSessionToken = generateSessionToken(newSessionData)
    cookieStore.set('admin-session', newSessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: SESSION_DURATION / 1000,
      path: '/',
    })

    return NextResponse.json({
      success: true,
      expiresAt: newSessionData.expiresAt,
      refreshed: true,
      sessionDuration: SESSION_DURATION,
    })
  } catch (error) {
    console.error('Manual refresh error:', error)
    return NextResponse.json(
      {
        error: 'Refresh failed',
      },
      { status: 500 }
    )
  }
}

export async function DELETE() {
  try {
    const cookieStore = await cookies()
    cookieStore.delete('admin-session')

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json({ error: 'Logout failed' }, { status: 500 })
  }
}
