import { NextResponse } from 'next/server'
import {
  createComment,
  getCommentsBySlug,
  getAllComments,
  updateCommentStatus,
  deleteComment,
} from '@/lib/supabase'

// GET /api/comments - Get comments for a post or all comments (admin)
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')
    const isAdmin = searchParams.get('admin') === 'true'

    if (isAdmin) {
      // Admin: get all comments regardless of status
      const comments = await getAllComments()
      return NextResponse.json(comments)
    }

    if (!slug) {
      return NextResponse.json({ error: 'Post slug is required' }, { status: 400 })
    }

    // Public: get approved comments for specific post
    const comments = await getCommentsBySlug(slug)
    return NextResponse.json(comments)
  } catch (error) {
    console.error('Error fetching comments:', error)
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 })
  }
}

// POST /api/comments - Create a new comment
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { post_slug, author_name, author_email, content } = body

    if (!post_slug || !author_name || !author_email || !content) {
      return NextResponse.json({ error: 'All fields are required' }, { status: 400 })
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(author_email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }

    // Create comment with pending status
    const comment = await createComment({
      post_slug,
      author_name: author_name.trim(),
      author_email: author_email.trim().toLowerCase(),
      content: content.trim(),
    })

    return NextResponse.json(
      {
        message: 'Comment submitted successfully and is pending approval',
        comment,
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating comment:', error)
    return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 })
  }
}

// PUT /api/comments - Update comment status (admin only)
export async function PUT(request: Request) {
  try {
    // Simple admin check - verify session exists
    const cookieHeader = request.headers.get('cookie') || ''
    const hasAdminSession = cookieHeader.includes('admin-session=')

    if (!hasAdminSession) {
      console.log('No admin session found in cookies:', cookieHeader)
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 401 })
    }

    const body = await request.json()
    const { id, status } = body

    if (!id || !status) {
      return NextResponse.json({ error: 'Comment ID and status are required' }, { status: 400 })
    }

    if (!['approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be approved or rejected' },
        { status: 400 }
      )
    }

    const updatedComment = await updateCommentStatus(id, status)
    return NextResponse.json(updatedComment)
  } catch (error) {
    console.error('Error updating comment:', error)
    return NextResponse.json({ error: 'Failed to update comment' }, { status: 500 })
  }
}

// DELETE /api/comments - Delete comment (admin only)
export async function DELETE(request: Request) {
  try {
    // Simple admin check - verify session exists
    const cookieHeader = request.headers.get('cookie') || ''
    const hasAdminSession = cookieHeader.includes('admin-session=')

    if (!hasAdminSession) {
      console.log('No admin session found in cookies:', cookieHeader)
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Comment ID is required' }, { status: 400 })
    }

    await deleteComment(id)
    return NextResponse.json({ message: 'Comment deleted successfully' })
  } catch (error) {
    console.error('Error deleting comment:', error)
    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 })
  }
}
