import { NextResponse } from 'next/server'
import { toggleLike, getPostInteraction, supabaseAdmin, supabase } from '@/lib/supabase'

// Helper function to get client IP
function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')

  if (forwarded) {
    const ip = forwarded.split(',')[0].trim()
    // Skip localhost IPs in development
    if (ip === '::1' || ip === '127.0.0.1') {
      return `dev-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
    return ip
  }

  const ip = realIP || remoteAddr || 'unknown'
  // Skip localhost IPs in development
  if (ip === '::1' || ip === '127.0.0.1') {
    return `dev-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  return ip
}

// GET /api/likes - Get post interaction data (likes and comments count)
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json({ error: 'Post slug is required' }, { status: 400 })
    }

    const userIP = getClientIP(request)
    const interaction = await getPostInteraction(slug, userIP)

    return NextResponse.json(interaction)
  } catch (error) {
    console.error('Error fetching post interaction:', error)
    return NextResponse.json({ error: 'Failed to fetch post interaction' }, { status: 500 })
  }
}

// POST /api/likes - Toggle like for a post
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { post_slug } = body

    if (!post_slug) {
      return NextResponse.json({ error: 'Post slug is required' }, { status: 400 })
    }

    const userIP = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || undefined

    const result = await toggleLike(post_slug, userIP, userAgent)

    return NextResponse.json({
      message: result.liked ? 'Post liked' : 'Post unliked',
      liked: result.liked,
      count: result.count,
    })
  } catch (error) {
    console.error('Error toggling like:', error)
    return NextResponse.json({ error: 'Failed to toggle like' }, { status: 500 })
  }
}

// DELETE /api/likes - Clear all likes for a post (for testing)
export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const slug = searchParams.get('slug')

    if (!slug) {
      return NextResponse.json({ error: 'Post slug is required' }, { status: 400 })
    }

    const client = supabaseAdmin || supabase
    if (!client) {
      throw new Error('Supabase is not configured')
    }

    const { error } = await client.from('likes').delete().eq('post_slug', slug)

    if (error) {
      throw error
    }

    return NextResponse.json({ message: 'All likes cleared for post' })
  } catch (error) {
    console.error('Error clearing likes:', error)
    return NextResponse.json({ error: 'Failed to clear likes' }, { status: 500 })
  }
}
