import { genPageMetadata } from 'app/seo'
import PageHeader from '@/components/PageHeader'
import { 
  User, 
  Mail, 
  ArrowRight,
  Github,
  Linkedin,
  Twitter,
  MapPin,
  Coffee,
  Code,
  Monitor
} from 'lucide-react'

export const metadata = genPageMetadata({ title: 'About' })

export default function Page() {
  return (
    <>
      {/* Clean Hero Section */}
      <PageHeader
        title="About This Blog"
        description="Technology insights, reviews, and tutorials to help you make informed decisions in the digital world."
        icon={<User className="w-4 h-4" />}
        badge="About"
      />

      {/* Main Content */}
      <div className="bg-slate-50 dark:bg-slate-800/50 py-16 lg:py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-16">
            {/* Profile Section */}
            <div className="text-center">
              <div className="relative inline-block mb-8">
                <img
                  src="/static/images/avatar.png"
                  alt="Profile"
                  className="w-32 h-32 rounded-2xl mx-auto shadow-lg border-4 border-white dark:border-slate-700"
                />
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>
              
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                Tech Enthusiast & Blogger
              </h2>
              <p className="text-slate-600 dark:text-slate-400 text-lg">
                Exploring technology to make it accessible for everyone
              </p>
            </div>

            {/* About Content */}
            <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 lg:p-12 shadow-sm border border-slate-200 dark:border-slate-800">
              <div className="prose prose-lg max-w-none dark:prose-invert">
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
                  Welcome to My Tech Journey
                </h3>
                
                <p className="text-slate-600 dark:text-slate-400 leading-relaxed mb-6">
                  I'm passionate about exploring the latest in technology and sharing practical insights that matter. 
                  Whether it's reviewing the newest gadgets, diving into software tutorials, or analyzing tech trends, 
                  my goal is to help you navigate the ever-evolving digital landscape.
                </p>

                <p className="text-slate-600 dark:text-slate-400 leading-relaxed mb-6">
                  This blog focuses on honest reviews, comprehensive guides, and thoughtful analysis of technology 
                  that impacts our daily lives. From productivity tools to cutting-edge innovations, I aim to provide 
                  content that's both informative and actionable.
                </p>

                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                  When I'm not writing or testing new tech, you'll find me exploring the intersection of technology 
                  and creativity, always looking for ways to make complex topics more accessible and engaging.
                </p>
              </div>
            </div>

            {/* What I Do */}
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 shadow-sm border border-slate-200 dark:border-slate-800">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center">
                    <Monitor className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white">Tech Reviews</h3>
                </div>
                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                  In-depth reviews of the latest gadgets, software, and digital services to help you make informed purchasing decisions.
                </p>
              </div>

              <div className="bg-white dark:bg-slate-900 rounded-2xl p-8 shadow-sm border border-slate-200 dark:border-slate-800">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center">
                    <Code className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white">Tutorials & Guides</h3>
                </div>
                <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                  Step-by-step tutorials and practical guides to help you get the most out of your technology and tools.
                </p>
              </div>
            </div>

            {/* Connect Section */}
            <div className="text-center bg-white dark:bg-slate-900 rounded-2xl p-8 lg:p-12 shadow-sm border border-slate-200 dark:border-slate-800">
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
                Let's Connect
              </h3>
              
              <p className="text-slate-600 dark:text-slate-400 mb-8 max-w-2xl mx-auto">
                I love connecting with fellow tech enthusiasts. Feel free to reach out if you have questions, 
                suggestions, or just want to chat about the latest in technology.
              </p>

              {/* Social Links */}
              <div className="flex justify-center gap-4 mb-8">
                <a href="#" className="w-12 h-12 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded-xl flex items-center justify-center text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors">
                  <Github className="w-5 h-5" />
                </a>
                <a href="#" className="w-12 h-12 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded-xl flex items-center justify-center text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors">
                  <Linkedin className="w-5 h-5" />
                </a>
                <a href="#" className="w-12 h-12 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded-xl flex items-center justify-center text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors">
                  <Twitter className="w-5 h-5" />
                </a>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/contact"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-xl transition-colors"
                >
                  <Mail className="w-5 h-5" />
                  Get in Touch
                </a>
                <a
                  href="/blog"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-transparent border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 font-medium rounded-xl transition-colors"
                >
                  Read Articles
                  <ArrowRight className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
