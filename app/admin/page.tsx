'use client'

import { AdminAuth } from 'components/AdminAuth'
import { AdminDashboard } from 'components/AdminDashboard'
import { useAdminSession } from '../../components/hooks/useAdminSession'

export default function AdminPage() {
  const {
    isAuthenticated,
    isLoading,
    logout,
  } = useAdminSession({
    showRefreshToasts: true, // Show toast notifications when session is auto-refreshed
    checkInterval: 2 * 60 * 1000, // Check every 2 minutes
  })

  // Show loading state while checking session
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center relative overflow-hidden">
        {/* Beautiful gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-950 dark:via-gray-900/50 dark:to-primary-900/20">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-warm opacity-5">
            <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="admin-loading-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
                  <rect x="15" y="15" width="8" height="8" rx="2" fill="currentColor" opacity="0.4"/>
                  <circle cx="45" cy="15" r="3" fill="currentColor" opacity="0.3"/>
                  <polygon points="15,45 23,45 19,37" fill="currentColor" opacity="0.2"/>
                  <rect x="35" y="35" width="16" height="6" rx="3" fill="currentColor" opacity="0.3"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#admin-loading-pattern)" />
            </svg>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-28 h-28 bg-blue-200/20 dark:bg-blue-800/20 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-200/20 dark:bg-purple-800/20 rounded-full blur-xl animate-float-delayed"></div>
          <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-indigo-200/20 dark:bg-indigo-800/20 rounded-full blur-xl animate-float" style={{ animationDelay: '1.5s' }}></div>
        </div>

        {/* Loading content */}
        <div className="relative z-10 text-center">
          <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl rounded-3xl border border-white/40 dark:border-gray-700/40 shadow-2xl">
            <div className="p-8">
              {/* Animated logo/icon */}
              <div className="mx-auto mb-6 h-20 w-20 relative">
                <div className="h-full w-full bg-gradient-primary rounded-3xl flex items-center justify-center shadow-2xl animate-pulse">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>

              {/* Loading spinner */}
              <div className="mx-auto mb-4 h-8 w-8 relative">
                <div className="absolute inset-0 border-4 border-gray-200 dark:border-gray-600 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-primary-600 dark:border-t-primary-400 rounded-full animate-spin"></div>
              </div>

              <h3 className="text-xl font-semibold mb-2">
                <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                  Accessing Admin Dashboard
                </span>
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Verifying your authentication...
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleLogin = async (success: boolean) => {
    if (success) {
    // Force a session check after authentication
    window.location.reload()
    }
  }

  if (!isAuthenticated) {
    return <AdminAuth onLogin={handleLogin} />
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Beautiful gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-gray-50/50 to-primary-50/30 dark:from-gray-950 dark:via-gray-900/50 dark:to-primary-900/20">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="admin-bg-pattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
                <rect x="20" y="20" width="10" height="10" rx="3" fill="currentColor" opacity="0.4"/>
                <circle cx="60" cy="20" r="4" fill="currentColor" opacity="0.3"/>
                <polygon points="20,60 30,60 25,48" fill="currentColor" opacity="0.2"/>
                <rect x="45" y="45" width="20" height="8" rx="4" fill="currentColor" opacity="0.3"/>
                <circle cx="25" cy="45" r="2" fill="currentColor" opacity="0.5"/>
                <rect x="55" y="65" width="6" height="6" rx="1" fill="currentColor" opacity="0.4"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#admin-bg-pattern)" />
          </svg>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-20 w-32 h-32 bg-blue-200/10 dark:bg-blue-800/10 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-32 right-20 w-40 h-40 bg-purple-200/10 dark:bg-purple-800/10 rounded-full blur-2xl animate-float-delayed"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-indigo-200/10 dark:bg-indigo-800/10 rounded-full blur-2xl animate-float" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-28 h-28 bg-pink-200/10 dark:bg-pink-800/10 rounded-full blur-2xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-2/3 left-20 w-20 h-20 bg-green-200/10 dark:bg-green-800/10 rounded-full blur-2xl animate-float-delayed" style={{ animationDelay: '0.5s' }}></div>
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 container mx-auto px-4 py-8 max-w-7xl">
        <AdminDashboard onLogout={logout} />
      </div>
    </div>
  )
}
