'use client'

import Link from '@/components/Link'
import Tag from '@/components/Tag'
import NewsletterForm from '@/components/NewsletterForm'
import siteMetadata from '@/data/siteMetadata'
import { formatDate } from 'pliny/utils/formatDate'
import { 
  Sparkles, 
  ArrowRight, 
  Calendar, 
  Clock, 
  Users, 
  Star, 
  TrendingUp, 
  BookOpen, 
  Award, 
  CheckCircle, 
  Mail,
  Eye,
  Heart,
  Zap,
  Target,
  Globe,
  Code,
  Lightbulb,
  Coffee,
  Rocket,
  Shield,
  Download,
  BarChart3,
  MessageSquare,
  Bookmark,
  Share2,
  Play,
  ChevronRight,
  Layers,
  Cpu,
  Database,
  Palette
} from 'lucide-react'

const MAX_DISPLAY = 5

interface Post {
  slug: string
  date: string
  title: string
  summary?: string
  description?: string
  tags?: string[]
}

interface HomeProps {
  posts: Post[]
}

export default function Home({ posts }: HomeProps) {
  return (
    <>
      {/* TechCrunch-style Hero */}
      <div className="border-b border-gray-200 dark:border-gray-800 pb-8 mb-8">
        <div className="mb-6">
          <h1 className="text-4xl font-display font-bold text-gray-900 dark:text-white mb-4">
            Technology News & Insights
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl">
            Breaking news, expert analysis, and in-depth coverage of the latest technology trends, startups, and industry developments.
          </p>
        </div>
      </div>

      {/* TechCrunch-style Article List */}
      {posts.length > 0 && (
        <section className="mb-12">
          {/* Featured Article */}
          {posts.length > 0 && (
            <div className="mb-8 pb-8 border-b border-gray-200 dark:border-gray-800">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <div className="mb-3">
                    <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300 rounded">
                      FEATURED
                    </span>
                  </div>
                  <h2 className="text-3xl font-display font-bold text-gray-900 dark:text-white mb-4 leading-tight">
                    <Link
                      href={`/blog/${posts[0].slug}`}
                      className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                    >
                      {posts[0].title}
                    </Link>
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 text-lg leading-relaxed">
                    {posts[0].summary || posts[0].description}
                  </p>
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <time>{formatDate(posts[0].date, siteMetadata.locale)}</time>
                    <span className="mx-2">•</span>
                    <span>5 min read</span>
                  </div>
                </div>
                <div className="bg-gray-100 dark:bg-gray-800 rounded-lg aspect-video flex items-center justify-center">
                  <span className="text-gray-400 dark:text-gray-500">Featured Image</span>
                </div>
              </div>
            </div>
          )}

          {/* Article List */}
          <div className="space-y-6">
            <h3 className="text-xl font-display font-bold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-800 pb-2">
              Latest News
            </h3>

            {posts.slice(1, 6).map((post) => {
              const { slug, date, title, summary, description, tags } = post
              return (
                <article
                  key={slug}
                  className="group border-b border-gray-100 dark:border-gray-800 pb-6 last:border-b-0"
                >
                  <div className="grid md:grid-cols-4 gap-4">
                    <div className="md:col-span-3">
                      <div className="mb-2">
                        {tags && tags.length > 0 && (
                          <Link
                            href={`/tags/${tags[0]}`}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors duration-200"
                          >
                            {tags[0].toUpperCase()}
                          </Link>
                        )}
                      </div>

                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 leading-tight">
                        <Link
                          href={`/blog/${slug}`}
                          className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                        >
                          {title}
                        </Link>
                      </h3>

                      <p className="text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                        {summary || description}
                      </p>

                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <time>{formatDate(date, siteMetadata.locale)}</time>
                        <span className="mx-2">•</span>
                        <span>3 min read</span>
                      </div>
                    </div>

                    <div className="md:col-span-1">
                      <div className="bg-gray-100 dark:bg-gray-800 rounded aspect-video flex items-center justify-center">
                        <span className="text-gray-400 dark:text-gray-500 text-sm">Image</span>
                      </div>
                    </div>
                  </div>
                </article>
              )
            })}
          </div>

          {/* Load More */}
          <div className="mt-8 text-center">
            <Link
              href="/blog"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
            >
              <span>View all articles</span>
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
        </section>
      )}

      {/* Sidebar-style Categories */}
      <section className="mb-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Main Content Area */}
          <div className="md:col-span-3">
            <div className="border-t border-gray-200 dark:border-gray-800 pt-8">
              <h3 className="text-xl font-display font-bold text-gray-900 dark:text-white mb-6">
                More Stories
              </h3>

              <div className="space-y-4">
                {posts.slice(6, 10).map((post) => {
                  const { slug, date, title, tags } = post
                  return (
                    <article
                      key={slug}
                      className="group border-b border-gray-100 dark:border-gray-800 pb-4 last:border-b-0"
                    >
                      <div className="flex items-start gap-4">
                        <div className="flex-1">
                          <div className="mb-1">
                            {tags && tags.length > 0 && (
                              <span className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase">
                                {tags[0]}
                              </span>
                            )}
                          </div>

                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 leading-tight">
                            <Link
                              href={`/blog/${slug}`}
                              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                            >
                              {title}
                            </Link>
                          </h4>

                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            <time>{formatDate(date, siteMetadata.locale)}</time>
                          </div>
                        </div>

                        <div className="w-20 h-16 bg-gray-100 dark:bg-gray-800 rounded flex-shrink-0"></div>
                      </div>
                    </article>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="md:col-span-1">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-display font-bold text-gray-900 dark:text-white mb-4">
                Popular Topics
              </h3>

              <div className="space-y-3">
                {['AI & Machine Learning', 'Startups', 'Cybersecurity', 'Cloud Computing', 'Mobile Tech'].map((topic) => (
                  <Link
                    key={topic}
                    href="#"
                    className="block text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                  >
                    {topic}
                  </Link>
                ))}
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
                  Newsletter
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  Get the latest tech news delivered to your inbox.
                </p>
                <Link
                  href="/newsletter"
                  className="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                >
                  Subscribe
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
