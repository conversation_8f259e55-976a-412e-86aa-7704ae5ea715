'use client'

import Link from '@/components/Link'
import Tag from '@/components/Tag'
import NewsletterForm from '@/components/NewsletterForm'
import siteMetadata from '@/data/siteMetadata'
import { formatDate } from 'pliny/utils/formatDate'
import { 
  Sparkles, 
  ArrowRight, 
  Calendar, 
  Clock, 
  Users, 
  Star, 
  TrendingUp, 
  BookOpen, 
  Award, 
  CheckCircle, 
  Mail,
  Eye,
  Heart,
  Zap,
  Target,
  Globe,
  Code,
  Lightbulb,
  Coffee,
  Rocket,
  Shield,
  Download,
  BarChart3,
  MessageSquare,
  Bookmark,
  Share2,
  Play,
  ChevronRight,
  Layers,
  Cpu,
  Database,
  Palette
} from 'lucide-react'

const MAX_DISPLAY = 5

interface Post {
  slug: string
  date: string
  title: string
  summary?: string
  description?: string
  tags?: string[]
}

interface HomeProps {
  posts: Post[]
}

export default function Home({ posts }: HomeProps) {
  return (
    <>
      {/* Wired-style Hero */}
      <div className="mb-12">
        <div className="text-center py-8">
          <h1 className="text-5xl md:text-6xl font-display font-black text-gray-900 dark:text-white mb-6 tracking-tight">
            TECHNOLOGY
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
            The latest in technology, science, culture and business
          </p>
        </div>
      </div>

      {/* Wired-style Article Grid */}
      {posts.length > 0 && (
        <section className="mb-16">
          {/* Today's Picks Header */}
          <div className="mb-8">
            <h2 className="text-2xl font-display font-bold text-gray-900 dark:text-white mb-2">
              Today's Picks
            </h2>
            <div className="w-12 h-0.5 bg-gray-900 dark:bg-white"></div>
          </div>

          {/* Featured Article - Large */}
          {posts.length > 0 && (
            <div className="mb-12">
              <article className="group">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="bg-gray-200 dark:bg-gray-800 aspect-[16/10] rounded-lg flex items-center justify-center">
                    <span className="text-gray-500 dark:text-gray-400">Featured Image</span>
                  </div>
                  <div>
                    <div className="mb-3">
                      <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider">
                        Featured Story
                      </span>
                    </div>
                    <h3 className="text-3xl font-display font-bold text-gray-900 dark:text-white mb-4 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                      <Link href={`/blog/${posts[0].slug}`}>
                        {posts[0].title}
                      </Link>
                    </h3>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                      {posts[0].summary || posts[0].description}
                    </p>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-medium">By WIRED Staff</span>
                      <span className="mx-2">•</span>
                      <time>{formatDate(posts[0].date, siteMetadata.locale)}</time>
                    </div>
                  </div>
                </div>
              </article>
            </div>
          )}

          {/* Article Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {posts.slice(1, 7).map((post) => {
              const { slug, date, title, summary, description, tags } = post
              return (
                <article key={slug} className="group">
                  <div className="bg-gray-200 dark:bg-gray-800 aspect-[16/10] rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-gray-500 dark:text-gray-400 text-sm">Image</span>
                  </div>

                  <div className="mb-2">
                    {tags && tags.length > 0 && (
                      <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider">
                        {tags[0]}
                      </span>
                    )}
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                    <Link href={`/blog/${slug}`}>
                      {title}
                    </Link>
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300 mb-3 line-clamp-3 text-sm leading-relaxed">
                    {summary || description}
                  </p>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    <span className="font-medium">WIRED Staff</span>
                    <span className="mx-2">•</span>
                    <time>{formatDate(date, siteMetadata.locale)}</time>
                  </div>
                </article>
              )
            })}
          </div>

          {/* Most Recent Section */}
          <div className="border-t border-gray-200 dark:border-gray-800 pt-8">
            <h3 className="text-xl font-display font-bold text-gray-900 dark:text-white mb-6">
              Most Recent
            </h3>

            <div className="space-y-6">
              {posts.slice(7, 12).map((post) => {
                const { slug, date, title, tags } = post
                return (
                  <article key={slug} className="group border-b border-gray-100 dark:border-gray-800 pb-6 last:border-b-0">
                    <div className="grid md:grid-cols-4 gap-4">
                      <div className="md:col-span-3">
                        <div className="mb-2">
                          {tags && tags.length > 0 && (
                            <span className="text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider">
                              {tags[0]}
                            </span>
                          )}
                        </div>

                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                          <Link href={`/blog/${slug}`}>
                            {title}
                          </Link>
                        </h4>

                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          <span className="font-medium">WIRED Staff</span>
                          <span className="mx-2">•</span>
                          <time>{formatDate(date, siteMetadata.locale)}</time>
                        </div>
                      </div>

                      <div className="md:col-span-1">
                        <div className="bg-gray-200 dark:bg-gray-800 aspect-[4/3] rounded flex items-center justify-center">
                          <span className="text-gray-500 dark:text-gray-400 text-xs">Image</span>
                        </div>
                      </div>
                    </div>
                  </article>
                )
              })}
            </div>
          </div>
        </section>
      )}


    </>
  )
}
