'use client'

import Link from '@/components/Link'
import Tag from '@/components/Tag'
import NewsletterForm from '@/components/NewsletterForm'
import siteMetadata from '@/data/siteMetadata'
import { formatDate } from 'pliny/utils/formatDate'
import { 
  Sparkles, 
  ArrowRight, 
  Calendar, 
  Clock, 
  Users, 
  Star, 
  TrendingUp, 
  BookOpen, 
  Award, 
  CheckCircle, 
  Mail,
  Eye,
  Heart,
  Zap,
  Target,
  Globe,
  Code,
  Lightbulb,
  Coffee,
  Rocket,
  Shield,
  Download,
  BarChart3,
  MessageSquare,
  Bookmark,
  Share2,
  Play,
  ChevronRight,
  Layers,
  Cpu,
  Database,
  Palette
} from 'lucide-react'

const MAX_DISPLAY = 5

interface Post {
  slug: string
  date: string
  title: string
  summary?: string
  description?: string
  tags?: string[]
}

interface HomeProps {
  posts: Post[]
}

export default function Home({ posts }: HomeProps) {
  return (
    <>
      {/* Premium Hero Section with Advanced Animations */}
      <div className="relative mb-4 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
          {/* Floating Elements */}
          <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-to-r from-pink-400/20 to-red-400/20 rounded-full blur-lg animate-float-delayed"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-2xl animate-pulse"></div>
          
          {/* Enhanced Background Pattern */}
          <div className="absolute inset-0 opacity-30">
            <svg
              width="60"
              height="60"
              viewBox="0 0 60 60"
              xmlns="http://www.w3.org/2000/svg"
              className="absolute inset-0 h-full w-full"
            >
              <defs>
                <pattern
                  id="premium-hero-pattern"
                  x="0"
                  y="0"
                  width="60"
                  height="60"
                  patternUnits="userSpaceOnUse"
                >
                  <circle cx="30" cy="30" r="1.5" fill="#6366f1" fillOpacity="0.15" />
                  <circle cx="15" cy="15" r="1" fill="#8b5cf6" fillOpacity="0.1" />
                  <circle cx="45" cy="45" r="1" fill="#ec4899" fillOpacity="0.1" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#premium-hero-pattern)" />
            </svg>
          </div>
        </div>

        <div className="relative px-4 py-4 text-center sm:px-6 sm:py-6 lg:px-8">
          <div className="mx-auto max-w-6xl">
            {/* Premium Badge */}
            <div className="mb-8 flex justify-center animate-fade-in-up">
              <div className="inline-flex items-center rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 px-6 py-3 text-sm font-semibold text-indigo-700 dark:text-indigo-300 ring-1 ring-indigo-600/20 dark:ring-indigo-400/20">
                <Sparkles className="mr-2 h-4 w-4 animate-pulse" />
                <span>Expert Insights & Professional Reviews</span>
                <Star className="ml-2 h-4 w-4 text-yellow-500" />
              </div>
            </div>

            {/* Enhanced Headline with Animations */}
            <h1 className="mb-4 text-3xl font-extrabold tracking-tight text-slate-900 sm:text-4xl md:text-5xl lg:text-6xl dark:text-white animate-fade-in-up">
                              <span className="block mb-2 relative">
                <span className="bg-gradient-to-r from-slate-900 via-indigo-600 to-slate-900 dark:from-white dark:via-indigo-400 dark:to-white bg-clip-text text-transparent bg-size-200 bg-pos-0 hover:bg-pos-100 transition-all duration-1000">
                  Discover. Learn.
                </span>
              </span>
              <span className="block bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x">
                Succeed.
              </span>
            </h1>

            {/* Enhanced Value Proposition */}
            <p className="mx-auto mb-6 max-w-4xl text-lg leading-relaxed text-slate-600 dark:text-slate-300 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              Join thousands of professionals who trust our expert insights, in-depth reviews, 
              and actionable guides to stay ahead in their field. Get the knowledge you need to make informed decisions.
            </p>

            {/* Enhanced CTA Section */}
            <div className="flex flex-col items-center justify-center gap-6 sm:flex-row animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              <Link
                href="/blog"
                className="group relative inline-flex items-center rounded-2xl bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-600 px-10 py-5 text-lg font-bold text-white shadow-2xl transition-all duration-500 hover:scale-110 hover:shadow-indigo-500/25 bg-size-200 hover:bg-pos-100"
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-400 to-purple-400 opacity-0 transition-opacity duration-500 group-hover:opacity-20"></div>
                <BookOpen className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                <span>Start Reading</span>
                <ArrowRight className="ml-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-2" />
              </Link>

              <Link
                href="#featured"
                className="group inline-flex items-center rounded-2xl border-2 border-slate-300 bg-white/80 backdrop-blur-sm px-10 py-5 text-lg font-bold text-slate-900 shadow-xl transition-all duration-500 hover:scale-110 hover:border-indigo-300 hover:shadow-2xl dark:border-slate-600 dark:bg-slate-800/80 dark:text-white dark:hover:border-indigo-500"
              >
                <Target className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                <span>Explore Topics</span>
                <Sparkles className="ml-3 h-5 w-5 text-indigo-600 dark:text-indigo-400 transition-transform duration-300 group-hover:rotate-12" />
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="mt-8 flex flex-wrap items-center justify-center gap-4 text-sm text-slate-500 dark:text-slate-400 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-green-500" />
                <span className="font-medium">Expert Reviewed</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-blue-500" />
                <span className="font-medium">Fact Checked</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-purple-500" />
                <span className="font-medium">Industry Trusted</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-indigo-500" />
                <span className="font-medium">Community Approved</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Featured Content Section */}
      {posts.length > 0 && (
        <section id="featured" className="mb-8 animate-fade-in-up">
                      <div className="mb-8 text-center">
            <div className="mb-4 flex justify-center">
              <div className="inline-flex items-center rounded-full bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 px-4 py-2 text-sm font-semibold text-yellow-700 dark:text-yellow-300">
                <Star className="mr-2 h-4 w-4 text-yellow-500" />
                <span>Featured Content</span>
              </div>
            </div>
            <h2 className="mb-4 text-2xl font-bold text-slate-900 dark:text-white sm:text-3xl">
              <span className="bg-gradient-to-r from-slate-900 via-indigo-600 to-slate-900 dark:from-white dark:via-indigo-400 dark:to-white bg-clip-text text-transparent">
                Latest Insights
              </span>
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-slate-600 dark:text-slate-300">
              Hand-picked articles that deliver the most value, insights, and actionable advice from industry experts
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {posts.slice(0, 3).map((post, index) => {
              const { slug, date, title, summary, description, tags } = post
              return (
                <article
                  key={slug}
                  className="group relative overflow-hidden rounded-3xl border border-slate-200/50 bg-white/80 backdrop-blur-sm shadow-xl transition-all duration-500 hover:scale-[1.05] hover:shadow-2xl hover:shadow-indigo-500/10 dark:border-slate-700/50 dark:bg-slate-800/80"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-indigo-50/20 dark:to-indigo-900/20 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
                  
                  <div className="relative p-8">
                    {/* Enhanced Meta Info */}
                    <div className="mb-6 flex items-center justify-between">
                      <div className="flex items-center space-x-3 text-sm">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4 text-indigo-500" />
                          <time className="font-medium text-slate-500 dark:text-slate-400">
                            {formatDate(date, siteMetadata.locale)}
                          </time>
                        </div>
                        <span className="text-slate-300 dark:text-slate-600">•</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4 text-slate-400" />
                          <span className="text-slate-400">3 min read</span>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Title */}
                    <h3 className="mb-4 text-xl font-bold leading-tight">
                      <Link
                        href={`/blog/${slug}`}
                        className="text-slate-900 transition-all duration-300 hover:text-indigo-600 dark:text-white dark:hover:text-indigo-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400"
                      >
                        {title}
                      </Link>
                    </h3>

                    {/* Enhanced Summary */}
                    <p className="mb-6 line-clamp-3 text-slate-600 dark:text-slate-300 leading-relaxed">
                      {summary || description}
                    </p>

                    {/* Enhanced Tags */}
                    {tags && tags.length > 0 && (
                      <div className="mb-6 flex flex-wrap gap-2">
                        {tags.slice(0, 2).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center rounded-full bg-indigo-100 dark:bg-indigo-900/30 px-3 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-300 transition-colors duration-300 hover:bg-indigo-200 dark:hover:bg-indigo-800/40"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Enhanced Read More */}
                    <Link
                      href={`/blog/${slug}`}
                      className="group/link inline-flex items-center font-bold text-indigo-600 transition-all duration-300 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 cursor-pointer z-10 relative"
                    >
                      <span>Read Full Article</span>
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover/link:translate-x-2" />
                    </Link>
                  </div>

                  {/* Hover Effect Border */}
                  <div className="absolute inset-0 rounded-3xl ring-1 ring-indigo-500/0 transition-all duration-500 group-hover:ring-indigo-500/20"></div>
                </article>
              )
            })}
          </div>

          {/* View All Posts Button */}
          <div className="mt-12 text-center">
            <Link
              href="/blog"
              className="group inline-flex items-center rounded-2xl bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 px-8 py-4 text-lg font-bold text-slate-900 dark:text-white shadow-lg transition-all duration-500 hover:scale-105 hover:shadow-xl"
            >
              <BookOpen className="mr-3 h-5 w-5" />
              <span>View All Articles</span>
              <ArrowRight className="ml-3 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </div>
        </section>
      )}

      {/* Value Proposition Section */}
      <section className="mb-24 animate-fade-in-up">
        <div className="text-center mb-16">
          <h2 className="mb-6 text-4xl font-bold text-slate-900 dark:text-white sm:text-5xl">
            <span className="bg-gradient-to-r from-slate-900 via-purple-600 to-slate-900 dark:from-white dark:via-purple-400 dark:to-white bg-clip-text text-transparent">
              Why Choose Us?
            </span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-slate-600 dark:text-slate-300">
            We're committed to delivering high-quality, actionable content that helps you succeed
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {/* Expert Content */}
          <div className="group relative overflow-hidden rounded-3xl border border-slate-200/50 bg-white/80 backdrop-blur-sm p-8 shadow-xl transition-all duration-500 hover:scale-105 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/80">
            <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
              <Award className="h-8 w-8" />
            </div>
            <h3 className="mb-4 text-2xl font-bold text-slate-900 dark:text-white">Expert Reviews</h3>
            <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
              In-depth analysis and honest reviews from industry professionals with years of experience.
            </p>
          </div>

          {/* Actionable Insights */}
          <div className="group relative overflow-hidden rounded-3xl border border-slate-200/50 bg-white/80 backdrop-blur-sm p-8 shadow-xl transition-all duration-500 hover:scale-105 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/80">
            <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-lg">
              <Lightbulb className="h-8 w-8" />
            </div>
            <h3 className="mb-4 text-2xl font-bold text-slate-900 dark:text-white">Actionable Tips</h3>
            <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
              Practical advice and step-by-step guides that you can implement immediately for real results.
            </p>
          </div>

          {/* Community Driven */}
          <div className="group relative overflow-hidden rounded-3xl border border-slate-200/50 bg-white/80 backdrop-blur-sm p-8 shadow-xl transition-all duration-500 hover:scale-105 hover:shadow-2xl dark:border-slate-700/50 dark:bg-slate-800/80">
            <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500 to-teal-600 text-white shadow-lg">
              <Users className="h-8 w-8" />
            </div>
            <h3 className="mb-4 text-2xl font-bold text-slate-900 dark:text-white">Community Driven</h3>
            <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
              Content shaped by our community's needs and feedback, ensuring relevance and value.
            </p>
          </div>
        </div>
      </section>

      {/* Enhanced Call to Action Section */}
      <section className="mb-24 text-center animate-fade-in-up">
        <div className="mx-auto max-w-4xl rounded-3xl border border-slate-200/50 bg-gradient-to-br from-white via-slate-50 to-indigo-50 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 dark:border-slate-700/50 p-12 shadow-2xl relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              xmlns="http://www.w3.org/2000/svg"
              className="absolute inset-0 h-full w-full"
            >
              <defs>
                <pattern
                  id="cta-pattern"
                  x="0"
                  y="0"
                  width="40"
                  height="40"
                  patternUnits="userSpaceOnUse"
                >
                  <circle cx="20" cy="20" r="2" fill="currentColor" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#cta-pattern)" />
            </svg>
          </div>

          <div className="relative">
            <div className="mb-8">
              <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 text-white shadow-xl animate-bounce-gentle">
                <Rocket className="h-10 w-10" />
              </div>
              <h2 className="mb-4 text-4xl font-bold text-slate-900 dark:text-white sm:text-5xl">
                <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Ready to Level Up?
                </span>
              </h2>
              <p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed mb-8">
                Join professionals who rely on our expert insights to make better decisions, 
                advance their careers, and achieve their goals.
              </p>
            </div>
            
            <div className="flex flex-col items-center justify-center gap-6 sm:flex-row">
              <Link
                href="/blog"
                className="group relative inline-flex items-center rounded-2xl bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 px-10 py-5 text-lg font-bold text-white shadow-2xl transition-all duration-500 hover:scale-110 hover:shadow-indigo-500/25 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 opacity-0 transition-opacity duration-500 group-hover:opacity-30"></div>
                <div className="relative flex items-center">
                  <BookOpen className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                  <span>Start Reading Now</span>
                  <ArrowRight className="ml-3 h-6 w-6 transition-transform duration-300 group-hover:translate-x-2" />
                </div>
              </Link>
              
              <Link
                href="/about"
                className="group inline-flex items-center rounded-2xl border-2 border-slate-300 bg-white/80 backdrop-blur-sm px-10 py-5 text-lg font-bold text-slate-900 shadow-lg transition-all duration-500 hover:scale-110 hover:border-indigo-300 hover:shadow-xl dark:border-slate-600 dark:bg-slate-800/80 dark:text-white dark:hover:border-indigo-500"
              >
                <Coffee className="mr-3 h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                <span>Learn About Us</span>
                <Sparkles className="ml-3 h-5 w-5 text-indigo-600 dark:text-indigo-400 transition-transform duration-300 group-hover:rotate-12" />
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="mt-12 text-center">
              <p className="mb-4 text-sm text-slate-500 dark:text-slate-400 font-medium uppercase tracking-wider">
                Trusted by professionals at
              </p>
              <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
                <div className="flex items-center space-x-2 text-slate-400 dark:text-slate-500">
                  <Globe className="h-5 w-5" />
                  <span className="font-medium">Global Companies</span>
                </div>
                <div className="flex items-center space-x-2 text-slate-400 dark:text-slate-500">
                  <Code className="h-5 w-5" />
                  <span className="font-medium">Tech Startups</span>
                </div>
                <div className="flex items-center space-x-2 text-slate-400 dark:text-slate-500">
                  <Cpu className="h-5 w-5" />
                  <span className="font-medium">Fortune 500</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
