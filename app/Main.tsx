'use client'

import Link from '@/components/Link'
import Tag from '@/components/Tag'
import NewsletterForm from '@/components/NewsletterForm'
import siteMetadata from '@/data/siteMetadata'
import { formatDate } from 'pliny/utils/formatDate'
import { 
  Sparkles, 
  ArrowRight, 
  Calendar, 
  Clock, 
  Users, 
  Star, 
  TrendingUp, 
  BookOpen, 
  Award, 
  CheckCircle, 
  Mail,
  Eye,
  Heart,
  Zap,
  Target,
  Globe,
  Code,
  Lightbulb,
  Coffee,
  Rocket,
  Shield,
  Download,
  BarChart3,
  MessageSquare,
  Bookmark,
  Share2,
  Play,
  ChevronRight,
  Layers,
  Cpu,
  Database,
  Palette
} from 'lucide-react'

const MAX_DISPLAY = 5

interface Post {
  slug: string
  date: string
  title: string
  summary?: string
  description?: string
  tags?: string[]
}

interface HomeProps {
  posts: Post[]
}

export default function Home({ posts }: HomeProps) {
  return (
    <>
      {/* Clean Professional Hero Section */}
      <div className="relative mb-16 py-16 px-4 text-center">
        <div className="mx-auto max-w-4xl">
          {/* Simple Badge */}
          <div className="mb-8 flex justify-center">
            <div className="inline-flex items-center rounded-full bg-blue-50 dark:bg-blue-900/20 px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 border border-blue-200/50 dark:border-blue-800/50">
              <Star className="mr-2 h-4 w-4" />
              <span>Expert Tech Insights</span>
            </div>
          </div>

          {/* Clean Headline */}
          <h1 className="mb-6 text-4xl font-display font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl dark:text-white">
            <span className="block">Stay Ahead in</span>
            <span className="block text-blue-600 dark:text-blue-400">Technology</span>
          </h1>

          {/* Clear Value Proposition */}
          <p className="mx-auto mb-8 max-w-2xl text-xl leading-relaxed text-gray-600 dark:text-gray-300">
            Get expert insights, in-depth reviews, and actionable guides to make informed technology decisions.
          </p>

          {/* Simple CTA */}
          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Link
              href="/blog"
              className="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-700 transition-colors duration-200"
            >
              <BookOpen className="mr-2 h-5 w-5" />
              <span>Read Articles</span>
            </Link>

            <Link
              href="#featured"
              className="inline-flex items-center rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-6 py-3 text-base font-semibold text-gray-900 dark:text-white shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <span>Browse Topics</span>
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 flex flex-wrap items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Expert Reviewed</span>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-blue-500" />
              <span>Fact Checked</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-purple-500" />
              <span>Trusted by Professionals</span>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Articles Section */}
      {posts.length > 0 && (
        <section id="featured" className="mb-16">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-3xl font-display font-bold text-gray-900 dark:text-white">
              Latest Articles
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
              Expert insights and in-depth analysis on the latest technology trends
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {posts.slice(0, 3).map((post) => {
              const { slug, date, title, summary, description, tags } = post
              return (
                <article
                  key={slug}
                  className="group overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <div className="p-6">
                    {/* Meta Info */}
                    <div className="mb-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <time className="font-medium">
                        {formatDate(date, siteMetadata.locale)}
                      </time>
                      <span className="mx-2">•</span>
                      <span>3 min read</span>
                    </div>

                    {/* Title */}
                    <h3 className="mb-3 text-xl font-semibold leading-tight">
                      <Link
                        href={`/blog/${slug}`}
                        className="text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                      >
                        {title}
                      </Link>
                    </h3>

                    {/* Summary */}
                    <p className="mb-4 line-clamp-3 text-gray-600 dark:text-gray-300 leading-relaxed">
                      {summary || description}
                    </p>

                    {/* Tags */}
                    {tags && tags.length > 0 && (
                      <div className="mb-4 flex flex-wrap gap-2">
                        {tags.slice(0, 2).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center rounded-md bg-blue-50 dark:bg-blue-900/20 px-2 py-1 text-xs font-medium text-blue-700 dark:text-blue-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Read More */}
                    <Link
                      href={`/blog/${slug}`}
                      className="inline-flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
                    >
                      <span>Read article</span>
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                  </div>
                </article>
              )
            })}
          </div>

          {/* View All Posts Button */}
          <div className="mt-12 text-center">
            <Link
              href="/blog"
              className="inline-flex items-center rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-6 py-3 text-base font-semibold text-gray-900 dark:text-white shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
            >
              <span>View all articles</span>
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </section>
      )}

      {/* Why Choose Us Section */}
      <section className="mb-16">
        <div className="text-center mb-12">
          <h2 className="mb-4 text-3xl font-display font-bold text-gray-900 dark:text-white">
            Why Choose Our Content?
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300">
            We deliver high-quality, actionable content that helps technology professionals succeed
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-3">
          {/* Expert Content */}
          <div className="text-center p-6">
            <div className="mb-4 flex justify-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Award className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">Expert Reviews</h3>
            <p className="text-gray-600 dark:text-gray-300">
              In-depth analysis and honest reviews from industry professionals with years of experience.
            </p>
          </div>

          {/* Actionable Insights */}
          <div className="text-center p-6">
            <div className="mb-4 flex justify-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                <Lightbulb className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">Actionable Insights</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Practical advice and step-by-step guides that you can implement immediately for real results.
            </p>
          </div>

          {/* Community Driven */}
          <div className="text-center p-6">
            <div className="mb-4 flex justify-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">Community Driven</h3>
            <p className="text-gray-600 dark:text-gray-300">
              Content shaped by our community's needs and feedback, ensuring relevance and value.
            </p>
          </div>
        </div>
      </section>

      {/* Newsletter CTA Section */}
      <section className="mb-16 text-center">
        <div className="mx-auto max-w-2xl rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 p-8">
          <div className="mb-6">
            <h2 className="mb-4 text-2xl font-display font-bold text-gray-900 dark:text-white">
              Stay Updated
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Get the latest tech insights and expert analysis delivered to your inbox.
            </p>
          </div>

          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Link
              href="/blog"
              className="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-700 transition-colors duration-200"
            >
              <BookOpen className="mr-2 h-5 w-5" />
              <span>Start Reading</span>
            </Link>

            <Link
              href="/about"
              className="inline-flex items-center rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-6 py-3 text-base font-semibold text-gray-900 dark:text-white shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              <span>Learn More</span>
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}
